# BITS Pilani Digital Data Science Platform

A comprehensive educational environment for data science learning and teaching built with React, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Role-Based Access Control**: Separate interfaces for Students, Instructors, and Administrators
- **Interactive Jupyter Notebooks**: Hands-on coding environment for data science projects
- **Rubric-Based Grading System**: Consistent and transparent evaluation process
- **Project Management**: Create, distribute, and manage data science assignments
- **Real-time Communication**: Built-in messaging system between users
- **Comprehensive Analytics**: Track student progress and platform usage
- **Sandbox Environment**: Safe space for experimentation and learning

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS v4
- **Build Tool**: Vite
- **UI Components**: Radix UI, Shadcn/ui
- **Icons**: Lucide React
- **Notifications**: Sonner
- **Charts**: Recharts
- **Forms**: React Hook Form
- **Animations**: Framer Motion
- **Code Quality**: ESLint, TypeScript

## 🏗️ Project Structure

```
├── components/           # React components
│   ├── ui/              # Reusable UI components
│   ├── figma/           # Figma-specific components
│   └── *.tsx            # Feature components
├── styles/              # CSS and styling files
├── assets/              # Static assets
├── public/              # Public files
└── dist/                # Build output
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18.0.0 or higher
- npm 8.0.0 or higher

### Installation

1. Clone the repository:
```bash
git clone https://github.com/ottlin/bits_ds_projects_portal_fe.git
cd data-science-platform
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

4. Start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint errors
- `npm run type-check` - Run TypeScript type checking
- `npm run clean` - Clean build artifacts
- `npm run analyze` - Analyze bundle size

## 👥 User Roles

### Student
- Access courses and assignments
- Work in interactive notebooks
- Submit assignments and view grades
- Track academic progress
- Use sandbox environment for experimentation

### Instructor
- Create and manage courses
- Design data science projects
- Grade student submissions using rubrics
- Monitor student progress
- Communicate with students

### Administrator
- Manage users and system settings
- Access platform analytics
- Configure system parameters
- Monitor audit logs
- Oversee platform operations

## 🔐 Demo Credentials

For testing purposes, use these credentials:

**Student Account:**
- Email: `<EMAIL>`
- Password: `password`
- Role: Student

**Instructor Account:**
- Email: `<EMAIL>`
- Password: `password`
- Role: Instructor

**Administrator Account:**
- Email: `<EMAIL>`
- Password: `password`
- Role: Administrator

## 🎨 Design System

The platform follows BITS Pilani branding guidelines:

- **Primary Color**: `#2B2B88` (BITS Blue)
- **Secondary Color**: `#B78A2D` (BITS Gold)
- **Accent Color**: `#CF2027` (BITS Red)
- **Typography**: System fonts with consistent spacing
- **Layout**: Responsive design with consistent spacing patterns

## 📱 Responsive Design

The platform is fully responsive and works on:
- Desktop computers (1024px and above)
- Tablets (768px - 1023px)
- Mobile devices (320px - 767px)

## 🔒 Security

- Input validation and sanitization
- Role-based access control
- Secure authentication flow
- XSS protection
- CSRF protection
- Content Security Policy headers

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your repository to Vercel
2. Set up environment variables
3. Deploy automatically on push to main branch

### Manual Build

```bash
npm run build
```

The built files will be in the `dist` directory.

## 📊 Performance

- Lighthouse score: 90+ across all metrics
- Bundle size optimization with code splitting
- Lazy loading for optimal performance
- Efficient state management
- Optimized images and assets

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature`
3. Commit your changes: `git commit -m 'Add some feature'`
4. Push to the branch: `git push origin feature/your-feature`
5. Submit a pull request

### Code Style

- Follow the existing code style
- Use TypeScript for type safety
- Write meaningful commit messages
- Add comments for complex logic
- Follow React best practices

## 🧪 Testing

Currently, the project uses manual testing. Future versions will include:
- Unit tests with Jest
- Integration tests with Testing Library
- End-to-end tests with Playwright
- Component testing with Storybook

## 📝 Documentation

- [Workflow Documentation](./WORKFLOW_DOCUMENTATION.md) - Detailed user workflows
- [Guidelines](./Guidelines.md) - Development guidelines

## 🐛 Known Issues

- Some features are in development/demo mode
- API integration is mocked for demonstration
- Real-time features use local state (not persistent)

## 📞 Support

For support, please contact:
- Email: <EMAIL>
- Documentation: [Workflow Guide](./WORKFLOW_DOCUMENTATION.md)

---

Made with ❤️ by OpenTurf Technologies