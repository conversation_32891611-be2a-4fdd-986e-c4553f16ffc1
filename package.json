{"name": "bits-pilani-data-science-platform", "version": "1.0.0", "type": "module", "description": "BITS Pilani Digital Data Science Platform - A comprehensive educational environment for data science learning and teaching", "keywords": ["education", "data-science", "react", "typescript", "vite", "tailwind"], "author": "BITS Pilani", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/bits-pilani/data-science-platform"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "build-dev": "vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0 --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules/.vite", "analyze": "npx vite-bundle-analyzer", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.86.0", "@tanstack/react-query-devtools": "^5.87.0", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "esbuild": "^0.25.9", "framer-motion": "^10.16.4", "input-otp": "^1.2.4", "lucide-react": "^0.263.1", "next-themes": "^0.2.1", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "^7.55.0", "react-query": "^3.39.3", "react-resizable-panels": "^0.0.55", "react-responsive-masonry": "^2.1.7", "react-router-dom": "^7.9.1", "react-slick": "^0.29.0", "react-textarea-autosize": "^8.5.3", "react-window": "^2.1.0", "recharts": "^2.10.1", "rollup": "^4.46.4", "slick-carousel": "^1.8.1", "sonner": "^2.0.3", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.7.0"}, "devDependencies": {"@babel/plugin-proposal-logical-assignment-operators": "^7.20.7", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-optional-chaining-assign": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@tailwindcss/vite": "^4.0.0-alpha.15", "@types/node": "^24.0.14", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/react-slick": "^0.23.10", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.5.6", "tailwindcss": "^3.4.3", "typescript": "^5.2.2", "vite": "^5.2.0", "vite-bundle-analyzer": "^0.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}