import { Routes, Route, Navigate } from "react-router-dom";
import useAuth from "./components/Context/AuthContext";
import useNavigation from "./components/Context/NavigationContext";
import StudentDashboard from "./pages/StudentDashboard";
import StudentsAllProjectsPage from "./pages/StudentsAllProjectsPage";
import InstructorDashboard from "./pages/InstructorDashboard";
import AdminDashboard from "./pages/AdminDashboard";
import ProjectDetailsPage from "./pages/ProjectDetailsPage";
import SubmissionsPage from "./pages/SubmissionsPage";
import CoursesPage from "./pages/CoursesPage";
import ViewCoursePage from "./pages/ViewCoursePage";
import ViewAllProjectsPage from "./pages/ViewAllProjectsPage";
import InstructorProjectDetailsPage from "./pages/InstructorProjectDetailsPage";
import ViewStudentPage from "./pages/ViewStudentPage";
import GradebookPage from "./pages/GradebookPage";
import { SubmissionNavigationProvider } from "./contexts/SubmissionNavigationContext";
import AdminProjectsPage from "./pages/AdminProjectsPage";
import AdminReportsPage from "./pages/AdminReportsPage";
import AdminSettingsPage from "./pages/AdminSettingsPage";
import AuditPage from "./pages/AuditPage";
import CommunicationPage from "./pages/CommunicationPage";
import SandboxEnvironmentPage from "./pages/SandboxEnvironmentPage";
import SandboxManagementPage from "./pages/SandboxManagementPage";
import UserManagementPage from "./pages/UserManagementPage";
import GradingPage from "./pages/GradingPage";
import EnhancedSubmissionDetailsPage from "./pages/EnhancedSubmissionDetailsPage";
import ProjectManagementPage from "./pages/ProjectManagementPage";
import CreateProjectPage from "./pages/CreateProjectPage";
import ModelTestingPage from "./pages/ModelTestingPage";
import NotebookPage from "./pages/NotebookPage";
import NotebooksPage from "./pages/NotebooksPage";
import CodeViewerPage from "./pages/CodeViewerPage";
import DatasetsPage from "./pages/DatasetsPage";
import RolesPermissionsListPage from "./pages/RolesPermissionsListPage";
import SandboxAnalyticsDashboard from "./pages/SandboxAnalyticsDashboard";
import TimeExtensionManagement from "./components/TimeExtensionManagement";
import paths from "./routes";
import studentRoutes from "./StudentRoutes";
import { getInstructorRoutes } from "./InstructorRoutes";
function DashboardRouter() {
  const { user } = useAuth();
  const { pageParams, goBack } = useNavigation();

  if (!user) return <Navigate to={paths.login} replace />;
console.log(user)
  return (
    <Routes>

      <Route path={paths.projectDetails} element={<ProjectDetailsPage />} />
      <Route path={paths.courses} element={<CoursesPage />} />
      <Route
        path={paths.viewCourse}
        element={<ViewCoursePage courseId={pageParams?.courseId} />}
      />
      <Route path={paths.viewAllProjects} element={<ViewAllProjectsPage />} />
      <Route
        path={paths.instructorProjectDetails}
        element={<InstructorProjectDetailsPage />}
      />
      <Route
        path={paths.viewStudent}
        element={<ViewStudentPage studentId={pageParams?.studentId} />}
      />
      <Route path={paths.gradebook} element={<GradebookPage />} />
      <Route
        path={paths.communication}
        element={<CommunicationPage defaultTab={pageParams?.defaultTab} />}
      />
      <Route
        path={paths.submissions}
        element={<SubmissionsPage assignmentId={pageParams?.assignmentId} />}
      />


{user.roles[0].name === "student" &&
  studentRoutes.map(({ path, element, name, index }) => {
    const routeProps: any = { element };
    if (path) routeProps.path = path;
    if (index) routeProps.index = index;

    return <Route key={name} {...routeProps} />;
  })
}
      {(user.roles[0].name === "instructor" || user.roles[0].name === "admin") && (
        <>
          <Route
            path={paths.timeExtensions}
            element={<TimeExtensionManagement userRole={user.roles[0].name} />}
          />
          <Route
            path={paths.studentNotebook}
            element={
              <SandboxEnvironmentPage
                mode="student-notebook"
                studentId={pageParams?.studentId}
                studentName={pageParams?.studentName}
                assignmentId={pageParams?.assignmentId}
                assignmentTitle={pageParams?.assignmentTitle}
                notebookUrl={pageParams?.notebookUrl}
              />
            }
          />
          <Route
            path={paths.modelTesting}
            element={
              <ModelTestingPage
                studentId={pageParams?.studentId}
                studentName={pageParams?.studentName}
                assignmentId={pageParams?.assignmentId}
                assignmentTitle={pageParams?.assignmentTitle}
                submissionId={pageParams?.submissionId}
                onBack={goBack}
              />
            }
          />
          <Route
            path={paths.viewStudentCode}
            element={
              <CodeViewerPage
                studentId={pageParams?.studentId}
                studentName={pageParams?.studentName}
                assignmentId={pageParams?.assignmentId}
                assignmentTitle={pageParams?.assignmentTitle}
                submissionId={pageParams?.submissionId}
              />
            }
          />
          <Route
            path={paths.submissionDetails}
            element={
              <SubmissionNavigationProvider>
                <EnhancedSubmissionDetailsPage />
              </SubmissionNavigationProvider>
            }
          />
          <Route path={paths.userManagement} element={<UserManagementPage />} />
          <Route
            path={paths.sandboxManagement}
            element={<SandboxManagementPage />}
          />
          <Route
            path={paths.gradingSandbox}
            element={<SandboxEnvironmentPage mode="grading" />}
          />
          <Route path={paths.grading} element={<GradingPage />} />
          <Route
            path={paths.createProject}
            element={<CreateProjectPage mode="create" />}
          />
          <Route
            path={paths.editProject}
            element={
              <CreateProjectPage
                mode="edit"
                projectId={pageParams?.projectId}
              />
            }
          />
          <Route
            path={paths.useTemplate}
            element={
              <CreateProjectPage
                mode="template"
                templateData={pageParams?.templateData}
              />
            }
          />
          <Route
            path={paths.manageProjects}
            element={<ProjectManagementPage />}
          />
          {user.role === "instructor" && (
            <Route index element={<InstructorDashboard />} />
          )}
        </>
      )}

      {/* Admin-only routes */}
      {user.roles[0].name === "admin" && (
        <>
          <Route
            path={paths.rolePermissions}
            element={<RolesPermissionsListPage />}
          />
          <Route
            path={paths.sandboxAnalytics}
            element={<SandboxAnalyticsDashboard />}
          />
          <Route path={paths.adminProjects} element={<AdminProjectsPage />} />
          <Route path={paths.adminReports} element={<AdminReportsPage />} />
          <Route path={paths.adminSettings} element={<AdminSettingsPage />} />
          <Route path={paths.adminAudit} element={<AuditPage />} />
          <Route index element={<AdminDashboard />} />
        </>
      )}

      {/* Sandbox (all roles) */}
      <Route
        path={paths.sandbox}
        element={<SandboxEnvironmentPage initialTab={pageParams?.initialTab} />}
      />

      {/* Catch-all */}
      <Route path={paths.notFound} element={<div className="mt-10 ml-4">Page not found</div>} />
    </Routes>
  );
}

export default DashboardRouter;
