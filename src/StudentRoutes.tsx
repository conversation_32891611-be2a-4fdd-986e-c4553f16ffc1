import paths from "./routes";
import { ReactElement } from "react";
import NotebookPage from "./pages/NotebookPage";
import NotebooksPage from "./pages/NotebooksPage";
import StudentsAllProjectsPage from "./pages/StudentsAllProjectsPage";
import DatasetsPage from "./pages/DatasetsPage";
import StudentDashboard from "./pages/StudentDashboard";

export interface AppRoute {
  path?: string;
  element: ReactElement;
  name: string;
  index?: boolean;
}

const studentRoutes: AppRoute[] = [
  // { path: paths.notebook, element: <NotebookPage />, name: "Notebook" },
  { path: paths.notebooks, element: <NotebooksPage />, name: "Notebooks" },
  { path: paths.allProjects, element: <StudentsAllProjectsPage />, name: "All Projects" },
  { path: paths.datasets, element: <DatasetsPage />, name: "Datasets" },
  { element: <StudentDashboard />, name: "Dashboard", index: true },
];

export default studentRoutes;
