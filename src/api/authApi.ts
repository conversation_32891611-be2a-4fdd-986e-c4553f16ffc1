import axios, { AxiosInstance } from "axios";
import { useQuery, UseQueryOptions, Query<PERSON>ey,useMutation } from "@tanstack/react-query";
import getBITSEndApiMap from "../apiMap";
import { tokenService } from "../services/TokenService";

// Attach auth similar to other APIs
function getAuthToken() {
  const t = tokenService.getToken();
  return t ? `Bearer ${t}` : "";
}

function attachAuth(instance: AxiosInstance) {
  instance.interceptors.request.use((config) => {
    const auth = getAuthToken();
    if (auth) {
      config.headers = config.headers ?? {};
      config.headers["Authorization"] = auth;
    }
    return config;
  });
  return instance;
}

const api = attachAuth(
  axios.create({
    headers: { "Content-Type": "application/json" },
    withCredentials: true,
    timeout: 60000,
  })
);

export interface LoginRequest {
  email: string;
  password: string;
}

export interface Permission {
  id: string;
  key: string;
  name: string;
  description: string;
  category: string;
  is_system_permission: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  RolePermission: {
    id: string;
    role_id: string;
    permission_id: string;
    granted_by: string;
    granted_at: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
  };
}

export interface UserRole {
  id: string;
  user_id: string;
  role_id: string;
  assigned_by: string | null;
  assigned_at: string;
  is_primary: boolean;
  context: {
    source: string;
  };
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface BackendRole {
  id: string;
  name: string; // e.g., "admin", "instructor", "student"
  lms_role_reference: string;
  description: string;
  is_system_role: boolean;
  priority: number;
  metadata: {
    scopes: string[];
  };
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  UserRole: UserRole;
  permissions: Permission[];
}

export interface BackendUser {
  id: string;
  name: string;
  email: string;
  profilePicture?: string;
  lastLogin?: string;
  roles: BackendRole[];
}

export interface LoginResponseData {
  accessToken: string;
  expiresIn: string;
  tokenType: string;
  user: BackendUser;
}

export interface LoginResponse {
  isSuccess: boolean;
  message: string;
  data: LoginResponseData;
  user:any
}

export async function loginUser(payload: LoginRequest): Promise<LoginResponse> {
  const url = getBITSEndApiMap("authLogin");
  const { data } = await api.post<LoginResponse>(url, payload);
  return data;
}

export function useLoginQuery(
  payload: LoginRequest,
  options?: Partial<UseQueryOptions<LoginResponse>>
) {

  return useQuery({
    queryKey: ["auth", "login", payload.email] as QueryKey,
    queryFn: () => loginUser(payload),
    enabled: false, // trigger manually with refetch on submit
    retry: 0,
    ...(options as any),
  });
}

export interface LogoutResponse {
  success: boolean;
  message: string;
}

export interface RefreshTokenRole {
  id: string;
  name: string;
  permissions: string[]; // Array of permission keys
}

export interface RefreshTokenUser {
  id: string;
  name: string;
  email: string;
  profilePicture?: string;
  lastLogin?: string;
  roles: RefreshTokenRole[];
}

export interface RefreshTokenResponse {
  success: boolean;
  accessToken: string;
  expiresIn: string;
  tokenType: string;
  user: RefreshTokenUser;
}

export async function refreshToken(): Promise<RefreshTokenResponse> {
  const url = getBITSEndApiMap("authRefresh");
  const { data } = await api.post<RefreshTokenResponse>(url);
  return data;
}

export function useRefreshTokenQuery(
  options?: Partial<UseQueryOptions<RefreshTokenResponse>>
) {
  return useQuery({
    queryKey: ["auth", "refresh"] as QueryKey,
    queryFn: refreshToken,
    enabled: false, // trigger manually
    retry: 0,
    ...(options as any),
  });
}

export async function logoutUser(): Promise<LogoutResponse> {
  const url = getBITSEndApiMap("authLogout");
  const { data } = await api.post<LogoutResponse>(url);
  return data;
}

// If needed, could expose a mutation wrapper; for now we'll call logoutUser in context

export interface AuthMeRole {
  id: string;
  name: string; // admin/instructor/student
  lms_role_reference: string;
  description: string;
  is_system_role: boolean;
  priority: number;
  metadata: {
    scopes: string[];
  };
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  UserRole: UserRole;
  permissions: Permission[];
}

export interface AuthMeUser {
  id: string;
  name: string;
  email: string;
  profilePicture?: string;
  lastLogin?: string;
  status?: string;
  preferences?: any;
  roles: AuthMeRole[];
}

export interface AuthMeResponse {
  success: boolean;
  user: AuthMeUser;
}

export async function getAuthMe(): Promise<AuthMeResponse> {
  const url = getBITSEndApiMap("authMe");
  const { data } = await api.get<AuthMeResponse>(url);
  return data;
}

export function useAuthMeQuery(
  options?: Partial<UseQueryOptions<AuthMeResponse>>
) {
  return useQuery({
    queryKey: ["auth", "me"] as QueryKey,
    queryFn: getAuthMe,
    enabled: true,
    retry: 1,
    staleTime: 10_000,
    ...(options as any),
  });
}
