import axios, { AxiosInstance } from "axios";
import {
  useQuery,
  useMutation,
  UseQueryOptions,
  QueryKey,
} from "@tanstack/react-query";
import getBITSEndApiMap from "../apiMap";
import { tokenService } from "../services/TokenService";

// Helper function to replace path params like :projectId, :kernelId, etc.
function buildUrl(template: string, params: Record<string, string | number>) {
  return template.replace(/:([a-zA-Z0-9_]+)/g, (_, key) =>
    String(params[key] ?? ":" + key)
  );
}

function getAuthToken() {
  const t = tokenService.getToken();
  return t ? `Bearer ${t}` : "";
}

function attachAuthInterceptor(instance: AxiosInstance) {
  instance.interceptors.request.use((config) => {
    // Always use the latest token before each request
    const auth = getAuthToken();
    if (auth) {
      config.headers = config.headers ?? {};
      config.headers["Authorization"] = auth;
    } else if (config.headers && "Authorization" in config.headers) {
      // Remove Authorization header if token is empty
      delete (config.headers as any)["Authorization"];
    }
    return config;
  });
  return instance;
}

// Axios instance for backend API (not Jupyter directly)
export const api: AxiosInstance = attachAuthInterceptor(
  axios.create({
    headers: {
      "Content-Type": "application/json",
    },
    withCredentials: true,
    timeout: 300000, // Increased timeout to 5 minutes for save operations and workspace creation
  })
);

// TypeScript Interfaces based on Postman collection responses

export interface CreateWorkspaceResponse {
  isSuccess: boolean;
  message: string;
  data: {
    folderPath: string;
    notebookPath: string;
    server: {
      name: string;
      full_name: string;
      last_activity: string;
      started: string;
      pending: string | null;
      ready: boolean;
      stopped: boolean;
      url: string;
      user_options: any;
      progress_url: string;
      full_url: string | null;
      full_progress_url: string | null;
      state: {
        object_id: string;
        object_name: string;
      };
    };
    session: {
      id: string;
      path: string;
      name: string;
      type: string;
      kernel: {
        id: string;
        name: string;
        last_activity: string;
        execution_state: string;
        connections: number;
      };
      notebook: {
        path: string;
        name: string;
      };
    };
    kernel: {
      id: string;
      name: string;
      last_activity: string;
      execution_state: string;
      connections: number;
    };
  };
}

export interface JupyterServerVersion {
  version: string;
}

export interface JupyterUser {
  name: string;
  display_name: string;
  color: string | null;
  initials: string;
  kind: string;
}

export interface JupyterServerStatus {
  connections: number;
  kernels: number;
  last_activity: string;
  started: string;
}

export interface JupyterFileContent {
  name: string;
  path: string;
  last_modified: string;
  created: string;
  content: any;
  format: string | null;
  mimetype: string | null;
  size: number | null;
  writable: boolean;
  type: "directory" | "file" | "notebook";
}

export interface GetContentsResponse {
  name: string;
  path: string;
  last_modified: string;
  created: string;
  content: JupyterFileContent[] | any; // array for directories, content for files
  format: string | null;
  mimetype: string | null;
  size: number | null;
  writable: boolean;
  type: "directory" | "file" | "notebook";
}

export interface JupyterKernel {
  id: string;
  name: string;
  last_activity: string;
  execution_state: string;
  connections: number;
}

export interface JupyterSession {
  id: string;
  path: string;
  name: string;
  type: string;
  kernel: JupyterKernel;
}

export interface JupyterCheckpoint {
  id: string;
  last_modified: string;
}

export interface ExecuteCellRequest {
  code: string;
  timeout?: number;
}

export interface ExecuteCellResponse {
  isSuccess: boolean;
  message: string;
  data: {
    kernel_id: string;
    execution_count: number;
    status: "ok" | "error";
    outputs: Array<{
      output_type: string;
      name?: string;
      text?: string;
      data?: any;
      metadata?: any;
    }>;
    error: string | null;
  };
}

export interface ExecuteNotebookRequest {
  notebook: {
    cells: Array<{
      cell_type: string;
      source: string[];
    }>;
  };
  timeout?: number;
  stopOnError?: boolean;
}

export interface ExecuteNotebookResponse {
  isSuccess: boolean;
  message: string;
  data: {
    kernel_id: string;
    total_cells: number;
    results: Array<{
      cell_index: number;
      execution_count: number;
      outputs: Array<{
        output_type: string;
        name?: string;
        text?: string;
        data?: any;
        metadata?: any;
      }>;
      status: "ok" | "error";
    }>;
  };
}

// Generic API response wrapper from backend
interface ApiResponse<T> {
  isSuccess: boolean;
  message: string;
  data: T;
}

// API Functions

// Workspace Management
export async function createWorkspace(
  projectId: string
): Promise<CreateWorkspaceResponse> {
  const url = buildUrl(getBITSEndApiMap("jupyterCreateWorkspace"), {
    projectId,
  });
  const { data } = await api.post<CreateWorkspaceResponse>(url);
  console.log("workspacedata", data);
  return data;
}

export async function getContents(
  projectId: string
): Promise<GetContentsResponse> {
  const url = buildUrl(getBITSEndApiMap("jupyterGetContents"), {
    projectId,
  });
  const { data } = await api.get<ApiResponse<GetContentsResponse>>(url);
  // Optional: keep a concise debug log
  // console.log("getContents data:", data.data);
  return data.data;
}

// Server Information
export async function getServerVersion(): Promise<JupyterServerVersion> {
  const url = getBITSEndApiMap("jupyterGetServerVersion");
  const { data } = await api.get<JupyterServerVersion>(url);
  return data;
}

export async function getCurrentUser(): Promise<JupyterUser> {
  const url = getBITSEndApiMap("jupyterGetCurrentUser");
  const { data } = await api.get<JupyterUser>(url);
  return data;
}

export async function getServerStatus(): Promise<JupyterServerStatus> {
  const url = getBITSEndApiMap("jupyterGetServerStatus");
  const { data } = await api.get<JupyterServerStatus>(url);
  return data;
}

// Session Management
export async function listSessions(): Promise<JupyterSession[]> {
  const url = getBITSEndApiMap("jupyterListSessions");
  const { data } = await api.get<JupyterSession[]>(url);
  return data;
}

export async function createSession(
  path: string,
  kernelName: string = "python3"
): Promise<JupyterSession> {
  const url = getBITSEndApiMap("jupyterCreateSession");
  const { data } = await api.post<JupyterSession>(url, {
    path,
    type: "notebook",
    kernel: { name: kernelName },
  });
  return data;
}

// Kernel Management
export async function listKernels(): Promise<JupyterKernel[]> {
  const url = getBITSEndApiMap("jupyterListKernels");
  const { data } = await api.get<JupyterKernel[]>(url);
  return data;
}

export async function startKernel(
  name: string = "python3",
  path?: string
): Promise<JupyterKernel> {
  const url = getBITSEndApiMap("jupyterStartKernel");
  const { data } = await api.post<JupyterKernel>(url, { name, path });
  return data;
}

export async function getKernel(kernelId: string): Promise<JupyterKernel> {
  const url = buildUrl(getBITSEndApiMap("jupyterGetKernel"), { kernelId });
  const { data } = await api.get<JupyterKernel>(url);
  return data;
}

export async function deleteKernel(kernelId: string): Promise<void> {
  const url = buildUrl(getBITSEndApiMap("jupyterDeleteKernel"), { kernelId });
  await api.delete(url);
}

export async function interruptKernel(kernelId: string): Promise<void> {
  const url = buildUrl(getBITSEndApiMap("jupyterInterruptKernel"), {
    kernelId,
  });
  await api.post(url);
  console.log("inside interruptKernel");
}

export async function restartKernel(kernelId: string): Promise<JupyterKernel> {
  const url = buildUrl(getBITSEndApiMap("jupyterRestartKernel"), { kernelId });
  const { data } = await api.post<JupyterKernel>(url);
  console.log("inside restartKernel", data);
  return data;
}

// Cell Execution
export async function executeCell(
  kernelId: string,
  request: ExecuteCellRequest
): Promise<ExecuteCellResponse> {
  const url = buildUrl(getBITSEndApiMap("jupyterExecuteCell"), { kernelId });
  const { data } = await api.post<ExecuteCellResponse>(url, request);
  return data;
}

export async function executeNotebook(
  kernelId: string,
  request: ExecuteNotebookRequest
): Promise<ExecuteNotebookResponse> {
  const url = buildUrl(getBITSEndApiMap("jupyterExecuteNotebook"), {
    kernelId,
  });
  const { data } = await api.post<ExecuteNotebookResponse>(url, request);
  console.log("inside executeNoteBook", data);
  return data;
}

// Content Management
export async function createContent(
  path: string,
  type: "file" | "directory",
  content?: any
): Promise<JupyterFileContent> {
  const url = buildUrl(getBITSEndApiMap("jupyterCreateContents"), { path });
  const { data } = await api.post<JupyterFileContent>(url, {
    type,
    content: content || null,
    format: type === "file" ? "text" : null,
  });
  return data;
}

export async function updateContent(
  path: string,
  content: any,
  type: string = "file",
  format: string = "text"
): Promise<JupyterFileContent> {
  const url = buildUrl(getBITSEndApiMap("jupyterUpdateContents"), { path });

  // Create a special axios instance with longer timeout for save operations
  // Use text/plain content type as required by Jupyter API
  const saveApi = attachAuthInterceptor(
    axios.create({
      headers: {
        "Content-Type": "text/plain",
      },
      withCredentials: true,
      timeout: 600000, // 10 minutes timeout for save operations
    })
  );

  console.log("Saving to URL:", url);
  console.log("Save payload:", { type, format, content });

  // Send the payload as JSON string since Content-Type is text/plain
  const payload = JSON.stringify({
    type,
    format,
    content,
  });

  const { data } = await saveApi.put<JupyterFileContent>(url, payload);
  return data;
}

export async function deleteContent(path: string): Promise<void> {
  const url = buildUrl(getBITSEndApiMap("jupyterDeleteContents"), { path });
  await api.delete(url);
}

export async function renameContent(
  oldPath: string,
  newPath: string
): Promise<JupyterFileContent> {
  // First get the current file content
  const currentContent = await getContents(oldPath);

  // Create the file at new path with same content
  const createUrl = buildUrl(getBITSEndApiMap("jupyterUpdateContents"), {
    path: newPath,
  });

  console.log("Renaming file:", { oldPath, newPath, createUrl });

  // Create special axios instance for rename operation
  const renameApi = attachAuthInterceptor(
    axios.create({
      headers: {
        "Content-Type": "text/plain",
      },
      withCredentials: true,
      timeout: 300000, // 5 minutes timeout
    })
  );

  // Create new file with same content
  const payload = JSON.stringify({
    type: currentContent.type,
    format: currentContent.format,
    content: currentContent.content,
  });

  const { data: newFile } = await renameApi.put<JupyterFileContent>(
    createUrl,
    payload
  );

  // Delete the old file
  await deleteContent(oldPath);

  return newFile;
}

// Checkpoint Management
export async function getCheckpoints(
  path: string
): Promise<JupyterCheckpoint[]> {
  const url = buildUrl(getBITSEndApiMap("jupyterGetCheckpoints"), { path });
  const { data } = await api.get<JupyterCheckpoint[]>(url);
  return data;
}

export async function createCheckpoint(
  path: string
): Promise<JupyterCheckpoint> {
  const url = buildUrl(getBITSEndApiMap("jupyterCreateCheckpoint"), { path });
  const { data } = await api.post<JupyterCheckpoint>(url);
  return data;
}

export async function restoreCheckpoint(
  path: string,
  checkpointId: string
): Promise<void> {
  const url = buildUrl(getBITSEndApiMap("jupyterRestoreCheckpoint"), {
    path,
    checkpointId,
  });
  await api.post(url);
}

export async function deleteCheckpoint(
  path: string,
  checkpointId: string
): Promise<void> {
  const url = buildUrl(getBITSEndApiMap("jupyterDeleteCheckpoint"), {
    path,
    checkpointId,
  });
  await api.delete(url);
}

// React Query Hooks

// Workspace Management Hooks
export function useCreateWorkspaceQuery(projectId: string, enabled = false) {
  return useQuery({
    queryKey: ["jupyter", "workspace", projectId] as QueryKey,
    queryFn: () => createWorkspace(projectId),
    enabled,
    staleTime: 0,
    retry: 1,
  } as UseQueryOptions<CreateWorkspaceResponse> as any);
}

export function useCreateWorkspaceMutation() {
  return useMutation({
    mutationFn: createWorkspace,
  });
}

export function useContentsQuery(projectId: string, enabled = true) {
  return useQuery({
    queryKey: ["jupyter", "contents", projectId] as QueryKey,
    queryFn: () => getContents(projectId),
    enabled,
    staleTime: 10_000,
  } as UseQueryOptions<GetContentsResponse> as any);
}

// Server Information Hooks
export function useServerVersionQuery(enabled = true) {
  return useQuery({
    queryKey: ["jupyter", "server", "version"] as QueryKey,
    queryFn: getServerVersion,
    enabled,
    staleTime: 300_000, // 5 minutes
  } as UseQueryOptions<JupyterServerVersion> as any);
}

export function useCurrentUserQuery(enabled = true) {
  return useQuery({
    queryKey: ["jupyter", "user", "current"] as QueryKey,
    queryFn: getCurrentUser,
    enabled,
    staleTime: 60_000, // 1 minute
  } as UseQueryOptions<JupyterUser> as any);
}

export function useServerStatusQuery(enabled = true) {
  return useQuery({
    queryKey: ["jupyter", "server", "status"] as QueryKey,
    queryFn: getServerStatus,
    enabled,
    staleTime: 30_000, // 30 seconds
  } as UseQueryOptions<JupyterServerStatus> as any);
}

// Session Management Hooks
export function useSessionsQuery(enabled = true) {
  return useQuery({
    queryKey: ["jupyter", "sessions"] as QueryKey,
    queryFn: listSessions,
    enabled,
    staleTime: 30_000,
  } as UseQueryOptions<JupyterSession[]> as any);
}

export function useCreateSessionMutation() {
  return useMutation({
    mutationFn: ({ path, kernelName }: { path: string; kernelName?: string }) =>
      createSession(path, kernelName),
  });
}

// Kernel Management Hooks
export function useKernelsQuery(enabled = true) {
  return useQuery({
    queryKey: ["jupyter", "kernels"] as QueryKey,
    queryFn: listKernels,
    enabled,
    staleTime: 30_000,
  } as UseQueryOptions<JupyterKernel[]> as any);
}

export function useKernelQuery(kernelId: string, enabled = true) {
  return useQuery({
    queryKey: ["jupyter", "kernel", kernelId] as QueryKey,
    queryFn: () => getKernel(kernelId),
    enabled: enabled && !!kernelId,
    staleTime: 10_000,
  } as UseQueryOptions<JupyterKernel> as any);
}

export function useStartKernelMutation() {
  return useMutation({
    mutationFn: ({ name, path }: { name?: string; path?: string }) =>
      startKernel(name, path),
  });
}

export function useDeleteKernelMutation() {
  return useMutation({
    mutationFn: deleteKernel,
  });
}

export function useInterruptKernelMutation() {
  return useMutation({
    mutationFn: interruptKernel,
  });
}

export function useRestartKernelMutation() {
  return useMutation({
    mutationFn: restartKernel,
  });
}

// Cell Execution Hooks
export function useExecuteCellMutation() {
  return useMutation({
    mutationFn: ({
      kernelId,
      request,
    }: {
      kernelId: string;
      request: ExecuteCellRequest;
    }) => executeCell(kernelId, request),
  });
}

export function useExecuteNotebookMutation() {
  return useMutation({
    mutationFn: ({
      kernelId,
      request,
    }: {
      kernelId: string;
      request: ExecuteNotebookRequest;
    }) => executeNotebook(kernelId, request),
  });
}

export function useRenameContentMutation() {
  return useMutation({
    mutationFn: ({ oldPath, newPath }: { oldPath: string; newPath: string }) =>
      renameContent(oldPath, newPath),
  });
}

// Content Management Hooks
export function useCreateContentMutation() {
  return useMutation({
    mutationFn: ({
      path,
      type,
      content,
    }: {
      path: string;
      type: "file" | "directory";
      content?: any;
    }) => createContent(path, type, content),
  });
}

export function useUpdateContentMutation() {
  return useMutation({
    mutationFn: ({
      path,
      content,
      type,
      format,
    }: {
      path: string;
      content: any;
      type?: string;
      format?: string;
    }) => updateContent(path, content, type, format),
  });
}

export function useDeleteContentMutation() {
  return useMutation({
    mutationFn: deleteContent,
  });
}

// Checkpoint Management Hooks
export function useCheckpointsQuery(path: string, enabled = true) {
  return useQuery({
    queryKey: ["jupyter", "checkpoints", path] as QueryKey,
    queryFn: () => getCheckpoints(path),
    enabled: enabled && !!path,
    staleTime: 60_000,
  } as UseQueryOptions<JupyterCheckpoint[]> as any);
}

export function useCreateCheckpointMutation() {
  return useMutation({
    mutationFn: createCheckpoint,
  });
}

export function useRestoreCheckpointMutation() {
  return useMutation({
    mutationFn: ({
      path,
      checkpointId,
    }: {
      path: string;
      checkpointId: string;
    }) => restoreCheckpoint(path, checkpointId),
  });
}

export function useDeleteCheckpointMutation() {
  return useMutation({
    mutationFn: ({
      path,
      checkpointId,
    }: {
      path: string;
      checkpointId: string;
    }) => deleteCheckpoint(path, checkpointId),
  });
}
