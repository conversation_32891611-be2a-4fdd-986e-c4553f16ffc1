import axios, { AxiosInstance } from "axios";
import {
  useQuery,
  useMutation,
  UseQueryOptions,
  QueryKey,
} from "@tanstack/react-query";
import getBITSEndApiMap from "../apiMap";
import { tokenService } from "../services/TokenService";

// Auth setup similar to other API files
function getAuthToken() {
  const t = tokenService.getToken();
  return t ? `Bearer ${t}` : "";
}

function attachAuth(instance: AxiosInstance) {
  instance.interceptors.request.use((config) => {
    const auth = getAuthToken();
    if (auth) {
      config.headers = config.headers ?? {};
      config.headers["Authorization"] = auth;
    }
    return config;
  });
  return instance;
}

const api = attachAuth(
  axios.create({
    headers: { "Content-Type": "application/json" },
    withCredentials: true,
    timeout: 120000,
  })
);

// TypeScript Interfaces

export interface LTIContext {
  id: string;
  title: string;
  label?: string;
  type?: string;
}

export interface LTIContextsResponse {
  success: boolean;
  message: string;
  data: LTIContext[];
}

export interface LTIContextMember {
  id: string;
  name: string;
  email: string;
  role: string;
  userId?: string;
}

export interface LTIContextMembersResponse {
  success: boolean;
  message: string;
  data: LTIContextMember[];
}

export interface LTIResourceLink {
  id: string;
  label: string;
  title?: string;
  scoreMaximum?: number;
  resourceLinkId?: string;
}

export interface LTIResourceLinksResponse {
  success: boolean;
  message: string;
  data: LTIResourceLink[];
}

export interface LinkProjectRequest {
  projectId: string;
  contextId: string;
  resourceLinkId: string;
  instructor_ids: string[];
  total_points: number;
}

export interface LinkProjectResponse {
  success: boolean;
  message: string;
  data?: any;
}

export interface GetContextMembersParams {
  contextId: string;
  platformId?: string | undefined;
  role?: string;
}

export interface GetUnassignedResourceLinksParams {
  contextId: string;
  platformId?: string | undefined;
}

// API Functions

export async function getLTIContexts(): Promise<LTIContext[]> {
  const url = getBITSEndApiMap("ltiGetContexts");
  const { data } = await api.get<LTIContextsResponse>(url);
  return data.data;
}

export async function getLTIContextMembers(
  params: GetContextMembersParams
): Promise<LTIContextMember[]> {
  const url = getBITSEndApiMap("ltiGetContextMembers");
  const { data } = await api.get<LTIContextMembersResponse>(url, { params });
  return data.data;
}

export async function getUnassignedResourceLinks(
  params: GetUnassignedResourceLinksParams
): Promise<LTIResourceLink[]> {
  const url = getBITSEndApiMap("ltiGetUnassignedResourceLinks");
  const { data } = await api.get<LTIResourceLinksResponse>(url, { params });
  return data.data;
}

export async function linkProject(
  request: LinkProjectRequest
): Promise<LinkProjectResponse> {
  const url = getBITSEndApiMap("ltiLinkProject");
  const { data } = await api.post<LinkProjectResponse>(url, request);
  return data;
}

// React Query Hooks

export function useLTIContextsQuery(enabled = true) {
  return useQuery({
    queryKey: ["lti", "contexts"] as QueryKey,
    queryFn: getLTIContexts,
    enabled,
    staleTime: 60_000,
  } as UseQueryOptions<LTIContext[]> as any);
}

export function useLTIContextMembersQuery(
  params: GetContextMembersParams,
  enabled = true
) {
  return useQuery({
    queryKey: [
      "lti",
      "contextMembers",
      params.contextId,
      params.role,
    ] as QueryKey,
    queryFn: () => getLTIContextMembers(params),
    enabled: enabled && !!params.contextId,
    staleTime: 60_000,
  } as UseQueryOptions<LTIContextMember[]> as any);
}

export function useUnassignedResourceLinksQuery(
  params: GetUnassignedResourceLinksParams,
  enabled = true
) {
  return useQuery({
    queryKey: ["lti", "resourceLinks", params.contextId] as QueryKey,
    queryFn: () => getUnassignedResourceLinks(params),
    enabled: enabled && !!params.contextId,
    staleTime: 60_000,
  } as UseQueryOptions<LTIResourceLink[]> as any);
}

export function useLinkProjectMutation() {
  return useMutation({
    mutationFn: linkProject,
  });
}
