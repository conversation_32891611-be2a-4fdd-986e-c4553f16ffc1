import axios, { AxiosInstance } from "axios";
import { useQuery, UseQueryOptions, QueryKey } from "@tanstack/react-query";
import getBITSEndApiMap from "../apiMap";
import { tokenService } from "../services/TokenService";

// Attach auth similar to jupyterManagementApi
function getAuthToken() {
  const t = tokenService.getToken();
  return t ? `Bearer ${t}` : "";
}

function attachAuth(instance: AxiosInstance) {
  instance.interceptors.request.use((config) => {
    const auth = getAuthToken();
    if (auth) {
      config.headers = config.headers ?? {};
      config.headers["Authorization"] = auth;
    }
    return config;
  });
  return instance;
}

const api = attachAuth(
  axios.create({
    headers: { "Content-Type": "application/json" },
    withCredentials: true,
    timeout: 120000,
  })
);
export interface Instructor {
  id: string;
  name: string;
  email: string;
}

export interface UserSubmission {
  id: string;
  status: "submitted" | "pending" | "graded" | string;
  submittedAt?: string | null;
  grade?: number | null;
  feedback?: string | null;
}

export interface EnhancedProjectCourse {
  id: string;
  name: string;
  code: string;
  term: string;
  description?: string;
  instructor: Instructor;
}

export interface EnhancedProject {
  id: string;
  projectId: string; // keep if your app already depends on this
  title: string;
  description?: string;
  status: "draft" | "published" | "archived" | string;
  difficultyLevel: "beginner" | "intermediate" | "advanced" | string;
  startDate: string | null;
  dueDate: string | null;
  submissionCount: number;
  userSubmission: UserSubmission | null;
  instructors: Instructor[];
  course: EnhancedProjectCourse;
  checkpoints: any[];
  subject?: string;
  difficulty?: string;
  progress?: any;
  sandboxStatus?: string;
  totalPoints?: any;
  hasDataset?: any;
  totalCheckpoints?: any;
  createdAt?: string;
  updatedAt?: string;

  // 🔽 Same names as API response
  instructions?: string | null;
  course_id?: string;
  created_by?: string;
  difficulty_level?: string;
  estimated_hours?: number;
  notebook_template_s3_url?: string | null;
  dataset_s3_url?: any[];
  additional_files_s3_urls?: any[];
  late_submission_allowed?: boolean;
  late_penalty_percent?: string;
  max_attempts?: number;
  auto_grading_enabled?: boolean;
  learning_objectives?: string;
  prerequisites?: string;
  tags?: string[];
  settings?: Record<string, any>;
  isScreen?: number;
  project_code?: string;
  category_id?: string;
  instructor_id?: string[];
  teaching_ass_id?: string[];
  total_points?: number;
  project_overview?: string;
  type?: string;
  sandbox_time_duration?: string | null;
  late_submission_days_allowed?: number;
  deletedAt?: string | null;
  creator?: {
    id: string;
    name: string;
    email: string;
  };
  rubrics?: any[];
  template?: any;
  assignments?: any[];
  assignmentStats?: {
    totalAssignments: number;
    byRole: Record<string, any>;
    roles: string[];
  };
  teachingAssistants?: {
    id: string;
    name: string;
    email: string;
    status: string;
  }[];
  students?: {
    id: string;
    name: string;
    email: string;
    status: string;
  }[];
  Submission?: {
    count: number;
    rows: any[];
  };
}

export interface EnhancedProjectsPagination {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

export interface EnhancedProjectsResponseData {
  projects: EnhancedProject[];
  pagination: EnhancedProjectsPagination;
}

export interface EnhancedProjectsResponse {
  isSuccess: boolean;
  message: string;
  data: EnhancedProjectsResponseData;
}

export interface GetEnhancedProjectsParams {
  page?: number; // default 1
  limit?: number; // default 10
  search?: string;
  courseId?: string;
  status?: "draft" | "published" | "archived";
  difficultyLevel?: "beginner" | "intermediate" | "advanced";
  token?: string | null; // Add token parameter (can be null)
}

export async function getEnhancedProjects(
  params: GetEnhancedProjectsParams = {}
): Promise<EnhancedProjectsResponseData> {
  const url = getBITSEndApiMap("getProjects"); 
  // Filter out token from params since it's handled by the request interceptor
  const { token, ...apiParams } = params;
  const { data } = await api.get<EnhancedProjectsResponse>(url, { params: apiParams });
  return data.data;
}


export async function publishProject(projectId: string): Promise<{ success: boolean; message: string }> {
  const url = getBITSEndApiMap("publishProject").replace(":id", projectId);
  const { data } = await api.put(url);
  return data;
}

export async function unpublishProject(projectId: string): Promise<{ success: boolean; message: string }> {
  const url = getBITSEndApiMap("unpublishProject").replace(":id", projectId);
  const { data } = await api.put(url);
  return data;
}
export async function getProjectById(
  id: string
): Promise<EnhancedProject> {
  const url = `${getBITSEndApiMap("getProjectById")}/${id}/details`;
  const { data } = await api.get<{ data: EnhancedProject }>(url);
  return data.data;
}


export function useEnhancedProjectsQuery(
  params: GetEnhancedProjectsParams,
  options?: Partial<
    UseQueryOptions<
      EnhancedProjectsResponseData,
      Error,
      EnhancedProjectsResponseData,
      QueryKey
    >
  >
) {
  return useQuery<EnhancedProjectsResponseData, Error, EnhancedProjectsResponseData, QueryKey>({
    queryKey: ["projects", "enhanced", params] as QueryKey,
    queryFn: () => getEnhancedProjects(params),
    ...(options as any),
  });
}