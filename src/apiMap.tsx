// apiMap.tsx
// Remove any `http` or other Node-only imports from frontend files.
// import { get } from "http"; // <-- remove this

const APIMapping = {
  /*************************** Auth API ******************************/
  authLogin: "/api/auth/login",
  authLogout: "/api/auth/logout",
  authRefresh: "/api/auth/refresh",
  authMe: "/api/auth/me",
  /*************************** Project API ******************************/
  createProject: "/api/projects/enhanced",
  editProject: "/api/projects",
  getProjects: "/api/projects/enhanced",
  getProjectById: "/api/projects", //?courseId=courseId
  publishProject: "/api/projects/:id/publish",
  unpublishProject: "/api/projects/:id/unpublish",
  /*************************** Course API ******************************/
  getCourses: "/api/courses/project-creation",
  getCourseTeachingStaff: "/api/courses/:courseId/teaching-staff",
  /*************************** Dataset API ******************************/
  uploadDataset: "/api/s3/upload-project-dataset",
  /*************************** Checkpoint API ******************************/
  createCheckPoint: "/api/checkpoints",
  /*************************** Rubric API ******************************/
  createRubric: "/api/rubrics",
  /*************************** Jupyter Management API ******************************/
  jupyterCreateWorkspace: "/api/jupyter/:projectId/createWorkspace",
  jupyterGetContents: "/api/jupyter/api/contents/:projectId",
  jupyterGetServerVersion: "/api/jupyter/api",
  jupyterGetCurrentUser: "/api/jupyter/api/me",
  jupyterGetServerStatus: "/api/jupyter/api/status",
  jupyterListSessions: "/api/jupyter/api/sessions",
  jupyterCreateSession: "/api/jupyter/api/sessions",
  jupyterListKernels: "/api/jupyter/api/kernels",
  jupyterStartKernel: "/api/jupyter/api/kernels",
  jupyterGetKernel: "/api/jupyter/api/kernels/:kernelId",
  jupyterDeleteKernel: "/api/jupyter/api/kernels/:kernelId",
  jupyterInterruptKernel: "/api/jupyter/api/kernels/:kernelId/interrupt",
  jupyterRestartKernel: "/api/jupyter/api/kernels/:kernelId/restart",
  jupyterExecuteCell: "/api/jupyter/kernels/:kernelId/execute",
  jupyterExecuteNotebook: "/api/jupyter/kernels/:kernelId/execute-notebook",
  jupyterCreateContents: "/api/jupyter/api/contents/:path",
  jupyterUpdateContents: "/api/jupyter/api/contents/:path",
  jupyterDeleteContents: "/api/jupyter/api/contents/:path",
  jupyterGetCheckpoints: "/api/jupyter/api/contents/:path/checkpoints",
  jupyterCreateCheckpoint: "/api/jupyter/api/contents/:path/checkpoints",
  jupyterRestoreCheckpoint:
    "/api/jupyter/api/contents/:path/checkpoints/:checkpointId",
  jupyterDeleteCheckpoint:
    "/api/jupyter/api/contents/:path/checkpoints/:checkpointId",
  /*************************** LTI API ******************************/
  ltiGetContexts: "/api/lti/contexts",
  ltiGetContextMembers: "/api/lti/contexts/members",
  ltiGetUnassignedResourceLinks: "/api/lti/resource-links/unassigned",
  ltiLinkProject: "/api/lti/ags/link-project",
} as const;

type ApiName = keyof typeof APIMapping;
// Note: readEnv was unused and caused TS noUnusedLocals error; keeping logic commented for future use.
// function readEnv() {
//   // CRA / Next.js
//   if (typeof process !== "undefined" && typeof process.env !== "undefined") {
//     return process.env as Record<string, string | undefined>;
//   }
//   // Vite
//   if (typeof import.meta !== "undefined" && (import.meta as any).env) {
//     return (import.meta as any).env as Record<string, string | undefined>;
//   }
//   // Runtime-injected env (optional)
//   if (typeof window !== "undefined" && (window as any).__ENV__) {
//     return (window as any).__ENV__;
//   }
//   return {};
// }

const getBITSEndApiMap = (name: ApiName) => {
  try {
    console.log("ApiName:", name);

    const BASE_URL = "http://localhost:5000";
    // const BASE_URL = "https://bitsds-test.openturf.dev/bits_be";

    // const env = readEnv();

    // // try common names (prefer tool-specific prefixes)
    // const NODE_ENV =
    //   env.REACT_APP_NODE_ENV ||
    //   env.VITE_NODE_ENV ||
    //   env.NODE_ENV ||
    //   env.NEXT_PUBLIC_NODE_ENV ||
    //   "production";

    // // choose base URL using multiple possible env variable names
    // const BASE_URL =
    //   NODE_ENV === "development"
    //     ? env.REACT_APP_DEVELOPMENT_API_BASE_URL ||
    //       env.VITE_DEVELOPMENT_API_BASE_URL ||
    //       env.DEVELOPMENT_API_BASE_URL
    //     : NODE_ENV === "testing"
    //     ? env.REACT_APP_TESTING_API_BASE_URL ||
    //       env.VITE_TESTING_API_BASE_URL ||
    //       env.TESTING_API_BASE_URL
    //     : env.REACT_APP_PRODUCTION_API_BASE_URL ||
    //       env.VITE_PRODUCTION_API_BASE_URL ||
    //       env.PRODUCTION_API_BASE_URL ||
    //       // fallback generic var
    //       env.REACT_APP_API_BASE_URL ||
    //       env.VITE_API_BASE_URL ||
    //       env.API_BASE_URL;

    // if (!BASE_URL) {
    //   throw new Error(
    //     "API base URL not found. Ensure env vars are set. For Vite use VITE_API_BASE_URL, for CRA use REACT_APP_API_BASE_URL."
    //   );
    // }

    // ensure no duplicate slash
    return `${BASE_URL.replace(/\/$/, "")}${APIMapping[name]}`;
  } catch (error) {
    console.error("Error in getBITSEndApiMap:", error);
    throw error;
  }
};

export default getBITSEndApiMap;
