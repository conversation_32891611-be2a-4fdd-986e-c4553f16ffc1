{"info": {"_postman_id": "a0013161-917b-493e-b9bb-c4c1243bf5f4", "name": "BITS DS Platform - LTI Selected Endpoints", "description": "Postman collection for key LTI routes. Base URL uses {{base_url}}. Collection-level Bearer token set to {{bearer_token}}. Each request lists mandatory and optional parameters with examples.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "30140794", "_collection_link": "https://live-share.postman.co/workspace/bitsDs~77b59363-c1e4-4312-b9a4-d8f8ffb49119/collection/30140794-a0013161-917b-493e-b9bb-c4c1243bf5f4?action=share&source=collection_link&creator=30140794"}, "item": [{"name": "Context", "item": [{"name": "List LTI Contexts", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/lti/contexts", "host": ["{{base_url}}"], "path": ["api", "lti", "contexts"], "query": [{"key": "page", "value": "1", "description": "Optional: page (integer). Example: 1", "disabled": true}, {"key": "limit", "value": "10", "description": "Optional: limit (integer). Example: 10", "disabled": true}]}, "description": "Get all LTI contexts.\n\nParameters:\n- Optional: page (integer)\n- Optional: limit (integer)\n\nNotes:\n- Authorization: Bearer {{bearer_token}} required."}, "response": [{"name": "200 OK - Sample", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/lti/contexts", "host": ["{{base_url}}"], "path": ["api", "lti", "contexts"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"isSuccess\": true,\n    \"message\": \"Get All Contexts\",\n    \"data\": {\n        \"success\": true,\n        \"data\": [\n            {\n                \"id\": \"79a23219-7f79-4240-8b85-93b78ceae095\",\n                \"platformId\": \"5222e75e-bb72-45b9-9dde-42abae5a291c\",\n                \"deploymentId\": \"4efd1903-714f-4304-9a73-6d4a29dba83d\",\n                \"courseId\": null,\n                \"contextId\": \"6830\",\n                \"contextType\": \"http://purl.imsglobal.org/vocab/lis/v2/course#CourseOffering\",\n                \"contextTitle\": \"Machine Learning 101 Test\",\n                \"contextLabel\": \"ML101\",\n                \"isActive\": true,\n                \"created_at\": \"2025-09-09T15:20:31.839Z\",\n                \"updated_at\": \"2025-09-21T07:32:31.689Z\",\n                \"deletedAt\": null,\n                \"platform\": {\n                    \"id\": \"5222e75e-bb72-45b9-9dde-42abae5a291c\",\n                    \"platformName\": \"Brightspace D2L\",\n                    \"platformId\": \"https://bitspilani.brightspacedemo.com\"\n                }\n            }\n        ],\n        \"totalPages\": 1,\n        \"currentPage\": 1,\n        \"totalItems\": 1\n    }\n}"}, {"name": "403 Forbidden - Sample", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/lti/contexts", "host": ["{{base_url}}"], "path": ["api", "lti", "contexts"]}}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "Text", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{ \n  \"success\": false, \n  \"message\": \"Insufficient permissions\" \n}"}]}]}, {"name": "Enrollment", "item": [{"name": "Get Context Members", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/lti/contexts/members?contextId=79a23219-7f79-4240-8b85-93b78ceae095&platformId=5222e75e-bb72-45b9-9dde-42abae5a291c&role=instructor", "host": ["{{base_url}}"], "path": ["api", "lti", "contexts", "members"], "query": [{"key": "contextId", "value": "79a23219-7f79-4240-8b85-93b78ceae095", "description": "Mandatory: contextId (UUID of LtiContext primary key)"}, {"key": "platformId", "value": "5222e75e-bb72-45b9-9dde-42abae5a291c", "description": "Mandatory: platformId (UUID of LtiPlatform primary key)"}, {"key": "role", "value": "instructor", "description": "Optional: role filter. One of admin, instructor, ta, student. If omitted, returns all roles."}]}, "description": "Get users within a context for a platform.\n\nParameters:\n- Mandatory: contextId (UUID)\n- Mandatory: platformId (UUID)\n- Optional: role (admin|instructor|ta|student)\n\nExamples:\n- With role filter: role=student\n- Without role: omit the parameter\n\nNotes:\n- Authorization: Bearer {{bearer_token}} required."}, "response": [{"name": "200 OK - Sample", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/lti/contexts/members?contextId=3f7b5f7e-3b3b-4a6d-9c2a-1fd2b1a23abc&platformId=a1b2c3d4-e5f6-7890-abcd-ef0123456789", "host": ["{{base_url}}"], "path": ["api", "lti", "contexts", "members"], "query": [{"key": "contextId", "value": "3f7b5f7e-3b3b-4a6d-9c2a-1fd2b1a23abc"}, {"key": "platformId", "value": "a1b2c3d4-e5f6-7890-abcd-ef0123456789"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "Text", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"isSuccess\": true,\n    \"message\": \"Get Context Instructors\",\n    \"data\": {\n        \"success\": true,\n        \"data\": [\n            {\n                \"id\": \"f2bb0594-462d-47b8-9fd6-0c56aaea4e2d\",\n                \"name\": \"<PERSON><PERSON><PERSON>\",\n                \"email\": \"<EMAIL>\",\n                \"lms_user_id\": \"42b30877-dcc9-4d58-acb2-05ea9cb84e7a_368\",\n                \"status\": \"active\",\n                \"profile_picture\": null,\n                \"roleId\": \"7d036477-68ec-494a-ad08-a78b2fafce5a\",\n                \"roleName\": \"instructor\"\n            }\n        ]\n    }\n}"}, {"name": "400 Bad Request - Sample", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/lti/contexts/members?contextId=79a23219-7f79-4240-8b85-93b78ceae095&platformId=5222e75e-bb72-45b9-9dde-42abae5a291c&role=instructor", "host": ["{{base_url}}"], "path": ["api", "lti", "contexts", "members"], "query": [{"key": "contextId", "value": "79a23219-7f79-4240-8b85-93b78ceae095", "description": "Mandatory: contextId (UUID of LtiContext primary key)"}, {"key": "platformId", "value": "5222e75e-bb72-45b9-9dde-42abae5a291c", "description": "Mandatory: platformId (UUID of LtiPlatform primary key)"}, {"key": "role", "value": "instructor", "description": "Optional: role filter. One of admin, instructor, ta, student. If omitted, returns all roles."}]}, "description": "Get users within a context for a platform.\n\nParameters:\n- Mandatory: contextId (UUID)\n- Mandatory: platformId (UUID)\n- Optional: role (admin|instructor|ta|student)\n\nExamples:\n- With role filter: role=student\n- Without role: omit the parameter\n\nNotes:\n- Authorization: Bearer {{bearer_token}} required."}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "Text", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{ \n  \"success\": false, \n  \"message\": \"Missing/invalid parameters: contextId, platformId\" \n}"}]}]}, {"name": "Resource Links", "item": [{"name": "Get Unassigned Resource Links", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/lti/resource-links/unassigned?contextId=79a23219-7f79-4240-8b85-93b78ceae095&platformId=5222e75e-bb72-45b9-9dde-42abae5a291c", "host": ["{{base_url}}"], "path": ["api", "lti", "resource-links", "unassigned"], "query": [{"key": "page", "value": "1", "description": "Optional: page (integer). Default: 1", "disabled": true}, {"key": "limit", "value": "10", "description": "Optional: limit (integer). Default: 10", "disabled": true}, {"key": "contextId", "value": "79a23219-7f79-4240-8b85-93b78ceae095", "description": "Optional: contextId (filter by context)"}, {"key": "platformId", "value": "5222e75e-bb72-45b9-9dde-42abae5a291c", "description": "Optional: platformId (filter by platform)"}, {"key": "sortBy", "value": "createdAt:desc", "description": "Optional: sort (e.g., createdAt:asc)", "disabled": true}]}, "description": "Get unassigned LTI resource links.\n\nParameters:\n- Optional: page (integer)\n- Optional: limit (integer)\n- Optional: contextId (string)\n- Optional: platformId (string)\n- Optional: sortBy (string, e.g., 'createdAt:asc')\n\nNotes:\n- Authorization: Bearer {{bearer_token}} required."}, "response": [{"name": "200 OK - Sample", "originalRequest": {"method": "GET", "header": []}, "status": "OK", "code": 200, "_postman_previewlanguage": "Text", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"isSuccess\": true,\n    \"message\": \"Get Unassigned Resource Links\",\n    \"data\": {\n        \"success\": true,\n        \"data\": [\n            {\n                \"id\": \"1b52d950-a687-47ba-820f-9f9a2569b967\",\n                \"platformId\": \"5222e75e-bb72-45b9-9dde-42abae5a291c\",\n                \"contextId\": \"79a23219-7f79-4240-8b85-93b78ceae095\",\n                \"projectId\": null,\n                \"resourceLinkId\": \"132_6830\",\n                \"resourceLinkTitle\": \"Linear regression\",\n                \"resourceLinkDescription\": null,\n                \"customParameters\": {},\n                \"isActive\": true,\n                \"created_at\": \"2025-09-16T11:00:39.311Z\",\n                \"updated_at\": \"2025-09-21T07:32:31.692Z\",\n                \"deletedAt\": null,\n                \"context\": {\n                    \"id\": \"79a23219-7f79-4240-8b85-93b78ceae095\",\n                    \"contextTitle\": \"Machine Learning 101 Test\",\n                    \"contextLabel\": \"ML101\"\n                },\n                \"platform\": {\n                    \"id\": \"5222e75e-bb72-45b9-9dde-42abae5a291c\",\n                    \"platformName\": \"Brightspace D2L\"\n                },\n                \"lineItem\": {\n                    \"id\": \"9bc919a3-ead4-45dd-a589-0df8479abb10\",\n                    \"scoreMaximum\": \"100.00\",\n                    \"label\": \"Linear regression\",\n                    \"tag\": null,\n                    \"startDateTime\": null,\n                    \"endDateTime\": null\n                }\n            },\n            {\n                \"id\": \"b8a1ccc3-87ca-4c24-8a10-e0d930bb71bb\",\n                \"platformId\": \"5222e75e-bb72-45b9-9dde-42abae5a291c\",\n                \"contextId\": \"79a23219-7f79-4240-8b85-93b78ceae095\",\n                \"projectId\": null,\n                \"resourceLinkId\": \"131_6830\",\n                \"resourceLinkTitle\": \"demo\",\n                \"resourceLinkDescription\": null,\n                \"customParameters\": {},\n                \"isActive\": true,\n                \"created_at\": \"2025-09-16T10:41:03.612Z\",\n                \"updated_at\": \"2025-09-16T10:41:03.612Z\",\n                \"deletedAt\": null,\n                \"context\": {\n                    \"id\": \"79a23219-7f79-4240-8b85-93b78ceae095\",\n                    \"contextTitle\": \"Machine Learning 101 Test\",\n                    \"contextLabel\": \"ML101\"\n                },\n                \"platform\": {\n                    \"id\": \"5222e75e-bb72-45b9-9dde-42abae5a291c\",\n                    \"platformName\": \"Brightspace D2L\"\n                },\n                \"lineItem\": null\n            },\n            {\n                \"id\": \"ccb6ec1a-ea75-44e4-aaad-0e4f1202adf4\",\n                \"platformId\": \"5222e75e-bb72-45b9-9dde-42abae5a291c\",\n                \"contextId\": \"79a23219-7f79-4240-8b85-93b78ceae095\",\n                \"projectId\": null,\n                \"resourceLinkId\": \"130_6830\",\n                \"resourceLinkTitle\": \"ML test\",\n                \"resourceLinkDescription\": \"Test redirection\",\n                \"customParameters\": {},\n                \"isActive\": true,\n                \"created_at\": \"2025-09-09T15:20:31.843Z\",\n                \"updated_at\": \"2025-09-21T05:04:39.413Z\",\n                \"deletedAt\": null,\n                \"context\": {\n                    \"id\": \"79a23219-7f79-4240-8b85-93b78ceae095\",\n                    \"contextTitle\": \"Machine Learning 101 Test\",\n                    \"contextLabel\": \"ML101\"\n                },\n                \"platform\": {\n                    \"id\": \"5222e75e-bb72-45b9-9dde-42abae5a291c\",\n                    \"platformName\": \"Brightspace D2L\"\n                },\n                \"lineItem\": {\n                    \"id\": \"e06d46c7-f845-4a5a-8965-f18a5797419c\",\n                    \"scoreMaximum\": \"100.00\",\n                    \"label\": \"ML test\",\n                    \"tag\": null,\n                    \"startDateTime\": null,\n                    \"endDateTime\": null\n                }\n            }\n        ],\n        \"totalPages\": 1,\n        \"currentPage\": 1,\n        \"totalItems\": 3\n    }\n}"}, {"name": "403 Forbidden - Sample", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/lti/resource-links/unassigned?contextId=79a23219-7f79-4240-8b85-93b78ceae095&platformId=5222e75e-bb72-45b9-9dde-42abae5a291c", "host": ["{{base_url}}"], "path": ["api", "lti", "resource-links", "unassigned"], "query": [{"key": "page", "value": "1", "description": "Optional: page (integer). Default: 1", "disabled": true}, {"key": "limit", "value": "10", "description": "Optional: limit (integer). Default: 10", "disabled": true}, {"key": "contextId", "value": "79a23219-7f79-4240-8b85-93b78ceae095", "description": "Optional: contextId (filter by context)"}, {"key": "platformId", "value": "5222e75e-bb72-45b9-9dde-42abae5a291c", "description": "Optional: platformId (filter by platform)"}, {"key": "sortBy", "value": "createdAt:desc", "description": "Optional: sort (e.g., createdAt:asc)", "disabled": true}]}, "description": "Get unassigned LTI resource links.\n\nParameters:\n- Optional: page (integer)\n- Optional: limit (integer)\n- Optional: contextId (string)\n- Optional: platformId (string)\n- Optional: sortBy (string, e.g., 'createdAt:asc')\n\nNotes:\n- Authorization: Bearer {{bearer_token}} required."}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "Text", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{ \n  \"success\": false, \n  \"message\": \"Insufficient permissions\" \n}"}]}]}, {"name": "Projects", "item": [{"name": "Link Project to LMS Line Item", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"resourceLinkId\": \"1b52d950-a687-47ba-820f-9f9a2569b967\",\n  \"projectId\": \"11111111-**************-************\",\n  \"total_points\": 100,\n  \"instructor_ids\": [\n    \"aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee\",\n    \"ffffffff-1111-2222-3333-************\"\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/lti/ags/link-project", "host": ["{{base_url}}"], "path": ["api", "lti", "ags", "link-project"]}, "description": "Link a local project to an LMS line item and set the line item's resourceId on the LMS.\n\nBody Parameters:\n- Mandatory: resourceLinkId (string)\n- Mandatory: projectId (UUID)\n- Optional: total_points (integer ≥ 1)\n- Optional: instructor_ids (array of UUIDs)\n\nExamples:\n- Provide total_points only when overriding from the LMS line item.\n- instructor_ids can be omitted or provided as an empty array.\n\nNotes:\n- Authorization: Bearer {{bearer_token}} required."}, "response": [{"name": "200 OK - Sample", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"resourceLinkId\": \"1b52d950-a687-47ba-820f-9f9a2569b967\",\n  \"projectId\": \"11111111-**************-************\",\n  \"total_points\": 100,\n  \"instructor_ids\": [\n    \"aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee\",\n    \"ffffffff-1111-2222-3333-************\"\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/lti/ags/link-project", "host": ["{{base_url}}"], "path": ["api", "lti", "ags", "link-project"]}, "description": "Link a local project to an LMS line item and set the line item's resourceId on the LMS.\n\nBody Parameters:\n- Mandatory: resourceLinkId (string)\n- Mandatory: projectId (UUID)\n- Optional: total_points (integer ≥ 1)\n- Optional: instructor_ids (array of UUIDs)\n\nExamples:\n- Provide total_points only when overriding from the LMS line item.\n- instructor_ids can be omitted or provided as an empty array.\n\nNotes:\n- Authorization: Bearer {{bearer_token}} required."}, "status": "OK", "code": 200, "_postman_previewlanguage": "Text", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"Mapping updated and LMS line item resourceId set\",\n  \"data\": {\n    \"resourceLinkId\": \"rlid-XYZ-123\",\n    \"projectId\": \"11111111-**************-************\",\n    \"lineItem\": {\n      \"id\": \"https://lms.example.edu/lineitems/123\",\n      \"label\": \"Project 1\",\n      \"scoreMaximum\": 100\n    }\n  }\n}"}, {"name": "400 Bad Request - Sample", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"resourceLinkId\": \"1b52d950-a687-47ba-820f-9f9a2569b967\",\n  \"projectId\": \"11111111-**************-************\",\n  \"total_points\": 100,\n  \"instructor_ids\": [\n    \"aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee\",\n    \"ffffffff-1111-2222-3333-************\"\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/lti/ags/link-project", "host": ["{{base_url}}"], "path": ["api", "lti", "ags", "link-project"]}, "description": "Link a local project to an LMS line item and set the line item's resourceId on the LMS.\n\nBody Parameters:\n- Mandatory: resourceLinkId (string)\n- Mandatory: projectId (UUID)\n- Optional: total_points (integer ≥ 1)\n- Optional: instructor_ids (array of UUIDs)\n\nExamples:\n- Provide total_points only when overriding from the LMS line item.\n- instructor_ids can be omitted or provided as an empty array.\n\nNotes:\n- Authorization: Bearer {{bearer_token}} required."}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "Text", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{ \n  \"success\": false, \n  \"message\": \"Validation error: resourceLinkId and projectId are required\" \n}"}, {"name": "404 Not Found - Sample", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"resourceLinkId\": \"1b52d950-a687-47ba-820f-9f9a2569b967\",\n  \"projectId\": \"11111111-**************-************\",\n  \"total_points\": 100,\n  \"instructor_ids\": [\n    \"aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee\",\n    \"ffffffff-1111-2222-3333-************\"\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/lti/ags/link-project", "host": ["{{base_url}}"], "path": ["api", "lti", "ags", "link-project"]}, "description": "Link a local project to an LMS line item and set the line item's resourceId on the LMS.\n\nBody Parameters:\n- Mandatory: resourceLinkId (string)\n- Mandatory: projectId (UUID)\n- Optional: total_points (integer ≥ 1)\n- Optional: instructor_ids (array of UUIDs)\n\nExamples:\n- Provide total_points only when overriding from the LMS line item.\n- instructor_ids can be omitted or provided as an empty array.\n\nNotes:\n- Authorization: Bearer {{bearer_token}} required."}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "Text", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{ \n  \"success\": false, \n  \"message\": \"Resource link or project not found\" \n}"}]}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{bearer_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "", "type": "string", "description": "Base URL for the API, e.g., http://localhost:5000"}, {"key": "bearer_token", "value": "", "type": "string", "description": "JWT Bearer token. Set this at the environment level or here."}]}