import React, { useState, useEffect } from "react";
import { Card } from "./ui/card";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { Progress } from "./ui/progress";
import {
  Play,
  Square,
  Clock,
  Cpu,
  HardDrive,
  Container,
  Zap,
  Cloud,
  RotateCcw,
  Alert<PERSON>riangle,
} from "lucide-react";
import { toast } from "sonner";

interface ActiveSession {
  sessionId: string;
  status: "Active" | "Idle" | "Terminating";
  mode: "UV" | "DOCKER" | "FARGATE";
  startedAt: string;
  idleTimeout: number;
  timeRemaining: number; // in minutes
  cpuUsage?: number; // percentage
  memoryUsage?: number; // percentage
  cpuLimit: string;
  memLimit: string;
}

interface ActiveSessionBannerProps {
  session: ActiveSession | null;
  onStop: (sessionId: string) => void;
  onSync: () => void;
  projectId: string;
}

// Mock API function that simulates stopping a session
const stopSession = async (projectId: string, sessionId: string) => {
  // Simulate API call delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  toast.success("Sandbox session stopped successfully");

  return {
    success: true,
    message: "Session stopped successfully",
  };
};

export default function ActiveSessionBanner({
  session,
  onStop,
  onSync,
  projectId,
}: ActiveSessionBannerProps) {
  const [timeRemaining, setTimeRemaining] = useState(
    session?.timeRemaining || 0
  );
  const [isSyncing, setIsSyncing] = useState(false);
  const [isStopping, setIsStopping] = useState(false);

  const getModeIcon = (mode: string) => {
    switch (mode) {
      case "UV":
        return <Zap className="w-4 h-4" />;
      case "DOCKER":
        return <Container className="w-4 h-4" />;
      case "FARGATE":
        return <Cloud className="w-4 h-4" />;
      default:
        return <Container className="w-4 h-4" />;
    }
  };

  const getModeColor = (mode: string) => {
    switch (mode) {
      case "UV":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "DOCKER":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "FARGATE":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800 border-green-200";
      case "Idle":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Terminating":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatTime = (minutes: number) => {
    if (minutes < 0) return "0m";
    const hours = Math.floor(minutes / 60);
    const mins = Math.floor(minutes % 60);
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const formatDuration = (startTime: string) => {
    const start = new Date(startTime);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    return formatTime(diffMins);
  };

  const handleStop = async () => {
    if (!session) return;

    setIsStopping(true);
    try {
      // Use the callback provided by parent component instead of making direct API call
      await onStop(session.sessionId);
    } catch (error) {
      console.error("Failed to stop session:", error);
    } finally {
      setIsStopping(false);
    }
  };

  const handleSync = async () => {
    setIsSyncing(true);
    try {
      await onSync();
    } catch (error) {
      console.error("Failed to sync:", error);
    } finally {
      setIsSyncing(false);
    }
  };

  // Update countdown timer
  useEffect(() => {
    if (!session || session.status === "Terminating") return;

    const interval = setInterval(() => {
      setTimeRemaining((prev) => Math.max(0, prev - 1));
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [session]);

  if (!session) return null;

  const isLowTime = timeRemaining <= 5;
  const showWarning = session.status === "Idle" || isLowTime;

  return (
    <Card
      className={`border-l-4 ${
        showWarning
          ? "border-l-yellow-500 bg-yellow-50"
          : "border-l-green-500 bg-green-50"
      }`}
    >
      <div className="p-4">
        <div className="flex items-center justify-between flex-wrap gap-4">
          {/* Left section - Session info */}
          <div className="flex items-center gap-4 flex-wrap">
            {/* Status */}
            <div className="flex items-center gap-2">
              <Badge
                variant="outline"
                className={getStatusColor(session.status)}
              >
                <Play className="w-3 h-3 mr-1" />
                {session.status}
              </Badge>

              <Badge variant="outline" className={getModeColor(session.mode)}>
                {getModeIcon(session.mode)}
                <span className="ml-1">{session.mode}</span>
              </Badge>
            </div>

            {/* Time info */}
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>Running: {formatDuration(session.startedAt)}</span>
              </div>

              <div
                className={`flex items-center gap-1 ${
                  isLowTime ? "text-red-600 font-medium" : ""
                }`}
              >
                {isLowTime && <AlertTriangle className="w-4 h-4" />}
                <span>
                  {isLowTime ? "Stopping in: " : "Time left: "}
                  {formatTime(timeRemaining)}
                </span>
              </div>
            </div>

            {/* Resource usage */}
            {(session.cpuUsage !== undefined ||
              session.memoryUsage !== undefined) && (
              <div className="flex items-center gap-4 text-sm">
                {session.cpuUsage !== undefined && (
                  <div className="flex items-center gap-2">
                    <Cpu className="w-4 h-4 text-gray-500" />
                    <div className="flex items-center gap-2">
                      <Progress value={session.cpuUsage} className="w-16 h-2" />
                      <span className="text-xs text-gray-600 min-w-[3rem]">
                        {session.cpuUsage}%
                      </span>
                    </div>
                  </div>
                )}

                {session.memoryUsage !== undefined && (
                  <div className="flex items-center gap-2">
                    <HardDrive className="w-4 h-4 text-gray-500" />
                    <div className="flex items-center gap-2">
                      <Progress
                        value={session.memoryUsage}
                        className="w-16 h-2"
                      />
                      <span className="text-xs text-gray-600 min-w-[3rem]">
                        {session.memoryUsage}%
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Right section - Controls */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSync}
              disabled={isSyncing || session.status === "Terminating"}
              className="flex items-center gap-1"
            >
              <RotateCcw
                className={`w-4 h-4 ${isSyncing ? "animate-spin" : ""}`}
              />
              {isSyncing ? "Syncing..." : "Sync Now"}
            </Button>

            <Button
              variant="destructive"
              size="sm"
              onClick={handleStop}
              disabled={isStopping || session.status === "Terminating"}
              className="flex items-center gap-1 !bg-bits-red"
            >
              <Square className="w-4 h-4" />
              {isStopping ? "Stopping..." : "Stop Session"}
            </Button>
          </div>
        </div>

        {/* Warning message */}
        {showWarning && (
          <div
            className={`mt-3 p-2 rounded-md ${
              session.status === "Idle"
                ? "bg-yellow-100 border border-yellow-200"
                : "bg-red-100 border border-red-200"
            }`}
          >
            <div className="flex items-center gap-2">
              <AlertTriangle
                className={`w-4 h-4 ${
                  session.status === "Idle" ? "text-yellow-600" : "text-red-600"
                }`}
              />
              <span
                className={`text-sm ${
                  session.status === "Idle" ? "text-yellow-800" : "text-red-800"
                }`}
              >
                {session.status === "Idle"
                  ? "Session is idle. It will automatically stop if no activity is detected."
                  : "Session will stop soon due to idle timeout. Save your work and sync changes."}
              </span>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}
