// import { createContext, useContext, ReactNode } from "react";
// import { useLoginMutation, logoutUser } from "../../api/authApi";

// export type UserRole = "student" | "instructor" | "admin";

// export interface User {
//   id: string;
//   name: string;
//   email: string;
//   role: UserRole;
//   roles:any[]
// }

// export interface AuthContextType {
//   user: User | null;
//   login: (email: string, password: string, role: UserRole) => Promise<boolean>;
//   logout: () => void;
//     setUser: (user: User | null) => void;
// }

// const AuthContext = createContext<AuthContextType | null>(null);

// export function AuthProvider({
//   user,
//   setUser,
//   children,
// }: {
//   user: User | null;
//   setUser: (user: User | null) => void;
//   children: ReactNode;
// }) {
//   const login = async (
//     email: string,
//     password: string,
//     role: UserRole
//   ): Promise<boolean> => {
//     // Simulate API call
//     await new Promise((resolve) => setTimeout(resolve, 1000));
//   return false;
//   };
//   const logout = () => {
//     logoutUser(); // optional API call
//     setUser(null); // update App's user state
//   };

//   return (
//     <AuthContext.Provider value={{ user,setUser, login, logout }}>
//       {children}
//     </AuthContext.Provider>
//   );
// }

// export function useAuth() {
//   const context = useContext(AuthContext);
//   if (!context) throw new Error("useAuth must be used within an AuthProvider");
//   return context;
// }

// export default useAuth;
