import { createContext, useContext } from "react";
export interface NavigationContextType {
  currentPage: string;
  navigateTo: (page: string, params?: any) => void;
  pageParams: any;
  goBack: () => void;
  navigationHistory: Array<{ page: string; params: any }>;
}

const NavigationContext = createContext<NavigationContextType | null>(null);
 const useNavigation = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error("useNavigation must be used within a NavigationProvider");
  }
  return context;
};
export {NavigationContext}
export default useNavigation