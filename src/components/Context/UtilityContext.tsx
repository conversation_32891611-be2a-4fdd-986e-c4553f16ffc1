import { createContext, useContext } from "react";
export interface UtilityContextType {
  downloadSubmission: (submissionId: string, submissionData: any) => void;
  generateSubmissionZip: (submissionData: any) => Promise<Blob>;
}
const UtilityContext = createContext<UtilityContextType | null>(null);

const useUtilities = () => {
  const context = useContext(UtilityContext);
  if (!context) {
    throw new Error("useUtilities must be used within a UtilityProvider");
  }
  return context;
};

export {UtilityContext}
export default useUtilities;