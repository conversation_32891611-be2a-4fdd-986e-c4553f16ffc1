import React from "react";
import { Badge } from "./ui/badge";
import { Label } from "./ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./ui/card";
import {
  BookOpen,
  Calendar,
  User,
  GraduationCap,
  Award,
  Clock,
} from "lucide-react";

interface CourseEnrollment {
  courseId: string;
  courseName: string;
  courseCode: string;
  instructor: string;
  enrollmentDate: string;
  currentGrade?: string;
  status: "enrolled" | "completed" | "dropped";
  role?: "student" | "ta" | "instructor";
}

interface UserData {
  id: string;
  name: string;
  role: string;
  courses: CourseEnrollment[];
  gpa?: number;
  totalCredits?: number;
  year?: string;
}

interface CoursesModalProps {
  user: UserData;
  trigger: React.ReactNode;
}

export function CoursesModal({ user, trigger }: CoursesModalProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "enrolled":
        return "bg-bits-blue/10 text-bits-blue";
      case "completed":
        return "bg-bits-green/10 text-bits-green";
      case "dropped":
        return "bg-bits-red/10 text-bits-red";
      default:
        return "bg-gray-100 text-muted-foreground";
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "student":
        return "bg-bits-blue/10 text-bits-blue border-bits-blue";
      case "ta":
        return "bg-bits-purple/10 text-bits-purple border-bits-purple";
      case "instructor":
        return "bg-bits-green/10 text-bits-green border-bits-green";
      default:
        return "bg-gray-50 text-muted-foreground border-gray-200";
    }
  };

  const getGradeColor = (grade: string) => {
    if (grade.startsWith("A"))
      return "bg-bits-green/10 text-bits-green border-bits-green";
    if (grade.startsWith("B"))
      return "bg-bits-blue/10 text-bits-blue border-bits-blue";
    if (grade.startsWith("C"))
      return "bg-bits-yellow/10 text-bits-yellow border-bits-yellow";
    if (grade.startsWith("D"))
      return "bg-bits-orange/10 text-bits-orange border-bits-orange";
    return "bg-bits-red/10 text-bits-red border-bits-red";
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const enrolledCourses = user.courses.filter(
    (course) => course.status === "enrolled"
  );
  const completedCourses = user.courses.filter(
    (course) => course.status === "completed"
  );
  const droppedCourses = user.courses.filter(
    (course) => course.status === "dropped"
  );

  return (
    <Dialog>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto bg-white border">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <BookOpen className="h-5 w-5 text-bits-blue" />
            <span>Course Enrollments</span>
          </DialogTitle>
          <DialogDescription>
            Course enrollment details for {user.name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* User Summary for Students */}
          {user.role === "student" && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Academic Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-bits-blue">
                      {user.gpa?.toFixed(2) || "N/A"}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Current GPA
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-bits-green">
                      {user.totalCredits || "N/A"}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Total Credits
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-bits-orange">
                      {user.year || "N/A"}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Academic Year
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Course Statistics */}
          <div className="grid grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-bits-blue" />
                  <div>
                    <div className="text-2xl font-bold text-bits-blue">
                      {enrolledCourses.length}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Currently Enrolled
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center space-x-2">
                  <Award className="h-4 w-4 text-bits-green" />
                  <div>
                    <div className="text-2xl font-bold text-bits-green">
                      {completedCourses.length}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Completed
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center space-x-2">
                  <GraduationCap className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-2xl font-bold text-muted-foreground">
                      {user.courses.length}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Total Courses
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Active Enrollments */}
          {enrolledCourses.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center space-x-2">
                <Clock className="h-5 w-5 text-bits-blue" />
                <span>Current Enrollments</span>
              </h3>
              <div className="border rounded-lg overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead>Course</TableHead>
                      <TableHead>Instructor</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Enrolled</TableHead>
                      {user.role === "student" && <TableHead>Grade</TableHead>}
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {enrolledCourses.map((course, index) => (
                      <TableRow key={index} className="hover:bg-gray-50">
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {course.courseName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {course.courseCode}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <User className="h-4 w-4 text-gray-500" />
                            <span>{course.instructor}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={getRoleColor(course.role || "student")}
                          >
                            {course.role || "student"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-gray-500" />
                            <span className="text-sm">
                              {formatDate(course.enrollmentDate)}
                            </span>
                          </div>
                        </TableCell>
                        {user.role === "student" && (
                          <TableCell>
                            {course.currentGrade ? (
                              <Badge
                                variant="outline"
                                className={getGradeColor(course.currentGrade)}
                              >
                                {course.currentGrade}
                              </Badge>
                            ) : (
                              <span className="text-sm text-gray-500">
                                In Progress
                              </span>
                            )}
                          </TableCell>
                        )}
                        <TableCell>
                          <Badge className={getStatusColor(course.status)}>
                            {course.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}

          {/* Completed Courses */}
          {completedCourses.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center space-x-2">
                <Award className="h-5 w-5 text-bits-green" />
                <span>Completed Courses</span>
              </h3>
              <div className="border rounded-lg overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead>Course</TableHead>
                      <TableHead>Instructor</TableHead>
                      <TableHead>Completed</TableHead>
                      {user.role === "student" && (
                        <TableHead>Final Grade</TableHead>
                      )}
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {completedCourses.map((course, index) => (
                      <TableRow key={index} className="hover:bg-gray-50">
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {course.courseName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {course.courseCode}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <User className="h-4 w-4 text-gray-500" />
                            <span>{course.instructor}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-gray-500" />
                            <span className="text-sm">
                              {formatDate(course.enrollmentDate)}
                            </span>
                          </div>
                        </TableCell>
                        {user.role === "student" && (
                          <TableCell>
                            {course.currentGrade ? (
                              <Badge
                                variant="outline"
                                className={getGradeColor(course.currentGrade)}
                              >
                                {course.currentGrade}
                              </Badge>
                            ) : (
                              <span className="text-sm text-gray-500">
                                No Grade
                              </span>
                            )}
                          </TableCell>
                        )}
                        <TableCell>
                          <Badge className={getStatusColor(course.status)}>
                            {course.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}

          {/* No Courses Message */}
          {user.courses.length === 0 && (
            <Card>
              <CardContent className="p-8 text-center">
                <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No Course Enrollments
                </h3>
                <p className="text-gray-600">
                  This user is not currently enrolled in any courses.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default CoursesModal;
