import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "./ui/dialog";
import { But<PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Badge } from "./ui/badge";
import {
  Search,
  Filter,
  Upload,
  Plus,
  ArrowLeft,
  Database,
  Download,
  Copy,
  Eye,
  MoreHorizontal,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { toast } from "sonner";

interface Dataset {
  id: string;
  name: string;
  description: string;
  author: string;
  lastModified: string;
  size: string;
  version: string;
  type: "Project File" | "Template" | "User Created";
  tags: string[];
  format: string;
}

const mockDatasets: Dataset[] = [
  {
    id: "1",
    name: "california_housing.csv",
    description: "Official California housing dataset for the assignment",
    author: "<PERSON><PERSON> <PERSON><PERSON>",
    lastModified: "2 weeks ago",
    size: "2.4 MB",
    version: "v1",
    type: "Project File",
    tags: ["template", "assignment"],
    format: "CSV",
  },
  {
    id: "2",
    name: "housing_features.csv",
    description: "Feature descriptions and metadata for the housing dataset",
    author: "Dr. A. Sharma",
    lastModified: "1 week ago",
    size: "1.8 MB",
    version: "v1",
    type: "Project File",
    tags: ["guide", "reference"],
    format: "CSV",
  },
  {
    id: "3",
    name: "cleaned_housing.csv",
    description: "Cleaned dataset with preprocessed features",
    author: "Rahul Sharma",
    lastModified: "2 hours ago",
    size: "1.76 MB",
    version: "v3",
    type: "User Created",
    tags: ["analysis", "progress"],
    format: "CSV",
  },
  {
    id: "4",
    name: "feature_engineered.csv",
    description: "Dataset with engineered features for model training",
    author: "Rahul Sharma",
    lastModified: "1 day ago",
    size: "2.1 MB",
    version: "v1",
    type: "User Created",
    tags: ["experiment", "models"],
    format: "CSV",
  },
  {
    id: "5",
    name: "test_data.csv",
    description: "Test dataset for model evaluation",
    author: "Rahul Sharma",
    lastModified: "3 days ago",
    size: "856 KB",
    version: "v1",
    type: "User Created",
    tags: ["final", "submission"],
    format: "CSV",
  },
];

interface DatasetManagementProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDatasetSelect?: (dataset: Dataset) => void;
  isPage?: boolean;
}

export default function DatasetManagement({
  open,
  onOpenChange,
  onDatasetSelect,
  isPage = false,
}: DatasetManagementProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [datasets] = useState<Dataset[]>(mockDatasets);

  const filteredDatasets = datasets.filter(
    (dataset) =>
      dataset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      dataset.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      dataset.tags.some((tag) =>
        tag.toLowerCase().includes(searchTerm.toLowerCase())
      )
  );

  const handleDownload = (dataset: Dataset) => {
    toast.success(`Downloading ${dataset.name}`);
  };

  const handleDuplicate = (dataset: Dataset) => {
    const extension = dataset.name.split(".").pop();
    const baseName = dataset.name.replace(`.${extension}`, "");
    toast.success(
      `Duplicated ${dataset.name} as ${baseName}_copy.${extension}`
    );
  };

  const handleView = (dataset: Dataset) => {
    if (onDatasetSelect) {
      onDatasetSelect(dataset);
    }
    onOpenChange(false);
    toast.success(`Opening ${dataset.name}`);
  };

  const handleUpload = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".csv,.json,.xlsx,.parquet";
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        toast.success(`Uploaded ${file.name}`);
      }
    };
    input.click();
  };

  const handleCreateNew = () => {
    toast.success("Creating new dataset...");
    onOpenChange(false);
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "Project File":
        return "bg-bits-blue/10 text-bits-blue border-bits-blue";
      case "Template":
        return "bg-bits-gold/10 text-bits-gold border-bits-gold";
      case "User Created":
        return "bg-muted-foreground/10 text-muted-foreground border-muted-foreground";
      default:
        return "bg-muted-foreground/10 text-muted-foreground border-muted-foreground";
    }
  };

  const content = (
    <>
      <div className="p-6 pb-0">
        <div className="flex items-center justify-between">
          {!isPage && (
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back
              </Button>
              <div className="flex items-center space-x-2">
                <Database className="h-5 w-5 text-bits-blue" />
                <DialogTitle>Dataset Management</DialogTitle>
              </div>
            </div>
          )}
          {isPage && (
            <div className="flex items-center space-x-2">
              <Database className="h-5 w-5 text-bits-blue" />
              <h2 className="text-xl font-semibold">Dataset Management</h2>
            </div>
          )}
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleUpload}>
              <Upload className="h-4 w-4 mr-2" />
              Upload dataset
            </Button>
            <Button
              size="sm"
              className="bg-gray-900 hover:bg-gray-800 text-white"
              onClick={handleCreateNew}
            >
              <Plus className="h-4 w-4 mr-2" />
              New dataset
            </Button>
          </div>
        </div>
        {!isPage && (
          <DialogDescription>
            Manage your datasets, upload new data files, or create new datasets
            for your data science projects.
          </DialogDescription>
        )}
        {isPage && (
          <p className="text-sm text-muted-foreground mt-2">
            Manage your datasets, upload new data files, or create new datasets
            for your data science projects.
          </p>
        )}
      </div>

      <div className="px-6 pb-2">
        <div className="flex items-center space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search datasets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9"
            />
          </div>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto px-6 pb-6">
        <div className="space-y-3">
          {filteredDatasets.map((dataset) => (
            <div
              key={dataset.id}
              className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <Database className="h-4 w-4 text-bits-blue flex-shrink-0" />
                    <h3 className="font-medium text-gray-900 truncate">
                      {dataset.name}
                    </h3>
                    <Badge
                      variant="outline"
                      className={`text-xs ${getTypeColor(dataset.type)}`}
                    >
                      {dataset.type}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {dataset.version}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {dataset.description}
                  </p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>👤 {dataset.author}</span>
                    <span>⏰ {dataset.lastModified}</span>
                    <span>📁 {dataset.size}</span>
                  </div>
                  <div className="flex items-center space-x-1 mt-2">
                    {dataset.tags.map((tag) => (
                      <Badge
                        key={tag}
                        variant="secondary"
                        className="text-xs px-2 py-0"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div className="flex items-center space-x-2 ml-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleView(dataset)}
                    className="h-8 px-3"
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    Open
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleDownload(dataset)}>
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDuplicate(dataset)}
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Duplicate
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );

  if (isPage) {
    return <div className="h-full flex flex-col bg-white">{content}</div>;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] p-0 bg-white border">
        <DialogHeader className="sr-only">
          <DialogTitle>Dataset Management</DialogTitle>
          <DialogDescription>Manage your datasets</DialogDescription>
        </DialogHeader>
        {content}
      </DialogContent>
    </Dialog>
  );
}
