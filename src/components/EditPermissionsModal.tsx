import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  DialogDescription,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "./ui/dialog";
import { Button } from "./ui/button";
import { Checkbox } from "./ui/checkbox";
import { Card } from "./ui/card";
import { Roles, Permission } from "../pages/RolesPermissionsListPage";
import { toast } from "sonner";

export function EditPermissionsModal({
  role,
  trigger,
  onSave,
}: {
  role: Roles;
  trigger: React.ReactNode;
  onSave: (updatedRow: Roles) => void;
}) {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load role permissions when modal opens
  useEffect(() => {
    if (role) {
      // ✅ deep clone to avoid mutating parent state directly
      setPermissions(JSON.parse(JSON.stringify(role.permissions)));
    }
  }, [role]);

  const togglePermission = (id: number, checked: boolean) => {
    setPermissions((prev) =>
      prev.map((p) =>
        p.id === id
          ? {
              ...p,
              isChecked: checked,
              nestedPermissions: p.nestedPermissions
                ? p.nestedPermissions.map((np) => ({
                    ...np,
                    isChecked: checked,
                  }))
                : null, // ✅ enforce `null` instead of `undefined`
            }
          : p
      )
    );
  };

  const toggleNested = (parentId: number, name: string, checked: boolean) => {
    setPermissions((prev) =>
      prev.map((p) =>
        p.id === parentId
          ? {
              ...p,
              isChecked: checked,
              nestedPermissions: p.nestedPermissions
                ? p.nestedPermissions.map((np) => ({
                    ...np,
                    isChecked: checked,
                  }))
                : null, // ✅ return null instead of undefined
            }
          : p
      )
    );
  };

  const handleSave = () => {
    if (!role) return;
    setIsSubmitting(true);
    onSave({ ...role, permissions });
    toast.success(`Permission saved successfully for ${role.roleName}`);
    setIsSubmitting(false);
  };

  return (
    <Dialog>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto bg-white border">
        <DialogHeader>
          <DialogTitle className="text-xl font-medium mb-3">
            Edit Permissions
          </DialogTitle>
          <DialogDescription>
            <div className="flex flex-col text-md font-medium">
              Role name
              <span className="text-sm font-normal text-muted-foreground">
                {role?.roleName}
              </span>
            </div>
          </DialogDescription>
        </DialogHeader>

        <span className="text-md font-medium">Select Permissions</span>
        <Card>
          {permissions.map((permission) => (
            <div className="ml-3 mt-3 mb-3" key={permission.id}>
              <label className="flex items-center space-x-2">
                <div className="flex flex-col">
                  <div>
                    <Checkbox
                      checked={permission.isChecked}
                      onCheckedChange={(c) =>
                        togglePermission(permission.id, c === true)
                      }
                      className="data-[state=checked]:bg-bits-blue data-[state=checked]:text-white"
                    />
                    <span className="text-sm font-medium text-bits-grey-600 ml-2">
                      {permission.name}
                    </span>
                    <div className="ml-4">
                      {permission.nestedPermissions?.map((nestedPermission) => (
                        <div key={nestedPermission.name} className="mt-8">
                          <Checkbox
                            checked={nestedPermission.isChecked}
                            onCheckedChange={(c) =>
                              toggleNested(
                                permission.id,
                                nestedPermission.name,
                                c === true
                              )
                            }
                            className="data-[state=checked]:bg-bits-blue data-[state=checked]:text-white"
                          />
                          <span className="ml-2 text-sm font-medium text-bits-grey-600">
                            {nestedPermission.name}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </label>
            </div>
          ))}
        </Card>
        <DialogFooter>
          <DialogClose asChild>
            <Button
              variant="outline"
              className="w-60 text-md font-semibold text-bits-grey-700"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          </DialogClose>

          <DialogClose asChild>
            <Button
              variant="outline"
              className="ml-4 w-60 text-md font-semibold text-white bg-bits-blue"
              onClick={handleSave}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
