import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "./ui/card";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { Progress } from "./ui/progress";
import {
  ArrowLeft,
  ExternalLink,
  RefreshCw,
  Maximize2,
  Minimize2,
  Terminal,
  FileText,
  Save,
  Download,
  Settings,
  AlertCircle,
  CheckCircle2,
} from "lucide-react";
import { toast } from "sonner";

interface EmbeddedNotebookProps {
  userId: string;
  projectId: string;
  projectName: string;
  mode?: "student" | "instructor" | "review";
  onBack?: () => void;
  onSave?: () => void;
  onDownload?: () => void;
  sessionId?: string;
}

interface NotebookStatus {
  isLoaded: boolean;
  isConnected: boolean;
  kernelStatus: "idle" | "busy" | "starting" | "dead";
  lastSaved?: string;
  hasUnsavedChanges: boolean;
}

export default function EmbeddedNotebook({
  userId,
  projectId,
  projectName,
  mode = "student",
  onBack,
  onSave,
  onDownload,
  sessionId,
}: EmbeddedNotebookProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [notebookStatus, setNotebookStatus] = useState<NotebookStatus>({
    isLoaded: false,
    isConnected: false,
    kernelStatus: "starting",
    hasUnsavedChanges: false,
  });
  const [loadingProgress, setLoadingProgress] = useState(0);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const notebookUrl = `/studio/user/${userId}/lab/tree/work`;

  // Mock notebook interface - replace iframe for demo
  const MockNotebookInterface = () => (
    <div className="w-full h-full bg-gray-50 flex items-center justify-center">
      <div className="max-w-2xl mx-auto p-8 text-center">
        <div className="w-20 h-20 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center">
          <Terminal className="w-10 h-10 text-blue-600" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-4">
          JupyterLab Environment Ready
        </h3>
        <p className="text-gray-600 mb-6">
          This is a demo of the embedded JupyterLab interface. In the actual
          platform, students would have access to a full Jupyter environment
          with Python kernels, data science libraries, and project files.
        </p>
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
          <h4 className="font-medium text-gray-900 mb-3">
            Features Available:
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <CheckCircle2 className="w-4 h-4 text-green-600" />
              Python 3.9+ with data science libraries
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle2 className="w-4 h-4 text-green-600" />
              Jupyter notebooks and code cells
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle2 className="w-4 h-4 text-green-600" />
              File browser and workspace management
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle2 className="w-4 h-4 text-green-600" />
              Real-time collaboration features
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle2 className="w-4 h-4 text-green-600" />
              Version control integration
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle2 className="w-4 h-4 text-green-600" />
              Assignment submission workflow
            </div>
          </div>
        </div>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-sm text-blue-800">
            <strong>Demo Note:</strong> In the production environment, this area
            would contain a fully functional JupyterLab interface connected to
            your personal workspace.
          </p>
        </div>
      </div>
    </div>
  );

  const getKernelStatusColor = (status: string) => {
    switch (status) {
      case "idle":
        return "bg-green-100 text-green-800";
      case "busy":
        return "bg-yellow-100 text-yellow-800";
      case "starting":
        return "bg-blue-100 text-blue-800";
      case "dead":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getKernelStatusIcon = (status: string) => {
    switch (status) {
      case "idle":
        return <CheckCircle2 className="w-3 h-3" />;
      case "busy":
        return <RefreshCw className="w-3 h-3 animate-spin" />;
      case "starting":
        return <RefreshCw className="w-3 h-3 animate-spin" />;
      case "dead":
        return <AlertCircle className="w-3 h-3" />;
      default:
        return <AlertCircle className="w-3 h-3" />;
    }
  };

  const formatLastSaved = (dateString?: string) => {
    if (!dateString) return "Never";
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;

    return date.toLocaleDateString();
  };

  // Simulate notebook loading
  useEffect(() => {
    const loadNotebook = async () => {
      // Simulate loading progress
      for (let i = 0; i <= 100; i += 10) {
        setLoadingProgress(i);
        await new Promise((resolve) => setTimeout(resolve, 200));
      }

      // Simulate notebook status updates
      setNotebookStatus((prev) => ({
        ...prev,
        isLoaded: true,
        isConnected: true,
        kernelStatus: "idle",
        lastSaved: new Date().toISOString(),
      }));
    };

    loadNotebook();
  }, []);

  // Simulate periodic status updates
  useEffect(() => {
    if (!notebookStatus.isLoaded) return;

    const interval = setInterval(() => {
      // Randomly update kernel status and save status for demo
      const statuses: ("idle" | "busy")[] = ["idle", "busy"];
      const randomStatus =
        statuses[Math.floor(Math.random() * statuses.length)];

      setNotebookStatus((prev) => ({
        ...prev,
        kernelStatus: randomStatus,
        hasUnsavedChanges: Math.random() > 0.7, // Randomly show unsaved changes
        lastSaved:
          Math.random() > 0.8 ? new Date().toISOString() : prev.lastSaved,
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, [notebookStatus.isLoaded]);

  const handleToggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    if (!isFullscreen) {
      document.documentElement.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
  };

  const handleSave = () => {
    // In real implementation, this would communicate with the iframe
    setNotebookStatus((prev) => ({
      ...prev,
      hasUnsavedChanges: false,
      lastSaved: new Date().toISOString(),
    }));
    onSave?.();
  };

  const handleRefresh = () => {
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
    }
  };

  const handleOpenInNewTab = () => {
    toast.info("Demo Environment", {
      description:
        "In the production environment, this would open the JupyterLab interface in a new tab with your personal workspace.",
      duration: 4000,
    });
  };

  const customTheme = `
    <style>
      /* Custom JupyterLab theme to match platform */
      :root {
        --jp-brand-color1: #2B2B88 !important;
        --jp-brand-color2: #B78A2D !important;
        --jp-accent-color1: #2B2B88 !important;
        --jp-ui-font-family: "Inter", system-ui, sans-serif !important;
      }
      
      /* Hide extension manager for students */
      ${
        mode === "student"
          ? `
        .jp-extensionmanager-view,
        .jp-ExtensionManager {
          display: none !important;
        }
      `
          : ""
      }
      
      /* Custom toolbar styling */
      .jp-Toolbar {
        border-bottom: 1px solid #e5e7eb !important;
        background: #f9fafb !important;
      }
    </style>
  `;

  const containerClass = isFullscreen
    ? "fixed inset-0 z-50 bg-white"
    : "w-full h-full";

  if (!notebookStatus.isLoaded) {
    return (
      <Card className={containerClass}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Terminal className="w-5 h-5" />
            Loading JupyterLab Environment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                <Terminal className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="font-medium text-gray-900 mb-2">
                Setting up your workspace
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Please wait while we initialize your JupyterLab environment...
              </p>

              <div className="max-w-md mx-auto">
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Loading...</span>
                  <span>{loadingProgress}%</span>
                </div>
                <Progress value={loadingProgress} className="w-full" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={containerClass}>
      {/* Custom Toolbar */}
      <div className="bg-white border-b border-gray-200 p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {onBack && (
              <Button
                variant="outline"
                size="sm"
                onClick={onBack}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Platform
              </Button>
            )}

            <div className="flex items-center gap-2">
              <Terminal className="w-4 h-4 text-gray-500" />
              <span className="font-medium text-gray-900">{projectName}</span>
              {mode === "review" && (
                <Badge variant="secondary">Review Mode</Badge>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Kernel Status */}
            <Badge
              variant="outline"
              className={getKernelStatusColor(notebookStatus.kernelStatus)}
            >
              {getKernelStatusIcon(notebookStatus.kernelStatus)}
              <span className="ml-1 capitalize">
                {notebookStatus.kernelStatus}
              </span>
            </Badge>

            {/* Save Status */}
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>
                Last saved: {formatLastSaved(notebookStatus.lastSaved)}
              </span>
              {notebookStatus.hasUnsavedChanges && (
                <Badge
                  variant="outline"
                  className="bg-yellow-100 text-yellow-800"
                >
                  Unsaved changes
                </Badge>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-1">
              {mode !== "review" && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSave}
                  disabled={!notebookStatus.hasUnsavedChanges}
                  className="flex items-center gap-1"
                >
                  <Save className="w-4 h-4" />
                  Save
                </Button>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={onDownload}
                className="flex items-center gap-1"
              >
                <Download className="w-4 h-4" />
                Export
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                className="flex items-center gap-1"
              >
                <RefreshCw className="w-4 h-4" />
                Refresh
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleToggleFullscreen}
                className="flex items-center gap-1"
              >
                {isFullscreen ? (
                  <Minimize2 className="w-4 h-4" />
                ) : (
                  <Maximize2 className="w-4 h-4" />
                )}
                {isFullscreen ? "Exit" : "Full"} Screen
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleOpenInNewTab}
                className="flex items-center gap-1"
              >
                <ExternalLink className="w-4 h-4" />
                Open in New Tab
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* JupyterLab Interface */}
      <div className="flex-1" style={{ height: "calc(100% - 80px)" }}>
        <MockNotebookInterface />
      </div>

      {/* Connection Status Indicator */}
      {!notebookStatus.isConnected && (
        <div className="absolute top-20 right-4 bg-red-100 border border-red-200 rounded-md p-2">
          <div className="flex items-center gap-2 text-sm text-red-800">
            <AlertCircle className="w-4 h-4" />
            <span>Connection lost. Reconnecting...</span>
          </div>
        </div>
      )}
    </div>
  );
}
