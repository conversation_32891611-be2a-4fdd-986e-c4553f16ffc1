import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "./ui/dialog";
import { But<PERSON> } from "./ui/button";
import { Textarea } from "./ui/textarea";
import { Label } from "./ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./ui/card";
import { Separator } from "./ui/separator";
import { Badge } from "./ui/badge";
import {
  Save,
  Send,
  X,
  Code,
  BarChart3,
  FileText,
  CheckCircle,
  AlertTriangle,
  BookOpen,
  Target,
  MessageSquare,
  GraduationCap,
} from "lucide-react";
import { toast } from "sonner";

interface FeedbackDialogProps {
  isOpen: boolean;
  onClose: () => void;
  studentName: string;
  assignmentTitle: string;
  currentFeedback?: string;
  currentGrade?: number;
  onSubmit: (feedbackData: any) => void;
}

interface FeedbackSection {
  id: string;
  title: string;
  description: string;
  score: number;
  comments: string;
  maxScore: number;
  icon: any;
}

export function FeedbackDialog({
  isOpen,
  onClose,
  studentName,
  assignmentTitle,
  currentFeedback,
  currentGrade,
  onSubmit,
}: FeedbackDialogProps) {
  const [feedbackSections, setFeedbackSections] = useState<FeedbackSection[]>([
    {
      id: "code_quality",
      title: "Code Quality & Structure",
      description:
        "Code organization, readability, best practices, and documentation",
      score: 0,
      comments: "",
      maxScore: 20,
      icon: Code,
    },
    {
      id: "methodology",
      title: "Data Science Methodology",
      description:
        "Problem understanding, data exploration, feature engineering, and approach",
      score: 0,
      comments: "",
      maxScore: 25,
      icon: BookOpen,
    },
    {
      id: "model_performance",
      title: "Model Performance & Evaluation",
      description:
        "Model selection, validation, metrics, and performance analysis",
      score: 0,
      comments: "",
      maxScore: 25,
      icon: BarChart3,
    },
    {
      id: "insights_communication",
      title: "Insights & Communication",
      description:
        "Data storytelling, visualizations, conclusions, and business impact",
      score: 0,
      comments: "",
      maxScore: 20,
      icon: MessageSquare,
    },
    {
      id: "reproducibility",
      title: "Reproducibility & Documentation",
      description:
        "Clear instructions, dependency management, and result reproducibility",
      score: 0,
      comments: "",
      maxScore: 10,
      icon: FileText,
    },
  ]);

  const [overallComments, setOverallComments] = useState("");
  const [feedbackType, setFeedbackType] = useState<"detailed" | "quick">(
    "detailed"
  );
  const [quickFeedback, setQuickFeedback] = useState("");
  const [quickGrade, setQuickGrade] = useState("");
  const [strengths, setStrengths] = useState("");
  const [improvements, setImprovements] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const updateSection = (
    sectionId: string,
    field: "score" | "comments",
    value: any
  ) => {
    setFeedbackSections((prev) =>
      prev.map((section) =>
        section.id === sectionId ? { ...section, [field]: value } : section
      )
    );
  };

  const getTotalScore = () => {
    return feedbackSections.reduce(
      (total, section) => total + section.score,
      0
    );
  };

  const getMaxTotalScore = () => {
    return feedbackSections.reduce(
      (total, section) => total + section.maxScore,
      0
    );
  };

  const getGradeColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 90) return "text-green-600";
    if (percentage >= 80) return "text-blue-600";
    if (percentage >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      const feedbackData = {
        type: feedbackType,
        totalScore:
          feedbackType === "detailed"
            ? getTotalScore()
            : parseInt(quickGrade) || 0,
        maxScore: feedbackType === "detailed" ? getMaxTotalScore() : 100,
        sections: feedbackType === "detailed" ? feedbackSections : [],
        overallComments:
          feedbackType === "detailed" ? overallComments : quickFeedback,
        strengths,
        improvements,
        studentName,
        assignmentTitle,
        submittedAt: new Date().toISOString(),
        submittedBy: "Dr. A. Sharma", // This would come from auth context in real app
      };

      await onSubmit(feedbackData);
      toast.success("Feedback submitted successfully!");
      onClose();
    } catch (error) {
      console.error("Error submitting feedback:", error);
      toast.error("Failed to submit feedback. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveDraft = () => {
    // Save draft functionality
    toast.success("Feedback saved as draft");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="flex items-center space-x-2">
                <GraduationCap className="h-5 w-5 text-bits-blue" />
                <span>Provide Feedback</span>
              </DialogTitle>
              <DialogDescription>
                {studentName} • {assignmentTitle}
              </DialogDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Feedback Type Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Feedback Type</CardTitle>
              <CardDescription>
                Choose between detailed rubric-based feedback or quick feedback
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <Button
                  variant={feedbackType === "detailed" ? "default" : "outline"}
                  onClick={() => setFeedbackType("detailed")}
                  className="flex-1"
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Detailed Rubric
                </Button>
                <Button
                  variant={feedbackType === "quick" ? "default" : "outline"}
                  onClick={() => setFeedbackType("quick")}
                  className="flex-1"
                >
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Quick Feedback
                </Button>
              </div>
            </CardContent>
          </Card>

          {feedbackType === "detailed" ? (
            <>
              {/* Detailed Rubric Sections */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Assessment Rubric</h3>
                  <div className="text-right">
                    <div
                      className={`text-lg font-semibold ${getGradeColor(
                        getTotalScore(),
                        getMaxTotalScore()
                      )}`}
                    >
                      {getTotalScore()}/{getMaxTotalScore()}
                    </div>
                    <div className="text-sm text-gray-500">
                      {((getTotalScore() / getMaxTotalScore()) * 100).toFixed(
                        1
                      )}
                      %
                    </div>
                  </div>
                </div>

                {feedbackSections.map((section, index) => {
                  const Icon = section.icon;
                  return (
                    <Card key={section.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Icon className="h-4 w-4 text-bits-blue" />
                            <CardTitle className="text-sm">
                              {section.title}
                            </CardTitle>
                            <Badge variant="outline">
                              {section.score}/{section.maxScore}
                            </Badge>
                          </div>
                        </div>
                        <CardDescription className="text-xs">
                          {section.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div>
                          <Label className="text-xs">
                            Score (0-{section.maxScore})
                          </Label>
                          <Select
                            value={section.score.toString()}
                            onValueChange={(value) =>
                              updateSection(
                                section.id,
                                "score",
                                parseInt(value)
                              )
                            }
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {Array.from(
                                { length: section.maxScore + 1 },
                                (_, i) => (
                                  <SelectItem key={i} value={i.toString()}>
                                    {i}/{section.maxScore}{" "}
                                    {i === section.maxScore
                                      ? "(Excellent)"
                                      : i === 0
                                      ? "(Needs Improvement)"
                                      : ""}
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label className="text-xs">Comments</Label>
                          <Textarea
                            value={section.comments}
                            onChange={(e) =>
                              updateSection(
                                section.id,
                                "comments",
                                e.target.value
                              )
                            }
                            placeholder={`Specific feedback for ${section.title.toLowerCase()}...`}
                            className="min-h-[80px] text-sm"
                          />
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              {/* Overall Comments */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Overall Comments</CardTitle>
                  <CardDescription>
                    General feedback and recommendations for the student
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    value={overallComments}
                    onChange={(e) => setOverallComments(e.target.value)}
                    placeholder="Provide overall feedback, key takeaways, and recommendations for improvement..."
                    className="min-h-[100px]"
                  />
                </CardContent>
              </Card>
            </>
          ) : (
            /* Quick Feedback */
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Quick Feedback</CardTitle>
                <CardDescription>
                  Provide a quick grade and feedback comments
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Grade (0-100)</Label>
                  <Select value={quickGrade} onValueChange={setQuickGrade}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select grade" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="100">
                        A+ (95-100) - Excellent
                      </SelectItem>
                      <SelectItem value="92">A (90-94) - Very Good</SelectItem>
                      <SelectItem value="87">B+ (85-89) - Good</SelectItem>
                      <SelectItem value="82">
                        B (80-84) - Satisfactory
                      </SelectItem>
                      <SelectItem value="77">C+ (75-79) - Average</SelectItem>
                      <SelectItem value="72">
                        C (70-74) - Below Average
                      </SelectItem>
                      <SelectItem value="60">D (60-69) - Poor</SelectItem>
                      <SelectItem value="50">F (0-59) - Fail</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Feedback Comments</Label>
                  <Textarea
                    value={quickFeedback}
                    onChange={(e) => setQuickFeedback(e.target.value)}
                    placeholder="Provide your feedback comments..."
                    className="min-h-[120px]"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Strengths and Areas for Improvement */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Key Strengths</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={strengths}
                  onChange={(e) => setStrengths(e.target.value)}
                  placeholder="What did the student do well?"
                  className="min-h-[80px]"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                  <span>Areas for Improvement</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={improvements}
                  onChange={(e) => setImprovements(e.target.value)}
                  placeholder="What could be improved?"
                  className="min-h-[80px]"
                />
              </CardContent>
            </Card>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <Button variant="outline" onClick={handleSaveDraft}>
              <Save className="h-4 w-4 mr-2" />
              Save Draft
            </Button>

            <div className="flex space-x-2">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={
                  isSubmitting ||
                  (feedbackType === "quick" && (!quickGrade || !quickFeedback))
                }
                className="bg-bits-blue hover:bg-bits-blue/90"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Submit Feedback
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default FeedbackDialog;
