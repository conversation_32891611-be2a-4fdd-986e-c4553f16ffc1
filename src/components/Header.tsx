import { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Badge } from "./ui/badge";
import {
  Bell,
  LogOut,
  User,
  MessageSquare,
  Users,
  Reply,
  CheckCircle,
  TrendingUp,
  UserCog,
  Shield,
  Check,
} from "lucide-react";
import { useAuth,useNavigation } from "../App";
// import useAuth from "./Context/AuthContext";
// import useNavigation from "./Context/NavigationContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { Card, CardContent } from "./ui/card";
import { ImageWithFallback } from "./figma/ImageWithFallback";
import { tokenService } from "../services/TokenService";
import { useNavigate } from "react-router-dom";

interface Notification {
  id: string;
  type: "announcement" | "message" | "forum_reply" | "feedback" | "mention";
  title: string;
  content: string;
  sourceId: string;
  sourceType: string;
  createdAt: string;
  isRead: boolean;
  actionUrl?: string;
  conversationId?: string;
  announcementId?: string;
}

// Mock notifications data with enhanced navigation info
const mockNotifications: Notification[] = [
  {
    id: "1",
    type: "announcement",
    title: "New Announcement",
    content: "Dr. A. Sharma posted: Midterm Project Guidelines Updated",
    sourceId: "1",
    sourceType: "announcement",
    createdAt: "2025-01-07T10:00:00Z",
    isRead: false,
    actionUrl: "/communication?tab=announcements&id=1",
    announcementId: "1",
  },
  {
    id: "2",
    type: "message",
    title: "New Group Message",
    content: "TA Sarah sent feedback for Team Alpha project",
    sourceId: "1",
    sourceType: "message",
    createdAt: "2025-01-07T09:00:00Z",
    isRead: false,
    actionUrl: "/communication?tab=messages&id=1",
    conversationId: "conv_1",
  },
  {
    id: "3",
    type: "forum_reply",
    title: "New Forum Reply",
    content: "Priya Patel replied to your question in Team Alpha discussion",
    sourceId: "thread_1",
    sourceType: "forum",
    createdAt: "2025-01-07T08:30:00Z",
    isRead: true,
    actionUrl: "/communication?tab=forums&threadId=thread_1",
  },
  {
    id: "4",
    type: "feedback",
    title: "Project Feedback",
    content: "You received feedback on your Linear Regression project",
    sourceId: "project_1",
    sourceType: "project",
    createdAt: "2025-01-07T07:15:00Z",
    isRead: false,
    actionUrl: "/project-details?id=project_1",
  },
  {
    id: "5",
    type: "mention",
    title: "You were mentioned",
    content: "Rahul Sharma mentioned you in a group discussion",
    sourceId: "mention_1",
    sourceType: "mention",
    createdAt: "2025-01-07T06:45:00Z",
    isRead: true,
    actionUrl: "/communication?tab=forums&threadId=mention_1",
  },
];

function NotificationDropdown() {
  const { navigateTo } = useNavigation();
  const navigate = useNavigate()
  const [notifications, setNotifications] = useState(mockNotifications);
  const [isOpen, setIsOpen] = useState(false);

  const unreadCount = notifications.filter((n) => !n.isRead).length;
  const recentNotifications = notifications.slice(0, 3);

  const getNotificationIcon = (type: string) => {
    const iconStyle = { width: "18px", height: "24px" };
    switch (type) {
      case "announcement":
        return <Bell style={iconStyle} className="text-bits-blue" />;
      case "message":
        return <MessageSquare style={iconStyle} className="text-orange-500" />;
      case "forum_reply":
        return <Reply style={iconStyle} className=" text-green-600" />;
      case "feedback":
        return <CheckCircle style={iconStyle} className=" text-purple-600" />;
      case "mention":
        return <Users style={iconStyle} className="text-bits-red" />;
      default:
        return <Bell style={iconStyle} />;
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    // Mark as read
    setNotifications((prev) =>
      prev.map((n) => (n.id === notification.id ? { ...n, isRead: true } : n))
    );

    // Navigate based on notification type
    if (notification.type === "announcement") {
      navigateTo("communication", {
        tab: "announcements",
        announcementId: notification.announcementId,
      });
    } else if (notification.type === "message") {
      navigateTo("communication", {
        tab: "messages",
        conversationId: notification.conversationId,
      });
    } else if (
      notification.type === "forum_reply" ||
      notification.type === "mention"
    ) {
      navigateTo("communication", {
        tab: "messages",
        conversationId: notification.sourceId,
      });
    } else if (notification.type === "feedback") {
      navigate(`/project-details/${notification.sourceId}`)
//      navigateTo("project-details", { projectId: notification.sourceId });
    } else {
      // Default fallback
      navigateTo("communication");
    }

    setIsOpen(false);
  };

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((n) => ({ ...n, isRead: true })));
  };

  const timeAgo = (date: string) => {
    const now = new Date();
    const past = new Date(date);
    const diffInHours = Math.floor(
      (now.getTime() - past.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <Bell className="h-4 w-4" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs h-5 w-5 flex items-center justify-center p-0">
              {unreadCount > 9 ? "9+" : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="p-0 border border-gray-300 rounded-lg shadow-md w-auto max-w-md"
      >
        <DropdownMenuLabel className="flex items-center justify-between px-5 py-4 border-b border-gray-300 rounded-t-lg mb-2">
          <span className="font-inter font-semibold text-xl text-gray-900">
            Notification
          </span>
          {unreadCount > 0 && (
            <Button
              variant="destructive"
              size="sm"
              className="font-inter font-semibold text-base  text-gray-600 hover:text-gray-800"
              onClick={markAllAsRead}
            >
              <Check className="h-3 w-3 mr-1" />
              Mark all as read
            </Button>
          )}
        </DropdownMenuLabel>

        <div>
          {recentNotifications.length > 0 ? (
            recentNotifications.map((notification) => (
              <div key={notification.id}>
                <DropdownMenuItem
                  className={`p-0 cursor-pointer`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <Card className="w-full border-0 shadow-none hover:bg-gray-50">
                    <CardContent className="px-1 py-1 flex items-start gap-3">
                      <div className="flex items-center justify-center w-12 h-12">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 mr-4">
                        <div className="flex items-center justify-between mb-1">
                          <p className="font-inter font-medium text-base text-gray-800 truncate">
                            {notification.title}
                          </p>
                        </div>
                        <p className="font-inter font-normal text-sm text-gray-600 line-clamp-2">
                          {notification.content}
                        </p>
                      </div>
                      <div className="flex items-center gap-2 mr-2 flex-shrink-0">
                        <span className="text-xs text-gray-500">
                          {timeAgo(notification.createdAt)}
                        </span>
                        {!notification.isRead && (
                          <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </DropdownMenuItem>
              </div>
            ))
          ) : (
            <div className="p-4 text-center text-muted-foreground text-sm">
              No notifications
            </div>
          )}
        </div>

        <DropdownMenuItem
          className="flex items-center justify-center gap-1 px-2 py-3 border-t border-gray-300 rounded-b-lg hover:bg-gray-50"
          onClick={() =>
            navigateTo("communication", { defaultTab: "announcements" })
          }
        >
          <MessageSquare className="h-6 w-6 text-gray-800" />
          <span className="font-inter font-medium text-lg text-gray-800">
            View all communication
          </span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default function Header() {
  const { user, logout } = useAuth();
  const { navigateTo } = useNavigation();
  const [accessToken, setAccessToken] = useState<string>("");

  useEffect(() => {
    // prefill from storage
    setAccessToken(tokenService.getToken() ?? "");
  }, []);

  const handleSaveToken = () => {
    const trimmed = accessToken.trim();
    if (trimmed) {
      tokenService.setToken(trimmed);
    } else {
      tokenService.clearToken();
    }
  };

  const handleClearToken = () => {
    tokenService.clearToken();
    setAccessToken("");
  };

  if (!user) return null;

  return (
    <header className="fixed top-0 left-0 w-full h-[72px] z-50 border-b border-gray-300 bg-white flex-shrink-0 flex items-center justify-between px-4">
      <div className="flex items-center justify-between w-full h-full">
        {/* Logo and Title */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 relative">
              <ImageWithFallback
                src="https://upload.wikimedia.org/wikipedia/en/d/d3/BITS_Pilani-Logo.svg"
                alt="BITS Pilani Logo"
                className="w-full h-full object-contain"
              />
            </div>
            <div>
              <h1 className="font-semibold text-bits-blue">BITS Pilani</h1>
              <p className="text-xs text-muted-foreground">
                Data Science Projects Platform
              </p>
            </div>
          </div>
        </div>

        {/* Access Token + User Actions */}
        <div className="flex items-center space-x-3">
          {/* Access Token input for testing */}
          {/* <div className="flex items-center space-x-2 max-w-md">
            <Input
              value={accessToken}
              onChange={(e) => setAccessToken(e.target.value)}
              placeholder="Enter Jupyter access token"
              className="w-72"
            />
            <Button variant="secondary" size="sm" onClick={handleSaveToken}>
              Save
            </Button>
            <Button variant="outline" size="sm" onClick={handleClearToken}>
              Clear
            </Button>
          </div> */}
          <Button
            variant="outline"
            size="sm"
            className="relative"
            onClick={() =>
              navigateTo("communication", { defaultTab: "messages" })
            }
          >
            <MessageSquare className="h-4 w-4" />
          </Button>
          {/* Notifications */}
          <NotificationDropdown />

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <User className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">{user.name}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>
                <div>
                  <p className="font-medium">{user.name}</p>
                  <p className="text-xs text-muted-foreground capitalize">
                    {user.role}
                  </p>
                  <p className="text-xs text-muted-foreground">{user.email}</p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />

              <DropdownMenuItem onClick={() => navigateTo("communication")}>
                <MessageSquare className="h-4 w-4 mr-2" />
                Communication Hub
              </DropdownMenuItem>

              {/* User Management for Admin and Instructor */}
              {(user.role === "admin" || user.role === "instructor") && (
                <DropdownMenuItem onClick={() => navigateTo("user-management")}>
                  <Users className="h-4 w-4 mr-2" />
                  User Management
                </DropdownMenuItem>
              )}

              {/* Role Permissions for Admin*/}
              {user.role === "admin" && (
                <DropdownMenuItem
                  onClick={() => navigateTo("role-permissions")}
                >
                  <Users className="h-4 w-4 mr-2" />
                  Role Permissions
                </DropdownMenuItem>
              )}

              {user.role === "student" && (
                <DropdownMenuItem onClick={() => navigateTo("gradebook")}>
                  <TrendingUp className="h-4 w-4 mr-2" />
                  My Grades
                </DropdownMenuItem>
              )}

              {(user.role === "instructor" || user.role === "admin") && (
                <DropdownMenuItem onClick={() => navigateTo("gradebook")}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Gradebook
                </DropdownMenuItem>
              )}

              {/* Admin-only features */}
              {user.role === "admin" && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => navigateTo("admin-audit")}>
                    <Shield className="h-4 w-4 mr-2" />
                    Audit & Security
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => navigateTo("admin-settings")}
                  >
                    <UserCog className="h-4 w-4 mr-2" />
                    Admin Settings
                  </DropdownMenuItem>
                </>
              )}

              <DropdownMenuSeparator />

              <DropdownMenuItem onClick={logout} className="text-red-600">
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
