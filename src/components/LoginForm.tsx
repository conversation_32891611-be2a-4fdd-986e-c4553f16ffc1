import React, { useState } from "react";
import useAuth, {UserRole} from "./Context/AuthContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { useLoginQuery, logoutUser } from "../api/authApi";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import { toast } from "sonner";
import { ImageWithFallback } from "../components/figma/ImageWithFallback";
import appLogo from "../assets/bits-logo.png";
import { demoCredentials } from "../helper/credential";
import { tokenService } from "@/services/TokenService";
function LoginForm() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [role, setRole] = useState<UserRole>("student");
  const [loading, setLoading] = useState(false);
  const {setUser,login} = useAuth()
  const loginQuery = useLoginQuery({ email, password });

  const handleSubmit = async (e: React.FormEvent) => {
console.log("here")
    e.preventDefault();
    setLoading(true);

    try {
      // Use backend login first
      const result = await loginQuery.refetch();
      const resp = result.data as any;

      if (resp?.isSuccess && resp?.data?.token && resp?.data?.user) {
        // Save token for subsequent requests
        tokenService.setToken(resp.data.token);

        // Map backend role to local role
        const roleName = (
          resp.data.user.roles?.[0]?.name || "student"
        ).toLowerCase();
        const mappedRole: UserRole =
          roleName === "admin"
            ? "admin"
            : roleName === "instructor" || roleName === "teacher"
            ? "instructor"
            : "student";

        const newUser: User = {
          id: resp.data.user.id,
          name: resp.data.user.name,
          email: resp.data.user.email,
          role: mappedRole,
        };
        setUser(newUser);
        try {
          localStorage.setItem("auth_user", JSON.stringify(newUser));
        } catch {}

        toast.success("Login successful!");
      } else {
        // Fallback to demo login if backend says invalid
        const success = await login(email, password, role);
        if (success) {
          toast.success("Login successful!");
        } else {
          toast.error(
            resp?.message || "Invalid credentials. Please try again."
          );
        }
      }
    } catch (error) {
      toast.error("Login failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCredentialClick = (credential: (typeof demoCredentials)[0]) => {
    setEmail(credential.email);
    setPassword(credential.password);
    setRole(credential.role);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#f7f8fa] py-16 px-4">
      <div className="flex flex-col sm:flex-row flex-nowrap items-start justify-center gap-6">
        {/* Login Form Card */}
        <div className="w-full max-w-md">
          <Card className="w-full bg-white rounded-2xl shadow-lg p-8 border border-border">
            <CardHeader className="text-center pb-2">
              <div className="mb-4">
                <div className="w-20 h-20 mx-auto mb-4 relative">
                  <ImageWithFallback
                    src={appLogo}
                    alt="BITS Pilani Logo"
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
              <CardTitle className="text-bits-blue text-xl font-semibold mb-1">
                BITS Pilani Digital
              </CardTitle>
              <CardDescription className="text-base text-muted-foreground">
                Data Science Project Platform
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-2">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2 text-left">
                  <Label htmlFor="email" className="text-sm font-medium">
                    Email
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    required
                    className="bg-muted rounded-lg text-muted-foreground border-none focus:ring-2 focus:ring-bits-blue focus:border-bits-blue"
                  />
                </div>
                <div className="space-y-2 text-left">
                  <Label htmlFor="password" className="text-sm font-medium">
                    Password
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    required
                    className="bg-muted rounded-lg text-muted-foreground border-none focus:ring-2 focus:ring-bits-blue focus:border-bits-blue"
                  />
                </div>
                <div className="space-y-2 text-left">
                  <Label htmlFor="role" className="text-sm font-medium">
                    Role
                  </Label>
                  <Select
                    value={role}
                    onValueChange={(value: UserRole) => setRole(value)}
                  >
                    <SelectTrigger className="bg-muted rounded-lg border-none focus:ring-2 focus:ring-bits-blue focus:border-bits-blue">
                      <SelectValue placeholder="Select your role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="student">Student</SelectItem>
                      <SelectItem value="instructor">Instructor</SelectItem>
                      <SelectItem value="admin">Administrator</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button
                  type="submit"
                  variant="bits"
                  className="w-full rounded-lg py-2 mt-2 shadow-sm text-base font-semibold"
                  disabled={loading}
                >
                  {loading ? "Signing in..." : "Sign In"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Demo Credentials Card */}
        <div className="w-full max-w-md">
          <Card className="w-full shadow-md rounded-lg">
            <CardHeader className="p-8 pb-4">
              <CardTitle className="text-base font-semibold">
                Demo Credentials
              </CardTitle>
              <CardDescription className="text-sm text-muted-foreground">
                Use these credentials to explore different user roles
              </CardDescription>
            </CardHeader>
            <CardContent className="p-8 pt-4 space-y-3">
              {demoCredentials.map((credential, index) => (
                <div
                  key={index}
                  onClick={() => handleCredentialClick(credential)}
                  className="p-3 border border-border rounded-lg cursor-pointer hover:border-bits-blue hover:bg-bits-light-blue/10 transition-colors mb-2"
                >
                  <h4 className="text-xs font-semibold text-foreground mb-0">
                    {credential.title}
                  </h4>
                  <p className="text-xs text-muted-foreground mb-0">
                    {credential.email}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Password: {credential.password}
                  </p>
                </div>
              ))}
              <div className="mt-2 p-3 bg-muted rounded-lg">
                <p className="text-xs text-muted-foreground">
                  <strong>Tip:</strong> Click any credential above to auto-fill
                  the login form, or use any password with the listed emails.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}


export default LoginForm