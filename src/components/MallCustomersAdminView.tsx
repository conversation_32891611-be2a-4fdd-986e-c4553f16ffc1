import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Textarea } from "./ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import { Switch } from "./ui/switch";
import { Separator } from "./ui/separator";
import { Alert, AlertDescription, AlertTitle } from "./ui/alert";
import {
  Settings,
  Upload,
  FileText,
  Palette,
  Database,
  Save,
  Eye,
  Plus,
  Trash2,
  Edit,
  CheckCircle,
  AlertCircle,
  Info,
} from "lucide-react";

export default function MallCustomersAdminView() {
  const [activeTab, setActiveTab] = useState("setup");
  const [projectSettings, setProjectSettings] = useState({
    title: "Mall Customers Analysis Project",
    description:
      "Multi-part data science project focusing on customer segmentation and prediction",
    dueDate: "2025-02-15",
    maxAttempts: 3,
    allowLateSubmissions: true,
    latePenalty: 10,
    autoGrading: true,
    peerReview: false,
  });

  const [rubricCriteria, setRubricCriteria] = useState([
    {
      id: 1,
      name: "K-Means Implementation",
      description: "Custom implementation of k-means clustering algorithm",
      maxPoints: 25,
      levels: [
        {
          name: "Excellent",
          points: 25,
          description: "Complete, efficient, well-documented implementation",
        },
        {
          name: "Good",
          points: 20,
          description: "Working implementation with minor issues",
        },
        {
          name: "Satisfactory",
          points: 15,
          description: "Basic implementation with some problems",
        },
        {
          name: "Needs Improvement",
          points: 10,
          description: "Incomplete or incorrect implementation",
        },
      ],
    },
    {
      id: 2,
      name: "Data Preprocessing",
      description: "Proper data cleaning and preprocessing techniques",
      maxPoints: 15,
      levels: [
        {
          name: "Excellent",
          points: 15,
          description: "Comprehensive preprocessing with justification",
        },
        {
          name: "Good",
          points: 12,
          description: "Good preprocessing with minor gaps",
        },
        {
          name: "Satisfactory",
          points: 9,
          description: "Basic preprocessing applied",
        },
        {
          name: "Needs Improvement",
          points: 6,
          description: "Minimal or incorrect preprocessing",
        },
      ],
    },
    {
      id: 3,
      name: "Cluster Analysis & Visualization",
      description: "Quality of cluster labeling and visualization techniques",
      maxPoints: 20,
      levels: [
        {
          name: "Excellent",
          points: 20,
          description:
            "Clear, insightful visualizations with strong justification",
        },
        {
          name: "Good",
          points: 16,
          description: "Good visualizations with adequate explanation",
        },
        {
          name: "Satisfactory",
          points: 12,
          description: "Basic visualizations with limited insight",
        },
        {
          name: "Needs Improvement",
          points: 8,
          description: "Poor or missing visualizations",
        },
      ],
    },
    {
      id: 4,
      name: "kNN Implementation",
      description: "Implementation of k-nearest neighbors algorithm",
      maxPoints: 20,
      levels: [
        {
          name: "Excellent",
          points: 20,
          description: "Accurate implementation with proper evaluation",
        },
        {
          name: "Good",
          points: 16,
          description: "Working implementation with minor issues",
        },
        {
          name: "Satisfactory",
          points: 12,
          description: "Basic implementation with some errors",
        },
        {
          name: "Needs Improvement",
          points: 8,
          description: "Incorrect or incomplete implementation",
        },
      ],
    },
    {
      id: 5,
      name: "Documentation & Code Quality",
      description: "Code documentation, comments, and overall presentation",
      maxPoints: 20,
      levels: [
        {
          name: "Excellent",
          points: 20,
          description: "Exceptional documentation and clean code",
        },
        {
          name: "Good",
          points: 16,
          description: "Good documentation with clear explanations",
        },
        {
          name: "Satisfactory",
          points: 12,
          description: "Basic documentation present",
        },
        {
          name: "Needs Improvement",
          points: 8,
          description: "Poor or missing documentation",
        },
      ],
    },
  ]);

  const [gradeMapping, setGradeMapping] = useState({
    totalPoints: 100,
    gradeScale: [
      { letter: "A+", minPercent: 97, maxPercent: 100 },
      { letter: "A", minPercent: 93, maxPercent: 96 },
      { letter: "A-", minPercent: 90, maxPercent: 92 },
      { letter: "B+", minPercent: 87, maxPercent: 89 },
      { letter: "B", minPercent: 83, maxPercent: 86 },
      { letter: "B-", minPercent: 80, maxPercent: 82 },
      { letter: "C+", minPercent: 77, maxPercent: 79 },
      { letter: "C", minPercent: 73, maxPercent: 76 },
      { letter: "C-", minPercent: 70, maxPercent: 72 },
      { letter: "D", minPercent: 60, maxPercent: 69 },
      { letter: "F", minPercent: 0, maxPercent: 59 },
    ],
  });

  const sandboxTemplates = [
    {
      id: 1,
      name: "Python Data Science Template",
      description:
        "Pre-configured environment with pandas, numpy, scikit-learn, matplotlib",
      libraries: ["pandas", "numpy", "scikit-learn", "matplotlib", "seaborn"],
      selected: true,
    },
    {
      id: 2,
      name: "Jupyter Notebook Template",
      description: "Interactive notebook environment with data science stack",
      libraries: ["jupyter", "pandas", "numpy", "scikit-learn", "plotly"],
      selected: false,
    },
    {
      id: 3,
      name: "Machine Learning Template",
      description:
        "Advanced ML environment with clustering and classification tools",
      libraries: ["scikit-learn", "pandas", "numpy", "matplotlib", "scipy"],
      selected: false,
    },
  ];

  const handleSaveProject = () => {
    // Save project configuration
    console.log("Saving project configuration:", projectSettings);
  };

  const handleUploadRubric = () => {
    // Handle rubric file upload
    console.log("Uploading rubric file");
  };

  const addRubricCriterion = () => {
    const newCriterion = {
      id: rubricCriteria.length + 1,
      name: "New Criterion",
      description: "Description for new criterion",
      maxPoints: 10,
      levels: [
        { name: "Excellent", points: 10, description: "Excellent work" },
        { name: "Good", points: 8, description: "Good work" },
        { name: "Satisfactory", points: 6, description: "Satisfactory work" },
        {
          name: "Needs Improvement",
          points: 4,
          description: "Needs improvement",
        },
      ],
    };
    setRubricCriteria([...rubricCriteria, newCriterion]);
  };

  return (
    <Card className="border-bits-blue">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Settings className="h-5 w-5 mr-2 text-bits-blue" />
          Admin Configuration Panel
        </CardTitle>
        <CardDescription>
          Configure project settings, rubrics, templates, and grading parameters
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="setup">Project Setup</TabsTrigger>
            <TabsTrigger value="rubric">Rubric Editor</TabsTrigger>
            <TabsTrigger value="templates">Sandbox Templates</TabsTrigger>
            <TabsTrigger value="grading">Grade Mapping</TabsTrigger>
          </TabsList>

          {/* Project Setup Tab */}
          <TabsContent value="setup" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Basic Project Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Project Title</Label>
                    <Input
                      id="title"
                      value={projectSettings.title}
                      onChange={(e) =>
                        setProjectSettings({
                          ...projectSettings,
                          title: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dueDate">Due Date</Label>
                    <Input
                      id="dueDate"
                      type="date"
                      value={projectSettings.dueDate}
                      onChange={(e) =>
                        setProjectSettings({
                          ...projectSettings,
                          dueDate: e.target.value,
                        })
                      }
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Project Description</Label>
                  <Textarea
                    id="description"
                    value={projectSettings.description}
                    onChange={(e) =>
                      setProjectSettings({
                        ...projectSettings,
                        description: e.target.value,
                      })
                    }
                    rows={3}
                  />
                </div>

                <Separator />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="maxAttempts">Max Attempts</Label>
                    <Input
                      id="maxAttempts"
                      type="number"
                      value={projectSettings.maxAttempts}
                      onChange={(e) =>
                        setProjectSettings({
                          ...projectSettings,
                          maxAttempts: parseInt(e.target.value),
                        })
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="latePenalty">Late Penalty (%)</Label>
                    <Input
                      id="latePenalty"
                      type="number"
                      value={projectSettings.latePenalty}
                      onChange={(e) =>
                        setProjectSettings({
                          ...projectSettings,
                          latePenalty: parseInt(e.target.value),
                        })
                      }
                    />
                  </div>
                  <div className="space-y-4 pt-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="allowLate"
                        checked={projectSettings.allowLateSubmissions}
                        onCheckedChange={(checked) =>
                          setProjectSettings({
                            ...projectSettings,
                            allowLateSubmissions: checked,
                          })
                        }
                      />
                      <Label htmlFor="allowLate">Allow Late Submissions</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="autoGrading"
                        checked={projectSettings.autoGrading}
                        onCheckedChange={(checked) =>
                          setProjectSettings({
                            ...projectSettings,
                            autoGrading: checked,
                          })
                        }
                      />
                      <Label htmlFor="autoGrading">Enable Auto-Grading</Label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end space-x-2">
              <Button variant="outline">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              <Button
                onClick={handleSaveProject}
                className="bg-bits-blue hover:bg-bits-blue/90"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Configuration
              </Button>
            </div>
          </TabsContent>

          {/* Rubric Editor Tab */}
          <TabsContent value="rubric" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium">Grading Rubric</h3>
                <p className="text-sm text-muted-foreground">
                  Define criteria and scoring levels for the project
                </p>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" onClick={handleUploadRubric}>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Rubric
                </Button>
                <Button onClick={addRubricCriterion}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Criterion
                </Button>
              </div>
            </div>

            <div className="space-y-4">
              {rubricCriteria.map((criterion, index) => (
                <Card key={criterion.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-base">
                          {criterion.name}
                        </CardTitle>
                        <CardDescription>
                          {criterion.description}
                        </CardDescription>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">
                          {criterion.maxPoints} pts
                        </Badge>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4 text-bits-red" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                      {criterion.levels.map((level, levelIndex) => (
                        <div key={levelIndex} className="p-3 border rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium text-sm">
                              {level.name}
                            </span>
                            <Badge variant="secondary" className="text-xs">
                              {level.points} pts
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {level.description}
                          </p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>
                Total Points:{" "}
                {rubricCriteria.reduce(
                  (sum, criterion) => sum + criterion.maxPoints,
                  0
                )}
              </AlertTitle>
              <AlertDescription>
                This rubric will be used for both auto-grading and manual
                evaluation by instructors and TAs.
              </AlertDescription>
            </Alert>
          </TabsContent>

          {/* Sandbox Templates Tab */}
          <TabsContent value="templates" className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">
                Sandbox Environment Templates
              </h3>
              <p className="text-sm text-muted-foreground mb-6">
                Select and configure the coding environment that students will
                use for the project
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {sandboxTemplates.map((template) => (
                <Card
                  key={template.id}
                  className={`cursor-pointer transition-all ${
                    template.selected
                      ? "border-bits-blue bg-bits-blue/10"
                      : "hover:border-gray-300"
                  }`}
                >
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">
                        {template.name}
                      </CardTitle>
                      {template.selected && (
                        <CheckCircle className="h-5 w-5 text-bits-blue" />
                      )}
                    </div>
                    <CardDescription>{template.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm font-medium">
                          Included Libraries:
                        </Label>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {template.libraries.map((lib, idx) => (
                            <Badge
                              key={idx}
                              variant="secondary"
                              className="text-xs"
                            >
                              {lib}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <Button
                        variant={template.selected ? "default" : "outline"}
                        size="sm"
                        className="w-full"
                      >
                        {template.selected ? "Selected" : "Select Template"}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Template Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Memory Limit (MB)</Label>
                    <Input type="number" defaultValue="2048" />
                  </div>
                  <div className="space-y-2">
                    <Label>Time Limit (minutes)</Label>
                    <Input type="number" defaultValue="120" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Additional Packages</Label>
                  <Textarea
                    placeholder="Enter additional Python packages (one per line)"
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Grade Mapping Tab */}
          <TabsContent value="grading" className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">
                Grade Scale Configuration
              </h3>
              <p className="text-sm text-muted-foreground mb-6">
                Configure how numerical scores translate to letter grades
              </p>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Grade Scale</CardTitle>
                <CardDescription>
                  Total Points: {gradeMapping.totalPoints} | Configure
                  percentage ranges for each letter grade
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {gradeMapping.gradeScale.map((grade, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-4 p-3 border rounded-lg"
                    >
                      <div className="w-12">
                        <Badge variant="outline" className="font-medium">
                          {grade.letter}
                        </Badge>
                      </div>
                      <div className="flex-1 grid grid-cols-2 gap-2">
                        <div className="flex items-center space-x-2">
                          <Label className="text-sm">Min:</Label>
                          <Input
                            type="number"
                            value={grade.minPercent}
                            className="w-20"
                            min="0"
                            max="100"
                            onChange={(e) => {
                              const newScale = [...gradeMapping.gradeScale];
                              newScale[index].minPercent = parseInt(
                                e.target.value
                              );
                              setGradeMapping({
                                ...gradeMapping,
                                gradeScale: newScale,
                              });
                            }}
                          />
                          <span className="text-sm text-muted-foreground">
                            %
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Label className="text-sm">Max:</Label>
                          <Input
                            type="number"
                            value={grade.maxPercent}
                            className="w-20"
                            min="0"
                            max="100"
                            onChange={(e) => {
                              const newScale = [...gradeMapping.gradeScale];
                              newScale[index].maxPercent = parseInt(
                                e.target.value
                              );
                              setGradeMapping({
                                ...gradeMapping,
                                gradeScale: newScale,
                              });
                            }}
                          />
                          <span className="text-sm text-muted-foreground">
                            %
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Grade Book Integration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Grade Column Name</Label>
                    <Input defaultValue="Mall Customers Project" />
                  </div>
                  <div className="space-y-2">
                    <Label>Category</Label>
                    <Select defaultValue="projects">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="projects">Projects</SelectItem>
                        <SelectItem value="assignments">Assignments</SelectItem>
                        <SelectItem value="exams">Exams</SelectItem>
                        <SelectItem value="participation">
                          Participation
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="autoSync" defaultChecked />
                  <Label htmlFor="autoSync">
                    Automatically sync grades to gradebook
                  </Label>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
