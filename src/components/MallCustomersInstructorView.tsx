import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./ui/card";
import { But<PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Textarea } from "./ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import { Progress } from "./ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./ui/table";
import {
  BookOpen,
  Users,
  FileText,
  BarChart3,
  Eye,
  Edit,
  MessageSquare,
  CheckCircle,
  Clock,
  AlertTriangle,
  Download,
  Send,
  Star,
  TrendingUp,
} from "lucide-react";
import { useNavigation } from "../App";

export default function MallCustomersInstructorView() {
  const { navigateTo } = useNavigation();
  const [activeTab, setActiveTab] = useState("overview");

  // Mock data for instructor view
  const projectStats = {
    totalStudents: 45,
    submitted: 32,
    inProgress: 8,
    notStarted: 5,
    averageScore: 78.5,
    completionRate: 71,
  };

  const recentSubmissions = [
    {
      id: 1,
      studentName: "Rahul Sharma",
      studentId: "2023001",
      submittedAt: "2025-01-13T14:30:00Z",
      status: "submitted",
      score: 85,
      parts: { part1: "completed", part2: "completed", part3: "in-progress" },
    },
    {
      id: 2,
      studentName: "Priya Patel",
      studentId: "2023002",
      submittedAt: "2025-01-13T12:15:00Z",
      status: "submitted",
      score: 92,
      parts: { part1: "completed", part2: "completed", part3: "completed" },
    },
    {
      id: 3,
      studentName: "Arjun Kumar",
      studentId: "2023003",
      submittedAt: "2025-01-13T10:45:00Z",
      status: "needs-review",
      score: null,
      parts: { part1: "completed", part2: "completed", part3: "completed" },
    },
    {
      id: 4,
      studentName: "Sneha Reddy",
      studentId: "2023004",
      submittedAt: "2025-01-13T09:20:00Z",
      status: "graded",
      score: 88,
      parts: { part1: "completed", part2: "completed", part3: "completed" },
    },
  ];

  const gradingQueue = [
    {
      id: 1,
      studentName: "Arjun Kumar",
      submittedAt: "2025-01-13T10:45:00Z",
      priority: "high",
      parts: ["Part 2", "Part 3"],
      estimatedTime: "15 min",
    },
    {
      id: 2,
      studentName: "Kavya Singh",
      submittedAt: "2025-01-13T08:30:00Z",
      priority: "medium",
      parts: ["Part 3"],
      estimatedTime: "10 min",
    },
    {
      id: 3,
      studentName: "Dev Mehta",
      submittedAt: "2025-01-12T16:45:00Z",
      priority: "low",
      parts: ["Part 1"],
      estimatedTime: "12 min",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "submitted":
        return "bg-blue-100 text-blue-800";
      case "graded":
        return "bg-green-100 text-green-800";
      case "needs-review":
        return "bg-orange-100 text-orange-800";
      case "late":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPartStatus = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "in-progress":
        return <Clock className="h-4 w-4 text-orange-600" />;
      case "not-started":
        return <AlertTriangle className="h-4 w-4 text-gray-400" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const handleGradeSubmission = (submissionId: number) => {
    navigateTo("grading", { submissionId, projectType: "mall-customers" });
  };

  const handleViewSubmission = (submissionId: number) => {
    navigateTo("submission-details", { submissionId });
  };

  return (
    <Card className="border-bits-gold">
      <CardHeader>
        <CardTitle className="flex items-center">
          <BookOpen className="h-5 w-5 mr-2 text-bits-gold" />
          Instructor Dashboard
        </CardTitle>
        <CardDescription>
          Manage project creation, monitor submissions, and coordinate grading
          activities
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Project Overview</TabsTrigger>
            <TabsTrigger value="submissions">Submissions</TabsTrigger>
            <TabsTrigger value="grading">Grading Workspace</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Project Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-bits-blue">
                      {projectStats.totalStudents}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Total Students
                    </p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">
                      {projectStats.submitted}
                    </p>
                    <p className="text-xs text-muted-foreground">Submitted</p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-orange-600">
                      {projectStats.inProgress}
                    </p>
                    <p className="text-xs text-muted-foreground">In Progress</p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-600">
                      {projectStats.notStarted}
                    </p>
                    <p className="text-xs text-muted-foreground">Not Started</p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-bits-gold">
                      {projectStats.averageScore}
                    </p>
                    <p className="text-xs text-muted-foreground">Avg Score</p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-purple-600">
                      {projectStats.completionRate}%
                    </p>
                    <p className="text-xs text-muted-foreground">Completion</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Project Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Project Progress Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Part 1: K-Means Clustering</span>
                      <span>38/45 completed (84%)</span>
                    </div>
                    <Progress value={84} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Part 2: Cluster Labeling</span>
                      <span>35/45 completed (78%)</span>
                    </div>
                    <Progress value={78} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Part 3: kNN Prediction</span>
                      <span>28/45 completed (62%)</span>
                    </div>
                    <Progress value={62} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">
                    Create Announcement
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Important project update..."
                    rows={3}
                    className="mb-3"
                  />
                  <Button size="sm" className="w-full">
                    <Send className="h-4 w-4 mr-2" />
                    Send to All Students
                  </Button>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Export Grades</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-3">
                    Download current grades for external systems
                  </p>
                  <Button variant="outline" size="sm" className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    Export CSV
                  </Button>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">View Analytics</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-3">
                    Detailed performance analytics and insights
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => setActiveTab("analytics")}
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    View Reports
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Submissions Tab */}
          <TabsContent value="submissions" className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Recent Submissions</h3>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export All
                </Button>
                <Select defaultValue="all">
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Submissions</SelectItem>
                    <SelectItem value="submitted">Submitted</SelectItem>
                    <SelectItem value="needs-review">Needs Review</SelectItem>
                    <SelectItem value="graded">Graded</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Submitted At</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Parts Progress</TableHead>
                      <TableHead>Score</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentSubmissions.map((submission) => (
                      <TableRow key={submission.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">
                              {submission.studentName}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {submission.studentId}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <p className="text-sm">
                            {new Date(
                              submission.submittedAt
                            ).toLocaleDateString()}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(
                              submission.submittedAt
                            ).toLocaleTimeString()}
                          </p>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(submission.status)}>
                            {submission.status.replace("-", " ")}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            <div className="flex items-center space-x-1">
                              {getPartStatus(submission.parts.part1)}
                              <span className="text-xs">P1</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              {getPartStatus(submission.parts.part2)}
                              <span className="text-xs">P2</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              {getPartStatus(submission.parts.part3)}
                              <span className="text-xs">P3</span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {submission.score ? (
                            <div className="flex items-center space-x-1">
                              <Star className="h-4 w-4 text-yellow-500" />
                              <span className="font-medium">
                                {submission.score}/100
                              </span>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">
                              Pending
                            </span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                handleViewSubmission(submission.id)
                              }
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                handleGradeSubmission(submission.id)
                              }
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <MessageSquare className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Grading Workspace Tab */}
          <TabsContent value="grading" className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Grading Queue</h3>
              <Button onClick={() => navigateTo("grading")}>
                <Edit className="h-4 w-4 mr-2" />
                Open Grading Interface
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-orange-600">12</p>
                    <p className="text-sm text-muted-foreground">
                      Pending Reviews
                    </p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">45 min</p>
                    <p className="text-sm text-muted-foreground">
                      Est. Time Remaining
                    </p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">28</p>
                    <p className="text-sm text-muted-foreground">
                      Completed Today
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Priority Grading Queue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {gradingQueue.map((item) => (
                    <div
                      key={item.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <div>
                            <p className="font-medium">{item.studentName}</p>
                            <p className="text-sm text-muted-foreground">
                              Submitted{" "}
                              {new Date(item.submittedAt).toLocaleDateString()}
                            </p>
                          </div>
                          <Badge
                            variant={
                              item.priority === "high"
                                ? "destructive"
                                : item.priority === "medium"
                                ? "default"
                                : "secondary"
                            }
                          >
                            {item.priority} priority
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2 mt-2">
                          <span className="text-sm text-muted-foreground">
                            Needs review:
                          </span>
                          {item.parts.map((part, idx) => (
                            <Badge
                              key={idx}
                              variant="outline"
                              className="text-xs"
                            >
                              {part}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground">
                          {item.estimatedTime}
                        </span>
                        <Button
                          size="sm"
                          onClick={() => handleGradeSubmission(item.id)}
                        >
                          Grade Now
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Score Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      { range: "90-100", count: 8, percentage: 18 },
                      { range: "80-89", count: 15, percentage: 33 },
                      { range: "70-79", count: 12, percentage: 27 },
                      { range: "60-69", count: 7, percentage: 16 },
                      { range: "0-59", count: 3, percentage: 7 },
                    ].map((item, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <div className="w-16 text-sm text-muted-foreground">
                          {item.range}
                        </div>
                        <div className="flex-1">
                          <Progress value={item.percentage} className="h-2" />
                        </div>
                        <div className="w-12 text-sm text-right">
                          {item.count}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Part Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Part 1: K-Means (avg: 22.3/25)</span>
                        <span>89%</span>
                      </div>
                      <Progress value={89} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Part 2: Labeling (avg: 13.1/15)</span>
                        <span>87%</span>
                      </div>
                      <Progress value={87} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Part 3: kNN (avg: 15.2/20)</span>
                        <span>76%</span>
                      </div>
                      <Progress value={76} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Documentation (avg: 16.8/20)</span>
                        <span>84%</span>
                      </div>
                      <Progress value={84} className="h-2" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Class Performance Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 bg-green-50 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <TrendingUp className="h-5 w-5 text-green-600" />
                      <span className="font-medium text-green-800">
                        Strong Areas
                      </span>
                    </div>
                    <ul className="text-sm text-green-700 space-y-1">
                      <li>• K-means implementation quality</li>
                      <li>• Data preprocessing techniques</li>
                      <li>• Code documentation</li>
                    </ul>
                  </div>

                  <div className="p-4 bg-orange-50 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <AlertTriangle className="h-5 w-5 text-orange-600" />
                      <span className="font-medium text-orange-800">
                        Areas for Improvement
                      </span>
                    </div>
                    <ul className="text-sm text-orange-700 space-y-1">
                      <li>• kNN algorithm accuracy</li>
                      <li>• Visualization techniques</li>
                      <li>• Statistical analysis depth</li>
                    </ul>
                  </div>

                  <div className="p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <Users className="h-5 w-5 text-blue-600" />
                      <span className="font-medium text-blue-800">
                        Student Engagement
                      </span>
                    </div>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li>• 92% completion rate Part 1</li>
                      <li>• 15 office hours visits</li>
                      <li>• 8 forum discussions active</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
