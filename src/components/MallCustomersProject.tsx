import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "./ui/tabs";
import {
  Users,
  Target,
  Brain,
  BarChart3,
  Database,
  ArrowRight,
  CheckCircle,
  Clock,
  AlertCircle,
} from "lucide-react";
// import { useAuth, useNavigation } from "../App";
import useAuth from "./Context/AuthContext";
import useNavigation from "./Context/NavigationContext";
import MallCustomersAdminView from "./MallCustomersAdminView";
import MallCustomersInstructorView from "./MallCustomersInstructorView";
import MallCustomersTAView from "./MallCustomersTAView";
import MallCustomersStudentView from "./MallCustomersStudentView";

export default function MallCustomersProject() {
  const { user } = useAuth();
  const { navigateTo } = useNavigation();

  // Project overview data
  const projectParts = [
    {
      id: 1,
      title: "K-Means Clustering",
      description:
        "Implement unsupervised learning to segment customers using k-means clustering",
      objectives: [
        "Preprocess Mall_Customers.csv dataset",
        "Implement custom k-means clustering",
        "Output cluster numbers, centroids, and record counts",
        "Demonstrate customer segmentation",
      ],
      deliverables: [
        "Python implementation",
        "Cluster analysis",
        "Results documentation",
      ],
      status: "active",
    },
    {
      id: 2,
      title: "Cluster Labeling & Visualization",
      description:
        "Apply meaningful labels to clusters and justify with data visualization",
      objectives: [
        "Analyze cluster characteristics",
        "Apply appropriate labels to each cluster",
        "Create data visualizations to justify labeling",
        "Document clustering logic and nomenclature",
      ],
      deliverables: [
        "Labeled dataset",
        "Visualization plots",
        "Justification report",
      ],
      status: "pending",
    },
    {
      id: 3,
      title: "kNN Prediction",
      description:
        "Build kNN classifier to predict customer segments for unknown records",
      objectives: [
        "Use labeled dataset from Part 2",
        "Implement kNN algorithm for prediction",
        "Generate confusion matrix and accuracy metrics",
        "Demonstrate similarity-based prediction with majority voting",
      ],
      deliverables: [
        "kNN implementation",
        "Confusion matrix",
        "Prediction system",
      ],
      status: "locked",
    },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <Clock className="h-4 w-4 text-blue-600" />;
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "pending":
        return <AlertCircle className="h-4 w-4 text-orange-600" />;
      case "locked":
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "locked":
        return "bg-gray-100 text-gray-600 border-gray-200";
      default:
        return "bg-gray-100 text-gray-600 border-gray-200";
    }
  };

  // Render role-specific view
  const renderRoleSpecificView = () => {
    switch (user?.role) {
      case "admin":
        return <MallCustomersAdminView />;
      case "instructor":
        return <MallCustomersInstructorView />;
      case "ta":
        return <MallCustomersTAView />;
      case "student":
      default:
        return <MallCustomersStudentView />;
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-bits-blue">
            Mall Customers Analysis Project
          </h1>
          <p className="text-muted-foreground mt-2">
            Multi-part data science project focusing on customer segmentation
            and prediction
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-bits-blue border-bits-blue">
            <Database className="h-3 w-3 mr-1" />
            Mall_Customers.csv
          </Badge>
          <Badge variant="outline" className="text-bits-gold border-bits-gold">
            Machine Learning
          </Badge>
        </div>
      </div>

      {/* Project Overview */}
      <Card className="border-bits-blue">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="h-5 w-5 mr-2 text-bits-blue" />
            Project Overview
          </CardTitle>
          <CardDescription>
            A comprehensive data science project exploring unsupervised and
            supervised learning techniques for customer segmentation and
            prediction using the Mall Customers dataset.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {projectParts.map((part, index) => (
              <Card
                key={part.id}
                className={`border-l-4 ${
                  part.status === "active"
                    ? "border-bits-blue"
                    : part.status === "completed"
                    ? "border-bits-gold"
                    : part.status === "pending"
                    ? "border-bits-gold"
                    : "border-border"
                }`}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Part {part.id}</CardTitle>
                    <Badge className={`${getStatusColor(part.status)} border`}>
                      {getStatusIcon(part.status)}
                      <span className="ml-1 capitalize">{part.status}</span>
                    </Badge>
                  </div>
                  <CardDescription className="font-medium">
                    {part.title}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    {part.description}
                  </p>

                  <div>
                    <h4 className="font-medium text-sm mb-2">
                      Key Objectives:
                    </h4>
                    <ul className="text-xs space-y-1">
                      {part.objectives.map((objective, idx) => (
                        <li key={idx} className="flex items-start">
                          <span className="text-bits-blue mr-1">•</span>
                          <span>{objective}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-medium text-sm mb-2">Deliverables:</h4>
                    <div className="flex flex-wrap gap-1">
                      {part.deliverables.map((deliverable, idx) => (
                        <Badge
                          key={idx}
                          variant="secondary"
                          className="text-xs"
                        >
                          {deliverable}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Dataset Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="h-5 w-5 mr-2 text-bits-gold" />
            Dataset Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">Mall_Customers.csv</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Records:</span>
                  <span className="font-medium">200 customers</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Features:</span>
                  <span className="font-medium">5 attributes</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">File Size:</span>
                  <span className="font-medium">4.2 KB</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Type:</span>
                  <span className="font-medium">Clean, structured data</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Data Attributes</h4>
              <div className="space-y-2">
                {[
                  {
                    name: "CustomerID",
                    type: "Integer",
                    desc: "Unique identifier",
                  },
                  { name: "Gender", type: "Categorical", desc: "Male/Female" },
                  { name: "Age", type: "Numeric", desc: "Customer age" },
                  {
                    name: "Annual Income",
                    type: "Numeric",
                    desc: "Income in thousands",
                  },
                  {
                    name: "Spending Score",
                    type: "Numeric",
                    desc: "Score 1-100",
                  },
                ].map((attr, idx) => (
                  <div
                    key={idx}
                    className="flex items-center space-x-2 text-sm"
                  >
                    <Badge variant="outline" className="text-xs min-w-16">
                      {attr.type}
                    </Badge>
                    <span className="font-medium">{attr.name}</span>
                    <span className="text-muted-foreground">- {attr.desc}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Role-Specific Interface */}
      {renderRoleSpecificView()}
    </div>
  );
}
