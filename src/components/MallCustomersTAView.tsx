import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Textarea } from "./ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import { Progress } from "./ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./ui/table";
import { Slider } from "./ui/slider";
import { Separator } from "./ui/separator";
import {
  GraduationCap,
  Cpu,
  FileText,
  MessageSquare,
  RotateCcw,
  CheckCircle,
  Clock,
  AlertCircle,
  Star,
  Send,
  Eye,
  Edit,
  PlayCircle,
  PauseCircle,
  RefreshCw,
  TrendingUp,
  Users,
  Target,
} from "lucide-react";

export default function MallCustomersTAView() {
  const [activeTab, setActiveTab] = useState("auto-grading");
  const [selectedSubmission, setSelectedSubmission] = useState(null);
  const [gradingCriteria, setGradingCriteria] = useState({});
  const [feedbackText, setFeedbackText] = useState("");

  // Mock data for TA interface
  const autoGradingQueue = [
    {
      id: 1,
      studentName: "Rahul Sharma",
      studentId: "2023001",
      submittedAt: "2025-01-13T14:30:00Z",
      priority: "high",
      autoGradeStatus: "pending",
      estimatedTime: "3 min",
      parts: ["Part 1", "Part 2"],
    },
    {
      id: 2,
      studentName: "Priya Patel",
      studentId: "2023002",
      submittedAt: "2025-01-13T12:15:00Z",
      priority: "medium",
      autoGradeStatus: "running",
      estimatedTime: "2 min",
      parts: ["Part 3"],
    },
    {
      id: 3,
      studentName: "Arjun Kumar",
      studentId: "2023003",
      submittedAt: "2025-01-13T10:45:00Z",
      priority: "low",
      autoGradeStatus: "completed",
      estimatedTime: "0 min",
      parts: ["Part 1", "Part 2", "Part 3"],
      autoScore: 78,
    },
  ];

  const resubmissions = [
    {
      id: 1,
      studentName: "Dev Mehta",
      studentId: "2023004",
      originalSubmission: "2025-01-10T14:00:00Z",
      resubmittedAt: "2025-01-13T16:20:00Z",
      attempt: 2,
      maxAttempts: 3,
      reason: "Clustering algorithm optimization",
      previousScore: 65,
      status: "pending-review",
    },
    {
      id: 2,
      studentName: "Kavya Singh",
      studentId: "2023005",
      originalSubmission: "2025-01-11T09:30:00Z",
      resubmittedAt: "2025-01-13T11:45:00Z",
      attempt: 2,
      maxAttempts: 3,
      reason: "Improved visualization and analysis",
      previousScore: 72,
      status: "graded",
      newScore: 85,
    },
  ];

  const rubricCriteria = [
    {
      id: 1,
      name: "K-Means Implementation",
      maxPoints: 25,
      currentScore: 0,
      levels: [
        {
          name: "Excellent",
          points: 25,
          description: "Complete, efficient implementation",
        },
        { name: "Good", points: 20, description: "Working with minor issues" },
        {
          name: "Satisfactory",
          points: 15,
          description: "Basic implementation",
        },
        {
          name: "Needs Improvement",
          points: 10,
          description: "Incomplete/incorrect",
        },
      ],
    },
    {
      id: 2,
      name: "Data Preprocessing",
      maxPoints: 15,
      currentScore: 0,
      levels: [
        {
          name: "Excellent",
          points: 15,
          description: "Comprehensive preprocessing",
        },
        { name: "Good", points: 12, description: "Good preprocessing" },
        { name: "Satisfactory", points: 9, description: "Basic preprocessing" },
        {
          name: "Needs Improvement",
          points: 6,
          description: "Minimal preprocessing",
        },
      ],
    },
    {
      id: 3,
      name: "Visualization & Analysis",
      maxPoints: 20,
      currentScore: 0,
      levels: [
        {
          name: "Excellent",
          points: 20,
          description: "Clear, insightful visualizations",
        },
        { name: "Good", points: 16, description: "Good visualizations" },
        {
          name: "Satisfactory",
          points: 12,
          description: "Basic visualizations",
        },
        {
          name: "Needs Improvement",
          points: 8,
          description: "Poor visualizations",
        },
      ],
    },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-orange-600" />;
      case "running":
        return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />;
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "failed":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const handleStartAutoGrading = (submissionId: number) => {
    console.log("Starting auto-grading for submission:", submissionId);
  };

  const handleScoreUpdate = (criteriaId: number, score: number) => {
    setGradingCriteria((prev) => ({
      ...prev,
      [criteriaId]: score,
    }));
  };

  const handleSendFeedback = () => {
    console.log("Sending feedback:", feedbackText);
    setFeedbackText("");
  };

  return (
    <Card className="border-purple-500">
      <CardHeader>
        <CardTitle className="flex items-center">
          <GraduationCap className="h-5 w-5 mr-2 text-purple-600" />
          Teaching Assistant Dashboard
        </CardTitle>
        <CardDescription>
          Manage auto-grading queue, score submissions using rubrics, and
          provide student feedback
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="auto-grading">Auto-Grading Queue</TabsTrigger>
            <TabsTrigger value="rubric-scoring">Rubric Scoring</TabsTrigger>
            <TabsTrigger value="feedback">Feedback Center</TabsTrigger>
            <TabsTrigger value="resubmissions">Resubmissions</TabsTrigger>
          </TabsList>

          {/* Auto-Grading Queue Tab */}
          <TabsContent value="auto-grading" className="space-y-6">
            {/* Queue Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-bits-blue">12</p>
                    <p className="text-xs text-muted-foreground">Pending</p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-orange-600">3</p>
                    <p className="text-xs text-muted-foreground">Running</p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">28</p>
                    <p className="text-xs text-muted-foreground">Completed</p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-purple-600">15 min</p>
                    <p className="text-xs text-muted-foreground">Est. Time</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Auto-Grading Controls */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Cpu className="h-5 w-5 mr-2" />
                    Auto-Grading Engine
                  </CardTitle>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      <PauseCircle className="h-4 w-4 mr-2" />
                      Pause Queue
                    </Button>
                    <Button
                      size="sm"
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <PlayCircle className="h-4 w-4 mr-2" />
                      Start All
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Submitted At</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Parts</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Est. Time</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {autoGradingQueue.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{item.studentName}</p>
                            <p className="text-sm text-muted-foreground">
                              {item.studentId}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <p className="text-sm">
                            {new Date(item.submittedAt).toLocaleDateString()}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(item.submittedAt).toLocaleTimeString()}
                          </p>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              item.priority === "high"
                                ? "destructive"
                                : item.priority === "medium"
                                ? "default"
                                : "secondary"
                            }
                          >
                            {item.priority}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {item.parts.map((part, idx) => (
                              <Badge
                                key={idx}
                                variant="outline"
                                className="text-xs"
                              >
                                {part}
                              </Badge>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(item.autoGradeStatus)}
                            <span className="text-sm capitalize">
                              {item.autoGradeStatus}
                            </span>
                            {item.autoScore && (
                              <Badge variant="outline">
                                {item.autoScore}/100
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{item.estimatedTime}</TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            {item.autoGradeStatus === "pending" && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleStartAutoGrading(item.id)}
                              >
                                <PlayCircle className="h-4 w-4" />
                              </Button>
                            )}
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* Auto-Grading Configuration */}
            <Card>
              <CardHeader>
                <CardTitle>Auto-Grading Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Processing Priority</Label>
                    <Select defaultValue="high-first">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high-first">
                          High Priority First
                        </SelectItem>
                        <SelectItem value="fifo">
                          First In, First Out
                        </SelectItem>
                        <SelectItem value="shortest-first">
                          Shortest Jobs First
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Concurrent Jobs</Label>
                    <Select defaultValue="3">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 Job</SelectItem>
                        <SelectItem value="3">3 Jobs</SelectItem>
                        <SelectItem value="5">5 Jobs</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Timeout (minutes)</Label>
                    <Input type="number" defaultValue="10" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Rubric Scoring Tab */}
          <TabsContent value="rubric-scoring" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Student Selection */}
              <Card className="lg:col-span-1">
                <CardHeader>
                  <CardTitle>Select Submission</CardTitle>
                </CardHeader>
                <CardContent>
                  <Select
                    onValueChange={(value) => setSelectedSubmission(value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a submission to grade" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="rahul">
                        Rahul Sharma - 2023001
                      </SelectItem>
                      <SelectItem value="priya">
                        Priya Patel - 2023002
                      </SelectItem>
                      <SelectItem value="arjun">
                        Arjun Kumar - 2023003
                      </SelectItem>
                    </SelectContent>
                  </Select>

                  {selectedSubmission && (
                    <div className="mt-4 space-y-3">
                      <div className="p-3 bg-muted rounded-lg">
                        <p className="font-medium">Submission Details</p>
                        <p className="text-sm text-muted-foreground">
                          Submitted: Jan 13, 2025 2:30 PM
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Auto Score: 78/100
                        </p>
                      </div>
                      <Button variant="outline" size="sm" className="w-full">
                        <Eye className="h-4 w-4 mr-2" />
                        View Code
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Rubric Scoring Panel */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Rubric Scoring Panel</CardTitle>
                  <CardDescription>
                    Score each criterion and provide detailed feedback
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {rubricCriteria.map((criterion) => (
                    <div key={criterion.id} className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{criterion.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            Max: {criterion.maxPoints} points
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold">
                            {gradingCriteria[criterion.id] || 0}/
                            {criterion.maxPoints}
                          </p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>
                          Score: {gradingCriteria[criterion.id] || 0}
                        </Label>
                        <Slider
                          value={[gradingCriteria[criterion.id] || 0]}
                          onValueChange={([value]) =>
                            handleScoreUpdate(criterion.id, value)
                          }
                          max={criterion.maxPoints}
                          step={1}
                          className="w-full"
                        />
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {criterion.levels.map((level, idx) => (
                          <Button
                            key={idx}
                            variant={
                              gradingCriteria[criterion.id] === level.points
                                ? "default"
                                : "outline"
                            }
                            size="sm"
                            className="text-xs p-2 h-auto flex-col"
                            onClick={() =>
                              handleScoreUpdate(criterion.id, level.points)
                            }
                          >
                            <span className="font-medium">{level.name}</span>
                            <span>{level.points} pts</span>
                          </Button>
                        ))}
                      </div>

                      <Textarea
                        placeholder={`Feedback for ${criterion.name}...`}
                        rows={2}
                        className="text-sm"
                      />

                      <Separator />
                    </div>
                  ))}

                  <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                    <div>
                      <p className="font-medium">Total Score</p>
                      <p className="text-sm text-muted-foreground">
                        {Object.values(gradingCriteria).reduce(
                          (sum: number, score: number) => sum + score,
                          0
                        )}{" "}
                        / 100 points
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline">Save Draft</Button>
                      <Button>Submit Grade</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Feedback Center Tab */}
          <TabsContent value="feedback" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Feedback Composer
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Student</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select student" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="rahul">Rahul Sharma</SelectItem>
                        <SelectItem value="priya">Priya Patel</SelectItem>
                        <SelectItem value="arjun">Arjun Kumar</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Feedback Type</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select feedback type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="general">
                          General Feedback
                        </SelectItem>
                        <SelectItem value="improvement">
                          Areas for Improvement
                        </SelectItem>
                        <SelectItem value="excellence">
                          Excellent Work
                        </SelectItem>
                        <SelectItem value="resubmission">
                          Resubmission Required
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Feedback Message</Label>
                  <Textarea
                    value={feedbackText}
                    onChange={(e) => setFeedbackText(e.target.value)}
                    placeholder="Provide detailed feedback on the student's work..."
                    rows={6}
                  />
                </div>

                <div className="flex items-center space-x-4">
                  <Button onClick={handleSendFeedback}>
                    <Send className="h-4 w-4 mr-2" />
                    Send Feedback
                  </Button>
                  <Button variant="outline">Save Template</Button>
                  <Button variant="outline">Load Template</Button>
                </div>
              </CardContent>
            </Card>

            {/* Quick Feedback Templates */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Feedback Templates</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    {
                      title: "Excellent K-Means Implementation",
                      template:
                        "Great work on your k-means clustering implementation! Your code is well-structured and the algorithm is correctly implemented. The cluster visualization clearly shows the customer segments.",
                    },
                    {
                      title: "Improve Data Preprocessing",
                      template:
                        "Your analysis shows good understanding, but consider improving your data preprocessing. Make sure to handle missing values and normalize features before clustering.",
                    },
                    {
                      title: "kNN Accuracy Needs Work",
                      template:
                        "Your kNN implementation is on the right track, but the accuracy could be improved. Consider feature scaling and optimizing the k value through cross-validation.",
                    },
                  ].map((template, idx) => (
                    <Card
                      key={idx}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => setFeedbackText(template.template)}
                    >
                      <CardContent className="pt-4">
                        <h4 className="font-medium text-sm mb-2">
                          {template.title}
                        </h4>
                        <p className="text-xs text-muted-foreground line-clamp-3">
                          {template.template}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Resubmissions Tab */}
          <TabsContent value="resubmissions" className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Resubmission Tracker</h3>
              <div className="flex space-x-2">
                <Badge variant="outline">6 pending</Badge>
                <Badge variant="outline">12 completed</Badge>
              </div>
            </div>

            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Attempt</TableHead>
                      <TableHead>Resubmitted At</TableHead>
                      <TableHead>Reason</TableHead>
                      <TableHead>Previous Score</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {resubmissions.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{item.studentName}</p>
                            <p className="text-sm text-muted-foreground">
                              {item.studentId}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {item.attempt}/{item.maxAttempts}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <p className="text-sm">
                            {new Date(item.resubmittedAt).toLocaleDateString()}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(item.resubmittedAt).toLocaleTimeString()}
                          </p>
                        </TableCell>
                        <TableCell>
                          <p className="text-sm">{item.reason}</p>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span>{item.previousScore}/100</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={
                              item.status === "graded"
                                ? "bg-green-100 text-green-800"
                                : item.status === "pending-review"
                                ? "bg-orange-100 text-orange-800"
                                : "bg-gray-100 text-gray-800"
                            }
                          >
                            {item.status.replace("-", " ")}
                          </Badge>
                          {item.newScore && (
                            <div className="mt-1">
                              <Badge variant="outline">
                                New: {item.newScore}/100
                              </Badge>
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <MessageSquare className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* Resubmission Analytics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-bits-blue">18</p>
                    <p className="text-sm text-muted-foreground">
                      Total Resubmissions
                    </p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">+12.3</p>
                    <p className="text-sm text-muted-foreground">
                      Avg Score Improvement
                    </p>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-purple-600">67%</p>
                    <p className="text-sm text-muted-foreground">
                      Success Rate
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
