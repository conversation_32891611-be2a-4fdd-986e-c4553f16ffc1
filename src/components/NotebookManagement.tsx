import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "./ui/dialog";
import { But<PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Badge } from "./ui/badge";
import {
  Search,
  Filter,
  Upload,
  Plus,
  ArrowLeft,
  FileText,
  Download,
  Copy,
  Eye,
  MoreHorizontal,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { toast } from "sonner";

interface Notebook {
  id: string;
  name: string;
  description: string;
  author: string;
  lastModified: string;
  size: string;
  version: string;
  type: "Project File" | "Template" | "User Created";
  tags: string[];
}

const mockNotebooks: Notebook[] = [
  {
    id: "1",
    name: "assignment_template.ipynb",
    description: "Assignment template with required sections and instructions",
    author: "<PERSON><PERSON> <PERSON><PERSON>",
    lastModified: "2 weeks ago",
    size: "45.2 KB",
    version: "v1",
    type: "Project File",
    tags: ["template", "assignment"],
  },
  {
    id: "2",
    name: "data_exploration_guide.ipynb",
    description: "Step-by-step guide for data exploration and visualization",
    author: "Dr. A. Sharma",
    lastModified: "1 week ago",
    size: "128.5 KB",
    version: "v1",
    type: "Project File",
    tags: ["guide", "reference"],
  },
  {
    id: "3",
    name: "my_analysis.ipynb",
    description:
      "My analysis notebook with data preprocessing and model training",
    author: "Rahul Sharma",
    lastModified: "2 hours ago",
    size: "234.7 KB",
    version: "v3",
    type: "User Created",
    tags: ["analysis", "progress"],
  },
  {
    id: "4",
    name: "model_experiments.ipynb",
    description: "Different model experiments and hyperparameter tuning",
    author: "Rahul Sharma",
    lastModified: "1 day ago",
    size: "185.3 KB",
    version: "v2",
    type: "User Created",
    tags: ["experiment", "models"],
  },
  {
    id: "5",
    name: "final_submission.ipynb",
    description: "Final submission notebook with complete analysis",
    author: "Rahul Sharma",
    lastModified: "3 days ago",
    size: "145.8 KB",
    version: "v1",
    type: "User Created",
    tags: ["final", "submission"],
  },
];

interface NotebookManagementProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onNotebookSelect?: (notebook: Notebook) => void;
  isPage?: boolean;
}

export default function NotebookManagement({
  open,
  onOpenChange,
  onNotebookSelect,
  isPage = false,
}: NotebookManagementProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [notebooks] = useState<Notebook[]>(mockNotebooks);

  const filteredNotebooks = notebooks.filter(
    (notebook) =>
      notebook.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notebook.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notebook.tags.some((tag) =>
        tag.toLowerCase().includes(searchTerm.toLowerCase())
      )
  );

  const handleDownload = (notebook: Notebook) => {
    toast.success(`Downloading ${notebook.name}`);
  };

  const handleDuplicate = (notebook: Notebook) => {
    toast.success(
      `Duplicated ${notebook.name} as ${notebook.name.replace(
        ".ipynb",
        "_copy.ipynb"
      )}`
    );
  };

  const handleView = (notebook: Notebook) => {
    if (onNotebookSelect) {
      onNotebookSelect(notebook);
    }
    onOpenChange(false);
    toast.success(`Opening ${notebook.name}`);
  };

  const handleUpload = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".ipynb";
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        toast.success(`Uploaded ${file.name}`);
      }
    };
    input.click();
  };

  const handleCreateNew = () => {
    toast.success("Creating new notebook...");
    onOpenChange(false);
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "Project File":
        return "bg-bits-blue/10 text-bits-blue border-bits-blue";
      case "Template":
        return "bg-bits-gold/10 text-bits-gold border-bits-gold";
      case "User Created":
        return "bg-muted text-muted-foreground border-border";
      default:
        return "bg-muted text-muted-foreground border-border";
    }
  };

  const content = (
    <>
      <div className="p-6 pb-0">
        <div className="flex items-center justify-between">
          {!isPage && (
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back
              </Button>
              <div className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-bits-gold" />
                <DialogTitle>Notebook Management</DialogTitle>
              </div>
            </div>
          )}
          {isPage && (
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-bits-gold" />
              <h2 className="text-xl font-semibold">Notebook Management</h2>
            </div>
          )}
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleUpload}>
              <Upload className="h-4 w-4 mr-2" />
              Upload notebook
            </Button>
            <Button
              size="sm"
              className="bg-bits-blue hover:bg-bits-blue/90 text-white"
              onClick={handleCreateNew}
            >
              <Plus className="h-4 w-4 mr-2" />
              New notebook
            </Button>
          </div>
        </div>
        {!isPage && (
          <DialogDescription>
            Manage your Jupyter notebooks, upload new files, or create new
            notebooks for your assignments.
          </DialogDescription>
        )}
        {isPage && (
          <p className="text-sm text-muted-foreground mt-2">
            Manage your Jupyter notebooks, upload new files, or create new
            notebooks for your assignments.
          </p>
        )}
      </div>

      <div className="px-6 pb-2">
        <div className="flex items-center space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search notebooks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9"
            />
          </div>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto px-6 pb-6">
        <div className="space-y-3">
          {filteredNotebooks.map((notebook) => (
            <div
              key={notebook.id}
              className="bg-muted rounded-lg p-4 hover:bg-bits-blue/5 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <FileText className="h-4 w-4 text-bits-gold flex-shrink-0" />
                    <h3 className="font-medium text-foreground truncate">
                      {notebook.name}
                    </h3>
                    <Badge
                      variant="outline"
                      className={`text-xs ${getTypeColor(notebook.type)}`}
                    >
                      {notebook.type}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {notebook.version}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {notebook.description}
                  </p>
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    <span>👤 {notebook.author}</span>
                    <span>⏰ {notebook.lastModified}</span>
                    <span>📁 {notebook.size}</span>
                  </div>
                  <div className="flex items-center space-x-1 mt-2">
                    {notebook.tags.map((tag) => (
                      <Badge
                        key={tag}
                        variant="secondary"
                        className="text-xs px-2 py-0"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div className="flex items-center space-x-2 ml-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleView(notebook)}
                    className="h-8 px-3"
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    Open
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => handleDownload(notebook)}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDuplicate(notebook)}
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Duplicate
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );

  if (isPage) {
    return <div className="h-full flex flex-col bg-white">{content}</div>;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] p-0 bg-white border">
        <DialogHeader className="sr-only">
          <DialogTitle>Notebook Management</DialogTitle>
          <DialogDescription>Manage your Jupyter notebooks</DialogDescription>
        </DialogHeader>
        {content}
      </DialogContent>
    </Dialog>
  );
}
