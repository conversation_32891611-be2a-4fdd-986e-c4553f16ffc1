import { But<PERSON> } from "../components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  Card<PERSON><PERSON><PERSON>,
  CardTit<PERSON>,
} from "../components/ui/card";
import { Badge } from "../components/ui/badge";
import {
  usePermissions,
  PermissionGate,
  RoleGate,
} from "../hooks/usePermissions";
import { PERMISSIONS } from "../utils/authUtils";

/**
 * Example component demonstrating the new permission system
 * This shows how to use the updated authentication with the new login response structure
 */
export function PermissionExampleComponent() {
  const {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    primaryRole,
    isAdmin,
    isInstructor,
    isStudent,
    allPermissions,
    permissionsByCategory,
    userRoles,
  } = usePermissions();

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Permission System Demo</h1>

      {/* Role Information */}
      <Card>
        <CardHeader>
          <CardTitle>Current User Role Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <strong>Primary Role:</strong> {primaryRole}
          </div>
          <div className="flex gap-2">
            <Badge variant={isAdmin ? "default" : "secondary"}>
              Admin: {isAdmin ? "Yes" : "No"}
            </Badge>
            <Badge variant={isInstructor ? "default" : "secondary"}>
              Instructor: {isInstructor ? "Yes" : "No"}
            </Badge>
            <Badge variant={isStudent ? "default" : "secondary"}>
              Student: {isStudent ? "Yes" : "No"}
            </Badge>
          </div>
          <div>
            <strong>Total Permissions:</strong> {allPermissions.length}
          </div>
        </CardContent>
      </Card>

      {/* Permission Checks */}
      <Card>
        <CardHeader>
          <CardTitle>Individual Permission Checks</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <strong>Can Create Projects:</strong>{" "}
              {hasPermission(PERMISSIONS.CREATE_PROJECTS) ? "✅" : "❌"}
            </div>
            <div>
              <strong>Can View Projects:</strong>{" "}
              {hasPermission(PERMISSIONS.VIEW_PROJECTS) ? "✅" : "❌"}
            </div>
            <div>
              <strong>Can Edit Projects:</strong>{" "}
              {hasPermission(PERMISSIONS.EDIT_PROJECTS) ? "✅" : "❌"}
            </div>
            <div>
              <strong>Can Delete Projects:</strong>{" "}
              {hasPermission(PERMISSIONS.DELETE_PROJECTS) ? "✅" : "❌"}
            </div>
            <div>
              <strong>Can Submit Assignments:</strong>{" "}
              {hasPermission(PERMISSIONS.SUBMIT_ASSIGNMENTS) ? "✅" : "❌"}
            </div>
            <div>
              <strong>Can View Submissions:</strong>{" "}
              {hasPermission(PERMISSIONS.VIEW_SUBMISSIONS) ? "✅" : "❌"}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Complex Permission Checks */}
      <Card>
        <CardHeader>
          <CardTitle>Complex Permission Checks</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div>
            <strong>Can Manage Any Projects:</strong>{" "}
            {hasAnyPermission([
              PERMISSIONS.CREATE_PROJECTS,
              PERMISSIONS.EDIT_PROJECTS,
              PERMISSIONS.DELETE_PROJECTS,
            ])
              ? "✅"
              : "❌"}
          </div>
          <div>
            <strong>Has Full Project Control:</strong>{" "}
            {hasAllPermissions([
              PERMISSIONS.CREATE_PROJECTS,
              PERMISSIONS.VIEW_PROJECTS,
              PERMISSIONS.EDIT_PROJECTS,
              PERMISSIONS.DELETE_PROJECTS,
              PERMISSIONS.PUBLISH_PROJECTS,
            ])
              ? "✅"
              : "❌"}
          </div>
        </CardContent>
      </Card>

      {/* Permission-Based Rendering */}
      <Card>
        <CardHeader>
          <CardTitle>Permission-Based UI Elements</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <PermissionGate permissions={PERMISSIONS.CREATE_PROJECTS}>
            <Button className="bg-green-600 hover:bg-green-700">
              Create New Project
            </Button>
          </PermissionGate>

          <PermissionGate
            permissions={[
              PERMISSIONS.EDIT_PROJECTS,
              PERMISSIONS.DELETE_PROJECTS,
            ]}
            requireAll={false}
          >
            <Button variant="outline">Manage Existing Projects</Button>
          </PermissionGate>

          <PermissionGate
            permissions={PERMISSIONS.VIEW_SUBMISSIONS}
            fallback={
              <Badge variant="destructive">No access to submissions</Badge>
            }
          >
            <Button className="bg-blue-600 hover:bg-blue-700">
              View Submissions
            </Button>
          </PermissionGate>
        </CardContent>
      </Card>

      {/* Role-Based Rendering */}
      <Card>
        <CardHeader>
          <CardTitle>Role-Based UI Elements</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <RoleGate roles="admin">
            <Button variant="destructive">Admin Only: Delete All Data</Button>
          </RoleGate>

          <RoleGate roles={["admin", "instructor"]}>
            <Button className="bg-purple-600 hover:bg-purple-700">
              Staff Only: Grade Assignments
            </Button>
          </RoleGate>

          <RoleGate
            roles="student"
            fallback={
              <Badge variant="secondary">Not available for students</Badge>
            }
          >
            <Button>Student Only: Submit Assignment</Button>
          </RoleGate>
        </CardContent>
      </Card>

      {/* Permissions by Category */}
      <Card>
        <CardHeader>
          <CardTitle>Permissions by Category</CardTitle>
        </CardHeader>
        <CardContent>
          {Object.entries(permissionsByCategory).map(
            ([category, permissions]) => (
              <div key={category} className="mb-4">
                <h3 className="text-lg font-semibold capitalize">{category}</h3>
                <div className="flex flex-wrap gap-2 mt-2">
                  {permissions.map((permission) => (
                    <Badge key={permission.id} variant="outline">
                      {permission.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )
          )}
        </CardContent>
      </Card>

      {/* Raw Role Data */}
      <Card>
        <CardHeader>
          <CardTitle>Raw Role Data</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto max-h-64">
            {JSON.stringify(userRoles, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
}
