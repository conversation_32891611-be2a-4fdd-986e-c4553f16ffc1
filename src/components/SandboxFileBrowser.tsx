import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "./ui/card";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import {
  Folder,
  File,
  Lock,
  Download,
  Upload,
  RefreshCw,
  User,
  Users,
  FileText,
  Image,
  Code,
  Database,
  Archive,
  ChevronRight,
  ChevronDown,
} from "lucide-react";

interface FileItem {
  name: string;
  type: "file" | "folder";
  size?: number;
  lastModified?: string;
  path: string;
  extension?: string;
  readOnly?: boolean;
}

interface FileSection {
  title: string;
  icon: React.ReactNode;
  path: string;
  readOnly: boolean;
  files: FileItem[];
}

interface SandboxFileBrowserProps {
  projectId: string;
  userId: string;
  isGroupProject?: boolean;
  onFileSelect?: (file: FileItem) => void;
  onFileUpload?: (files: FileList, targetPath: string) => void;
  onFileDownload?: (file: FileItem) => void;
  onRefresh?: () => void;
}

export default function SandboxFileBrowser({
  projectId,
  userId,
  isGroupProject = false,
  onFileSelect,
  onFileUpload,
  onFileDownload,
  onRefresh,
}: SandboxFileBrowserProps) {
  const [sections, setSections] = useState<FileSection[]>([]);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(
    new Set()
  );
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [dragOver, setDragOver] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("workspace");

  const getFileIcon = (file: FileItem) => {
    if (file.type === "folder") {
      return <Folder className="w-4 h-4 text-blue-600" />;
    }

    const ext = file.extension?.toLowerCase();
    switch (ext) {
      case "py":
      case "ipynb":
        return <Code className="w-4 h-4 text-green-600" />;
      case "csv":
      case "json":
      case "xlsx":
        return <Database className="w-4 h-4 text-purple-600" />;
      case "png":
      case "jpg":
      case "jpeg":
      case "gif":
        return <Image className="w-4 h-4 text-pink-600" />;
      case "zip":
      case "tar":
      case "gz":
        return <Archive className="w-4 h-4 text-orange-600" />;
      case "md":
      case "txt":
      case "rst":
        return <FileText className="w-4 h-4 text-gray-600" />;
      default:
        return <File className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "";
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i))} ${sizes[i]}`;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "";
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const toggleFolder = (path: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedFolders(newExpanded);
  };

  const handleFileClick = (file: FileItem) => {
    if (file.type === "folder") {
      toggleFolder(file.path);
    } else {
      setSelectedFile(file.path);
      onFileSelect?.(file);
    }
  };

  const handleDragOver = (
    e: React.DragEvent,
    sectionPath: string,
    readOnly: boolean
  ) => {
    e.preventDefault();
    if (!readOnly) {
      setDragOver(sectionPath);
    }
  };

  const handleDragLeave = () => {
    setDragOver(null);
  };

  const handleDrop = (
    e: React.DragEvent,
    sectionPath: string,
    readOnly: boolean
  ) => {
    e.preventDefault();
    setDragOver(null);

    if (readOnly) return;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      onFileUpload?.(files, sectionPath);
    }
  };

  const renderFileTree = (files: FileItem[], level = 0) => {
    return files.map((file) => {
      const isExpanded = expandedFolders.has(file.path);
      const isSelected = selectedFile === file.path;

      return (
        <div key={file.path}>
          <div
            className={`flex items-center gap-2 py-2 px-2 rounded cursor-pointer hover:bg-gray-100 ${
              isSelected ? "bg-blue-50 border border-blue-200" : ""
            }`}
            style={{ marginLeft: `${level * 16}px` }}
            onClick={() => handleFileClick(file)}
          >
            {file.type === "folder" && (
              <Button variant="ghost" size="sm" className="p-0 h-auto">
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
              </Button>
            )}

            {getFileIcon(file)}

            <span
              className={`flex-1 text-sm ${
                file.readOnly ? "text-gray-600" : "text-gray-900"
              }`}
            >
              {file.name}
            </span>

            {file.readOnly && <Lock className="w-3 h-3 text-gray-400" />}

            <div className="flex items-center gap-2 text-xs text-gray-500">
              {file.size && <span>{formatFileSize(file.size)}</span>}
              {file.lastModified && (
                <span>{formatDate(file.lastModified)}</span>
              )}
            </div>

            {file.type === "file" && !file.readOnly && (
              <Button
                variant="ghost"
                size="sm"
                className="opacity-0 group-hover:opacity-100"
                onClick={(e) => {
                  e.stopPropagation();
                  onFileDownload?.(file);
                }}
              >
                <Download className="w-3 h-3" />
              </Button>
            )}
          </div>

          {file.type === "folder" &&
            isExpanded &&
            file.name.includes("children") && (
              <div>
                {/* Render nested files here - in real implementation, you'd fetch subfolder contents */}
              </div>
            )}
        </div>
      );
    });
  };

  const renderSection = (section: FileSection) => (
    <Card
      key={section.path}
      className={`${
        dragOver === section.path ? "border-blue-400 bg-blue-50" : ""
      }`}
      onDragOver={(e) => handleDragOver(e, section.path, section.readOnly)}
      onDragLeave={handleDragLeave}
      onDrop={(e) => handleDrop(e, section.path, section.readOnly)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center gap-2">
            {section.icon}
            {section.title}
            {section.readOnly && (
              <Badge variant="secondary" className="text-xs">
                <Lock className="w-3 h-3 mr-1" />
                Read Only
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            {!section.readOnly && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const input = document.createElement("input");
                  input.type = "file";
                  input.multiple = true;
                  input.onchange = (e) => {
                    const target = e.target as HTMLInputElement;
                    if (target.files && target.files.length > 0) {
                      onFileUpload?.(target.files, section.path);
                    }
                  };
                  input.click();
                }}
              >
                <Upload className="w-4 h-4" />
              </Button>
            )}
            <Button variant="outline" size="sm" onClick={onRefresh}>
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {section.files.length > 0 ? (
          <div className="space-y-1 max-h-64 overflow-y-auto">
            {renderFileTree(section.files)}
          </div>
        ) : (
          <div className="text-center py-6 text-gray-500">
            <Folder className="w-8 h-8 mx-auto mb-2 text-gray-300" />
            <p className="text-sm">No files in this workspace</p>
            {!section.readOnly && (
              <p className="text-xs mt-1">
                Drag and drop files here or click the upload button
              </p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );

  // Mock data - in real implementation, fetch from API
  useEffect(() => {
    const loadFileStructure = async () => {
      setLoading(true);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const mockSections: FileSection[] = [
        {
          title: "My Workspace",
          icon: <User className="w-4 h-4" />,
          path: `/users/${userId}/projects/${projectId}/`,
          readOnly: false,
          files: [
            {
              name: "analysis.ipynb",
              type: "file",
              size: 15420,
              lastModified: "2024-01-15T10:30:00Z",
              path: `/users/${userId}/projects/${projectId}/analysis.ipynb`,
              extension: "ipynb",
            },
            {
              name: "data_preprocessing.py",
              type: "file",
              size: 8943,
              lastModified: "2024-01-15T09:15:00Z",
              path: `/users/${userId}/projects/${projectId}/data_preprocessing.py`,
              extension: "py",
            },
            {
              name: "results",
              type: "folder",
              lastModified: "2024-01-14T16:45:00Z",
              path: `/users/${userId}/projects/${projectId}/results`,
            },
          ],
        },
      ];

      // Add shared section for group projects
      if (isGroupProject) {
        mockSections.push({
          title: "Shared (Read-Only)",
          icon: <Users className="w-4 h-4" />,
          path: `/shared/projects/${projectId}/`,
          readOnly: true,
          files: [
            {
              name: "dataset.csv",
              type: "file",
              size: 524288,
              lastModified: "2024-01-10T14:20:00Z",
              path: `/shared/projects/${projectId}/dataset.csv`,
              extension: "csv",
              readOnly: true,
            },
            {
              name: "requirements.txt",
              type: "file",
              size: 256,
              lastModified: "2024-01-10T14:20:00Z",
              path: `/shared/projects/${projectId}/requirements.txt`,
              extension: "txt",
              readOnly: true,
            },
            {
              name: "templates",
              type: "folder",
              lastModified: "2024-01-10T14:20:00Z",
              path: `/shared/projects/${projectId}/templates`,
              readOnly: true,
            },
          ],
        });
      }

      setSections(mockSections);
      setLoading(false);
    };

    loadFileStructure();
  }, [projectId, userId, isGroupProject]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading Files...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-gray-900">Project Files</h3>
        <Badge variant="secondary" className="text-xs">
          {sections.reduce((total, section) => total + section.files.length, 0)}{" "}
          files
        </Badge>
      </div>

      {sections.length === 1 ? (
        // Single section - render directly
        renderSection(sections[0])
      ) : (
        // Multiple sections - use custom tabs
        <div className="space-y-4">
          <div className="flex items-center bg-gray-200 rounded-lg p-1 w-fit">
            <button
              onClick={() => setActiveTab("workspace")}
              className={`px-6 py-2 rounded-md transition-all duration-200 text-sm font-medium flex items-center gap-2 ${
                activeTab === "workspace"
                  ? "bg-white text-bits-blue shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              <User className="w-4 h-4" />
              My Workspace
            </button>
            <button
              onClick={() => setActiveTab("shared")}
              className={`px-6 py-2 rounded-md transition-all duration-200 text-sm font-medium flex items-center gap-2 ${
                activeTab === "shared"
                  ? "bg-white text-bits-blue shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              <Users className="w-4 h-4" />
              Shared
            </button>
          </div>

          {activeTab === "workspace" && renderSection(sections[0])}
          {activeTab === "shared" && sections[1] && renderSection(sections[1])}
        </div>
      )}
    </div>
  );
}
