import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "./ui/card";
import { But<PERSON> } from "./ui/button";
import { Progress } from "./ui/progress";
import { Badge } from "./ui/badge";
import {
  CheckCircle2,
  XCircle,
  Clock,
  Upload,
  Play,
  Cpu,
  HardDrive,
  Container,
  Zap,
  Cloud,
  Activity,
  AlertTriangle,
} from "lucide-react";
import { toast } from "sonner";
import SandboxLaunchStatus from "./SandboxLaunchStatus";
import SandboxRecoveryManager from "./SandboxRecoveryManager";

interface SandboxSettings {
  mode: "UV" | "DOCKER" | "FARGATE";
  cpuLimit: string;
  memLimit: string;
  idleTimeout: number;
  imageName: string;
}

interface ChecklistItem {
  id: string;
  label: string;
  completed: boolean;
  icon: React.ReactNode;
}

interface SandboxLaunchPanelProps {
  projectId: string;
  onLaunch: (settings: SandboxSettings) => void;
  isLaunching?: boolean;
}

// Mock API functions that simulate backend calls but provide demo functionality
const fetchSandboxSettings = async (
  projectId: string
): Promise<SandboxSettings> => {
  // Simulate loading delay for demo
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    mode: "DOCKER",
    cpuLimit: "0.5",
    memLimit: "2Gi",
    idleTimeout: 30,
    imageName: "jupyter/datascience-notebook:latest",
  };
};

const checkWorkspaceStatus = async (projectId: string) => {
  // Simulate loading delay for demo
  await new Promise((resolve) => setTimeout(resolve, 300));

  // In demo, randomly simulate different states
  const isReady = Math.random() > 0.3; // 70% chance it's ready

  return {
    is_ready: isReady,
    forkedAt: isReady
      ? new Date(Date.now() - Math.random() * 86400000).toISOString()
      : null,
    s3Prefix: `users/student-1/projects/${projectId}/`,
  };
};

const forkExercise = async (studentId: string, projectId: string) => {
  // Simulate processing time for demo
  await new Promise((resolve) => setTimeout(resolve, 1000));

  toast.success("Project forked successfully to your workspace!");

  return {
    success: true,
    message: "Project forked successfully",
    forkedAt: new Date().toISOString(),
  };
};

const pushToCloud = async (
  projectId: string,
  onProgress: (progress: number) => void
) => {
  // Simulate file push with progress updates for demo
  const totalSteps = 100;

  for (let i = 0; i <= totalSteps; i += 5) {
    onProgress(i);
    await new Promise((resolve) => setTimeout(resolve, 100));
  }

  toast.success("Files pushed successfully to cloud workspace!");

  return {
    success: true,
    message: "Files pushed successfully",
  };
};

export default function SandboxLaunchPanel({
  projectId,
  onLaunch,
  isLaunching = false,
}: SandboxLaunchPanelProps) {
  const [settings, setSettings] = useState<SandboxSettings | null>(null);
  const [checklist, setChecklist] = useState<ChecklistItem[]>([
    {
      id: "forked",
      label: "Project forked",
      completed: false,
      icon: <CheckCircle2 className="w-4 h-4" />,
    },
    {
      id: "pushed",
      label: "Files pushed to cloud",
      completed: false,
      icon: <Cloud className="w-4 h-4" />,
    },
    {
      id: "ready",
      label: "Sandbox ready",
      completed: false,
      icon: <Play className="w-4 h-4" />,
    },
  ]);
  const [isPushing, setIsPushing] = useState(false);
  const [pushProgress, setPushProgress] = useState(0);
  const [loading, setLoading] = useState(true);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [launchFailed, setLaunchFailed] = useState(false);
  const [isInternalLaunching, setIsInternalLaunching] = useState(false);

  const getModeIcon = (mode: string) => {
    switch (mode) {
      case "UV":
        return <Zap className="w-4 h-4" />;
      case "DOCKER":
        return <Container className="w-4 h-4" />;
      case "FARGATE":
        return <Cloud className="w-4 h-4" />;
      default:
        return <Container className="w-4 h-4" />;
    }
  };

  const getModeColor = (mode: string) => {
    switch (mode) {
      case "UV":
        return "bg-yellow-100 text-yellow-800";
      case "DOCKER":
        return "bg-blue-100 text-blue-800";
      case "FARGATE":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Load sandbox settings
        const sandboxSettings = await fetchSandboxSettings(projectId);
        setSettings(sandboxSettings);

        // Check workspace status
        const workspaceStatus = await checkWorkspaceStatus(projectId);

        // Update checklist based on status
        setChecklist((prev) =>
          prev.map((item) => {
            switch (item.id) {
              case "forked":
                return { ...item, completed: !!workspaceStatus.forkedAt };
              case "pushed":
                return { ...item, completed: workspaceStatus.is_ready };
              case "ready":
                return { ...item, completed: workspaceStatus.is_ready };
              default:
                return item;
            }
          })
        );
      } catch (error) {
        console.error("Failed to load sandbox data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [projectId]);

  const handleForkProject = async () => {
    try {
      // In real implementation, get studentId from auth context
      await forkExercise("current-student-id", projectId);

      setChecklist((prev) =>
        prev.map((item) =>
          item.id === "forked" ? { ...item, completed: true } : item
        )
      );
    } catch (error) {
      console.error("Failed to fork project:", error);
    }
  };

  const handlePushToCloud = async () => {
    setIsPushing(true);
    setPushProgress(0);

    try {
      await pushToCloud(projectId, setPushProgress);

      setChecklist((prev) =>
        prev.map((item) =>
          item.id === "pushed" || item.id === "ready"
            ? { ...item, completed: true }
            : item
        )
      );
    } catch (error) {
      console.error("Failed to push to cloud:", error);
    } finally {
      setIsPushing(false);
    }
  };

  const handleLaunchComplete = (success: boolean, sandboxId?: string) => {
    setIsInternalLaunching(false);
    if (success) {
      setLaunchFailed(false);
      toast.success("Sandbox launched successfully!");
      // Call the parent's onLaunch callback or navigate as needed
    } else {
      setLaunchFailed(true);
      toast.error("Sandbox launch failed");
    }
  };

  const handleRetryLaunch = () => {
    setIsInternalLaunching(true);
    setLaunchFailed(false);
  };

  const handleRecoverySuccess = (sandboxId: string) => {
    setLaunchFailed(false);
    toast.success("Sandbox recovered successfully!");
    // Handle successful recovery
  };

  const handleRecoveryFailed = (reason: string) => {
    toast.error(`Recovery failed: ${reason}`);
  };

  const handleLaunch = () => {
    if (settings) {
      setIsInternalLaunching(true);
      setLaunchFailed(false);
      onLaunch(settings);

      // Simulate launch process - in real implementation this would be handled by the parent
      setTimeout(() => {
        handleLaunchComplete(Math.random() > 0.3); // 70% success rate for demo
      }, 8000);
    }
  };

  const allChecksPassed = checklist.every((item) => item.completed);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading Sandbox Settings...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show real-time launch status when launching
  if (isInternalLaunching || launchFailed) {
    return (
      <div className="space-y-6">
        <SandboxLaunchStatus
          isLaunching={isInternalLaunching}
          onLaunchComplete={handleLaunchComplete}
          onRetry={handleRetryLaunch}
          autoRetry={true}
          maxRetries={3}
        />

        {launchFailed && showAdvanced && (
          <SandboxRecoveryManager
            sandboxId={projectId}
            onRecoverySuccess={handleRecoverySuccess}
            onRecoveryFailed={handleRecoveryFailed}
          />
        )}

        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-center gap-3">
              <Button
                variant="outline"
                onClick={() => {
                  setIsInternalLaunching(false);
                  setLaunchFailed(false);
                }}
              >
                Cancel
              </Button>

              {launchFailed && (
                <Button
                  variant="outline"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                  className="flex items-center gap-2"
                >
                  <Activity className="w-4 h-4" />
                  {showAdvanced ? "Hide" : "Show"} Advanced Recovery
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Container className="w-5 h-5" />
          Sandbox Environment
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Sandbox Configuration Display */}
        {settings && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Badge
                variant="secondary"
                className={`${getModeColor(
                  settings.mode
                )} flex items-center gap-1`}
              >
                {getModeIcon(settings.mode)}
                {settings.mode}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Cpu className="w-4 h-4 text-gray-500" />
              <span className="text-sm">{settings.cpuLimit} CPU</span>
            </div>
            <div className="flex items-center gap-2">
              <HardDrive className="w-4 h-4 text-gray-500" />
              <span className="text-sm">{settings.memLimit} RAM</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-gray-500" />
              <span className="text-sm">{settings.idleTimeout}min idle</span>
            </div>
          </div>
        )}

        {/* Pre-launch Checklist */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">Pre-launch Checklist</h4>
          <div className="space-y-2">
            {checklist.map((item) => (
              <div
                key={item.id}
                className="flex items-center gap-3 p-2 rounded-md hover:bg-gray-50"
              >
                <div
                  className={`${
                    item.completed ? "text-green-600" : "text-gray-400"
                  }`}
                >
                  {item.completed ? (
                    <CheckCircle2 className="w-5 h-5" />
                  ) : (
                    <XCircle className="w-5 h-5" />
                  )}
                </div>
                <span
                  className={`${
                    item.completed ? "text-gray-900" : "text-gray-500"
                  }`}
                >
                  {item.label}
                </span>
                {item.id === "forked" && !item.completed && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleForkProject}
                    className="ml-auto"
                  >
                    Fork Now
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Push to Cloud Section */}
        <div className="space-y-3">
          {isPushing && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  Pushing files to cloud...
                </span>
                <span className="text-sm font-medium">{pushProgress}%</span>
              </div>
              <Progress value={pushProgress} className="w-full" />
            </div>
          )}

          <div className="flex gap-3">
            <Button
              onClick={handlePushToCloud}
              disabled={
                isPushing ||
                checklist.find((item) => item.id === "pushed")?.completed
              }
              variant="outline"
              className="flex items-center gap-2"
            >
              <Upload className="w-4 h-4" />
              {isPushing ? "Pushing..." : "Push to Cloud"}
            </Button>

            <Button
              onClick={handleLaunch}
              disabled={!allChecksPassed || isLaunching || isInternalLaunching}
              className="bg-bits-blue hover:bg-bits-blue/90 flex items-center gap-2"
            >
              <Play className="w-4 h-4" />
              {isLaunching || isInternalLaunching
                ? "Launching..."
                : "Launch Sandbox"}
            </Button>
          </div>
        </div>

        {/* Enhanced Status Information */}
        {!allChecksPassed && (
          <div className="flex items-start gap-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
            <AlertTriangle className="w-4 h-4 text-amber-600 flex-shrink-0 mt-0.5" />
            <div className="text-sm text-amber-800">
              <p className="font-medium">
                Complete checklist to launch sandbox
              </p>
              <p>
                All items must be completed before launching your environment.
              </p>
            </div>
          </div>
        )}

        {/* Image Information */}
        {settings && (
          <div className="p-3 bg-blue-50 rounded-md border border-blue-200">
            <div className="flex items-center gap-2">
              <Container className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">Image:</span>
              <span className="text-sm text-blue-700 font-mono">
                {settings.imageName}
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
