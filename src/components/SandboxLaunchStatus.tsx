import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./ui/card";
import { Button } from "./ui/button";
import { Progress } from "./ui/progress";
import { Badge } from "./ui/badge";
import { toast } from "sonner";
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  RefreshCw,
  Terminal,
  Server,
  Network,
  FileCheck,
  Rocket,
} from "lucide-react";

interface LaunchStep {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  description: string;
  status: "pending" | "running" | "completed" | "failed";
  progress?: number;
  duration?: number;
  error?: string;
}

interface SandboxLaunchStatusProps {
  isLaunching: boolean;
  onLaunchComplete: (success: boolean, sandboxId?: string) => void;
  onRetry: () => void;
  autoRetry?: boolean;
  maxRetries?: number;
}

const LAUNCH_STEPS: Omit<
  LaunchStep,
  "status" | "progress" | "duration" | "error"
>[] = [
  {
    id: "validation",
    name: "Validating Request",
    icon: FileCheck,
    description: "Checking user permissions and resource availability",
  },
  {
    id: "provisioning",
    name: "Provisioning Resources",
    icon: Server,
    description: "Allocating compute resources and storage",
  },
  {
    id: "networking",
    name: "Setting up Network",
    icon: Network,
    description: "Configuring network access and security groups",
  },
  {
    id: "container",
    name: "Starting Container",
    icon: Terminal,
    description: "Initializing Jupyter environment and dependencies",
  },
  {
    id: "ready",
    name: "Finalizing Setup",
    icon: Rocket,
    description: "Performing final checks and preparing workspace",
  },
];

export default function SandboxLaunchStatus({
  isLaunching,
  onLaunchComplete,
  onRetry,
  autoRetry = true,
  maxRetries = 3,
}: SandboxLaunchStatusProps) {
  const [steps, setSteps] = useState<LaunchStep[]>(() =>
    LAUNCH_STEPS.map((step) => ({ ...step, status: "pending" as const }))
  );
  const [currentStep, setCurrentStep] = useState(0);
  const [overallProgress, setOverallProgress] = useState(0);
  const [launchStartTime, setLaunchStartTime] = useState<number | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  // Mock real-time status updates
  const simulateLaunchProgress = useCallback(async () => {
    if (!isLaunching) return;

    setLaunchStartTime(Date.now());
    setSteps((prev) =>
      prev.map((step) => ({ ...step, status: "pending" as const }))
    );
    setCurrentStep(0);
    setOverallProgress(0);

    const stepDurations = [2000, 4000, 3000, 5000, 2000]; // Realistic durations in ms

    try {
      for (let i = 0; i < LAUNCH_STEPS.length; i++) {
        // Start current step
        setCurrentStep(i);
        setSteps((prev) =>
          prev.map((step, index) =>
            index === i ? { ...step, status: "running" } : step
          )
        );

        // Simulate progress within the step
        const stepDuration = stepDurations[i];
        const progressInterval = 100; // Update every 100ms
        const progressSteps = stepDuration / progressInterval;

        for (
          let progress = 0;
          progress <= 100;
          progress += 100 / progressSteps
        ) {
          if (!isLaunching) return; // Check if launch was cancelled

          setSteps((prev) =>
            prev.map((step, index) =>
              index === i
                ? { ...step, progress: Math.min(progress, 100) }
                : step
            )
          );

          setOverallProgress(
            (i * 100 + Math.min(progress, 100)) / LAUNCH_STEPS.length
          );

          await new Promise((resolve) => setTimeout(resolve, progressInterval));
        }

        // Complete current step
        setSteps((prev) =>
          prev.map((step, index) =>
            index === i
              ? {
                  ...step,
                  status: "completed",
                  progress: 100,
                  duration: stepDuration,
                }
              : step
          )
        );

        // Random failure simulation (10% chance on any step after validation)
        if (i > 0 && Math.random() < 0.1) {
          throw new Error(
            `Failed during ${LAUNCH_STEPS[
              i
            ].name.toLowerCase()}: Resource allocation timeout`
          );
        }
      }

      // Success!
      setOverallProgress(100);
      const totalDuration = Date.now() - (launchStartTime || Date.now());
      toast.success(
        `Sandbox launched successfully in ${(totalDuration / 1000).toFixed(1)}s`
      );
      onLaunchComplete(true, `sandbox-${Date.now()}`);
    } catch (error) {
      // Handle failure
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      setSteps((prev) =>
        prev.map((step, index) =>
          index === currentStep
            ? {
                ...step,
                status: "failed",
                error: errorMessage,
              }
            : step
        )
      );

      toast.error(`Sandbox launch failed: ${errorMessage}`);

      // Auto-retry logic
      if (autoRetry && retryCount < maxRetries) {
        setIsRetrying(true);
        setTimeout(() => {
          setRetryCount((prev) => prev + 1);
          setIsRetrying(false);
          toast.info(
            `Retrying launch attempt ${retryCount + 2}/${maxRetries + 1}`
          );
          simulateLaunchProgress();
        }, Math.min(1000 * Math.pow(2, retryCount), 10000)); // Exponential backoff, max 10s
      } else {
        onLaunchComplete(false);
      }
    }
  }, [
    isLaunching,
    retryCount,
    maxRetries,
    autoRetry,
    currentStep,
    launchStartTime,
    onLaunchComplete,
  ]);

  useEffect(() => {
    if (isLaunching) {
      simulateLaunchProgress();
    }
  }, [isLaunching, simulateLaunchProgress]);

  const getStatusIcon = (status: LaunchStep["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "running":
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      case "failed":
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: LaunchStep["status"]) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "running":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "failed":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-600 border-gray-200";
    }
  };

  const formatDuration = (ms: number) => {
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getElapsedTime = () => {
    if (!launchStartTime) return "0.0s";
    return formatDuration(Date.now() - launchStartTime);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Terminal className="w-5 h-5" />
              Sandbox Launch Status
              {isRetrying && (
                <Badge
                  variant="outline"
                  className="text-orange-600 border-orange-300"
                >
                  Retrying {retryCount}/{maxRetries}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Real-time progress updates for your sandbox environment
            </CardDescription>
          </div>
          <div className="text-right text-sm text-gray-500">
            <div>Elapsed: {getElapsedTime()}</div>
            {retryCount > 0 && (
              <div className="text-orange-600">
                Attempt {retryCount + 1}/{maxRetries + 1}
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span>{Math.round(overallProgress)}%</span>
          </div>
          <Progress value={overallProgress} className="h-2" />
        </div>

        {/* Step-by-step Progress */}
        <div className="space-y-3">
          {steps.map((step, index) => {
            const StepIcon = step.icon;
            const isActive = index === currentStep && step.status === "running";

            return (
              <div
                key={step.id}
                className={`
                  flex items-start gap-3 p-3 rounded-lg border transition-colors
                  ${
                    isActive
                      ? "bg-blue-50 border-blue-200"
                      : "bg-gray-50 border-gray-200"
                  }
                `}
              >
                <div className="flex-shrink-0 mt-0.5">
                  <StepIcon
                    className={`w-5 h-5 ${
                      step.status === "completed"
                        ? "text-green-500"
                        : step.status === "running"
                        ? "text-blue-500"
                        : step.status === "failed"
                        ? "text-red-500"
                        : "text-gray-400"
                    }`}
                  />
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium">{step.name}</h4>
                    <div className="flex items-center gap-2">
                      {step.duration && (
                        <span className="text-xs text-gray-500">
                          {formatDuration(step.duration)}
                        </span>
                      )}
                      {getStatusIcon(step.status)}
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-2">
                    {step.description}
                  </p>

                  {step.status === "running" && step.progress !== undefined && (
                    <div className="space-y-1">
                      <Progress value={step.progress} className="h-1" />
                      <div className="text-xs text-gray-500">
                        {Math.round(step.progress)}% complete
                      </div>
                    </div>
                  )}

                  {step.error && (
                    <div className="flex items-start gap-2 mt-2 p-2 bg-red-50 border border-red-200 rounded">
                      <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0 mt-0.5" />
                      <span className="text-sm text-red-700">{step.error}</span>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Action Buttons */}
        {!isLaunching && steps.some((step) => step.status === "failed") && (
          <div className="flex justify-center pt-4 border-t">
            <Button
              onClick={onRetry}
              variant="outline"
              className="flex items-center gap-2"
              disabled={isRetrying}
            >
              <RefreshCw
                className={`w-4 h-4 ${isRetrying ? "animate-spin" : ""}`}
              />
              {isRetrying ? "Retrying..." : "Retry Launch"}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
