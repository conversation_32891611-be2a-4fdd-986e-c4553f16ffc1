import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./ui/card";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { Switch } from "./ui/switch";
import { Label } from "./ui/label";
import { toast } from "sonner";
import {
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Shield,
  Activity,
  Settings,
  TrendingUp,
  AlertCircle,
  Zap,
} from "lucide-react";

interface RecoveryAttempt {
  id: string;
  timestamp: Date;
  failureReason: string;
  recoveryStrategy: string;
  status: "pending" | "running" | "success" | "failed";
  duration?: number;
  resourcesUsed?: {
    cpu: number;
    memory: number;
    storage: number;
  };
}

interface RecoveryConfig {
  enabled: boolean;
  maxRetries: number;
  retryStrategy: "exponential" | "linear" | "immediate";
  resourceScaling: boolean;
  failoverRegions: boolean;
  healthChecks: boolean;
  autoFallback: boolean;
}

interface SandboxRecoveryManagerProps {
  sandboxId?: string;
  onRecoverySuccess: (sandboxId: string) => void;
  onRecoveryFailed: (reason: string) => void;
}

const FAILURE_PATTERNS = {
  resource_exhaustion: {
    name: "Resource Exhaustion",
    strategy: "Scale up resources and retry",
    icon: TrendingUp,
    color: "text-orange-500",
  },
  network_timeout: {
    name: "Network Timeout",
    strategy: "Switch to backup network configuration",
    icon: Activity,
    color: "text-blue-500",
  },
  container_failed: {
    name: "Container Start Failed",
    strategy: "Use lightweight container image",
    icon: Shield,
    color: "text-red-500",
  },
  quota_exceeded: {
    name: "Quota Exceeded",
    strategy: "Queue request for next available slot",
    icon: Clock,
    color: "text-purple-500",
  },
  dependency_unavailable: {
    name: "Dependency Unavailable",
    strategy: "Use cached dependencies and retry",
    icon: Zap,
    color: "text-green-500",
  },
} as const;

const DEFAULT_CONFIG: RecoveryConfig = {
  enabled: true,
  maxRetries: 5,
  retryStrategy: "exponential",
  resourceScaling: true,
  failoverRegions: true,
  healthChecks: true,
  autoFallback: true,
};

export default function SandboxRecoveryManager({
  sandboxId,
  onRecoverySuccess,
  onRecoveryFailed,
}: SandboxRecoveryManagerProps) {
  const [config, setConfig] = useState<RecoveryConfig>(DEFAULT_CONFIG);
  const [recoveryAttempts, setRecoveryAttempts] = useState<RecoveryAttempt[]>(
    []
  );
  const [isRecovering, setIsRecovering] = useState(false);
  const [currentAttempt, setCurrentAttempt] = useState<RecoveryAttempt | null>(
    null
  );
  const [recoveryStats, setRecoveryStats] = useState({
    totalAttempts: 0,
    successRate: 0,
    averageRecoveryTime: 0,
    commonFailures: [] as string[],
  });

  // Mock failure detection and analysis
  const analyzeFailure = useCallback((error: string) => {
    const patterns = Object.keys(FAILURE_PATTERNS);
    // Simple pattern matching - in real implementation, this would use ML or rule-based analysis
    if (error.includes("timeout") || error.includes("network"))
      return "network_timeout";
    if (
      error.includes("memory") ||
      error.includes("cpu") ||
      error.includes("resource")
    )
      return "resource_exhaustion";
    if (error.includes("container") || error.includes("docker"))
      return "container_failed";
    if (error.includes("quota") || error.includes("limit"))
      return "quota_exceeded";
    if (error.includes("dependency") || error.includes("unavailable"))
      return "dependency_unavailable";
    return patterns[Math.floor(Math.random() * patterns.length)];
  }, []);

  // Intelligent recovery strategy
  const executeRecovery = useCallback(
    async (failureReason: string) => {
      if (!config.enabled) {
        toast.error("Auto-recovery is disabled");
        return false;
      }

      if (recoveryAttempts.length >= config.maxRetries) {
        toast.error(`Maximum recovery attempts (${config.maxRetries}) reached`);
        onRecoveryFailed("Max retries exceeded");
        return false;
      }

      const failureType = analyzeFailure(failureReason);
      const pattern =
        FAILURE_PATTERNS[failureType as keyof typeof FAILURE_PATTERNS];

      const attempt: RecoveryAttempt = {
        id: `recovery-${Date.now()}`,
        timestamp: new Date(),
        failureReason,
        recoveryStrategy: pattern.strategy,
        status: "running",
      };

      setCurrentAttempt(attempt);
      setRecoveryAttempts((prev) => [...prev, attempt]);
      setIsRecovering(true);

      toast.info(`Starting recovery: ${pattern.strategy}`);

      try {
        // Calculate retry delay based on strategy
        let delay = 1000; // Base delay
        if (config.retryStrategy === "exponential") {
          delay = Math.min(1000 * Math.pow(2, recoveryAttempts.length), 30000);
        } else if (config.retryStrategy === "linear") {
          delay = 1000 * (recoveryAttempts.length + 1);
        }

        await new Promise((resolve) => setTimeout(resolve, delay));

        // Simulate recovery process with different strategies
        const startTime = Date.now();
        let success = false;

        switch (failureType) {
          case "resource_exhaustion":
            if (config.resourceScaling) {
              // Simulate resource scaling
              await new Promise((resolve) => setTimeout(resolve, 3000));
              success = Math.random() > 0.3; // 70% success rate with scaling
            }
            break;

          case "network_timeout":
            if (config.failoverRegions) {
              // Simulate failover to different region
              await new Promise((resolve) => setTimeout(resolve, 2000));
              success = Math.random() > 0.2; // 80% success rate with failover
            }
            break;

          case "container_failed":
            // Try with lighter container
            await new Promise((resolve) => setTimeout(resolve, 4000));
            success = Math.random() > 0.4; // 60% success rate
            break;

          case "quota_exceeded":
            // Wait for quota availability
            await new Promise((resolve) => setTimeout(resolve, 5000));
            success = Math.random() > 0.5; // 50% success rate
            break;

          case "dependency_unavailable":
            // Use cached dependencies
            await new Promise((resolve) => setTimeout(resolve, 1500));
            success = Math.random() > 0.1; // 90% success rate with cache
            break;
        }

        const duration = Date.now() - startTime;

        if (success) {
          const updatedAttempt = {
            ...attempt,
            status: "success" as const,
            duration,
            resourcesUsed: {
              cpu: Math.random() * 2 + 0.5,
              memory: Math.random() * 4 + 2,
              storage: Math.random() * 10 + 5,
            },
          };

          setCurrentAttempt(updatedAttempt);
          setRecoveryAttempts((prev) =>
            prev.map((a) => (a.id === attempt.id ? updatedAttempt : a))
          );

          toast.success(
            `Recovery successful! Sandbox restored in ${(
              duration / 1000
            ).toFixed(1)}s`
          );
          onRecoverySuccess(`sandbox-recovered-${Date.now()}`);
          return true;
        } else {
          throw new Error(`Recovery strategy failed: ${pattern.strategy}`);
        }
      } catch (error) {
        const duration = Date.now() - Date.now();
        const updatedAttempt = {
          ...attempt,
          status: "failed" as const,
          duration,
        };

        setCurrentAttempt(updatedAttempt);
        setRecoveryAttempts((prev) =>
          prev.map((a) => (a.id === attempt.id ? updatedAttempt : a))
        );

        toast.error(
          `Recovery attempt failed: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );

        // Try next recovery strategy or give up
        if (recoveryAttempts.length + 1 < config.maxRetries) {
          // Recursive retry with different strategy
          setTimeout(() => {
            executeRecovery(`Retry after: ${failureReason}`);
          }, 2000);
        } else {
          onRecoveryFailed("All recovery strategies exhausted");
        }

        return false;
      } finally {
        setIsRecovering(false);
      }
    },
    [
      config,
      recoveryAttempts,
      analyzeFailure,
      onRecoverySuccess,
      onRecoveryFailed,
    ]
  );

  // Update recovery statistics
  useEffect(() => {
    const totalAttempts = recoveryAttempts.length;
    const successfulAttempts = recoveryAttempts.filter(
      (a) => a.status === "success"
    ).length;
    const completedAttempts = recoveryAttempts.filter((a) => a.duration);
    const avgTime =
      completedAttempts.length > 0
        ? completedAttempts.reduce((sum, a) => sum + (a.duration || 0), 0) /
          completedAttempts.length
        : 0;

    setRecoveryStats({
      totalAttempts,
      successRate:
        totalAttempts > 0 ? (successfulAttempts / totalAttempts) * 100 : 0,
      averageRecoveryTime: avgTime,
      commonFailures:
        recoveryAttempts
          .map((a) => analyzeFailure(a.failureReason))
          .reduce((acc, failure) => {
            acc[failure] = (acc[failure] || 0) + 1;
            return acc;
          }, {} as Record<string, number>)
          .then((counts) =>
            Object.entries(counts)
              .sort(([, a], [, b]) => b - a)
              .slice(0, 3)
              .map(([failure]) => failure)
          ) || [],
    });
  }, [recoveryAttempts, analyzeFailure]);

  const getStatusIcon = (status: RecoveryAttempt["status"]) => {
    switch (status) {
      case "success":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "failed":
        return <XCircle className="w-4 h-4 text-red-500" />;
      case "running":
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const triggerManualRecovery = () => {
    executeRecovery("Manual recovery triggered by user");
  };

  return (
    <div className="space-y-6">
      {/* Recovery Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Recovery Configuration
          </CardTitle>
          <CardDescription>
            Configure automatic recovery settings for sandbox failures
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="recovery-enabled">Auto Recovery</Label>
              <Switch
                id="recovery-enabled"
                checked={config.enabled}
                onCheckedChange={(enabled) =>
                  setConfig((prev) => ({ ...prev, enabled }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="max-retries">Max Retries</Label>
              <Select
                value={config.maxRetries.toString()}
                onValueChange={(value) =>
                  setConfig((prev) => ({
                    ...prev,
                    maxRetries: parseInt(value),
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="3">3 attempts</SelectItem>
                  <SelectItem value="5">5 attempts</SelectItem>
                  <SelectItem value="10">10 attempts</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="retry-strategy">Retry Strategy</Label>
              <Select
                value={config.retryStrategy}
                onValueChange={(value) =>
                  setConfig((prev) => ({
                    ...prev,
                    retryStrategy: value as RecoveryConfig["retryStrategy"],
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="immediate">Immediate</SelectItem>
                  <SelectItem value="linear">Linear Backoff</SelectItem>
                  <SelectItem value="exponential">
                    Exponential Backoff
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="resource-scaling">Resource Scaling</Label>
              <Switch
                id="resource-scaling"
                checked={config.resourceScaling}
                onCheckedChange={(resourceScaling) =>
                  setConfig((prev) => ({ ...prev, resourceScaling }))
                }
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recovery Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Recovery Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {recoveryStats.totalAttempts}
              </div>
              <div className="text-sm text-gray-600">Total Attempts</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {recoveryStats.successRate.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Success Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {(recoveryStats.averageRecoveryTime / 1000).toFixed(1)}s
              </div>
              <div className="text-sm text-gray-600">Avg Recovery Time</div>
            </div>
            <div className="text-center">
              <Button
                onClick={triggerManualRecovery}
                disabled={isRecovering}
                variant="outline"
                size="sm"
                className="w-full"
              >
                {isRecovering ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Recovering...
                  </>
                ) : (
                  <>
                    <Shield className="w-4 h-4 mr-2" />
                    Manual Recovery
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Recovery Attempts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5" />
            Recent Recovery Attempts
          </CardTitle>
          <CardDescription>
            History of automatic recovery attempts and their outcomes
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recoveryAttempts.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Shield className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No recovery attempts yet</p>
              <p className="text-sm">
                Recovery attempts will appear here when failures occur
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {recoveryAttempts
                .slice(-5)
                .reverse()
                .map((attempt) => {
                  const failureType = analyzeFailure(attempt.failureReason);
                  const pattern =
                    FAILURE_PATTERNS[
                      failureType as keyof typeof FAILURE_PATTERNS
                    ];
                  const PatternIcon = pattern.icon;

                  return (
                    <div
                      key={attempt.id}
                      className="flex items-start gap-3 p-3 border rounded-lg"
                    >
                      <PatternIcon
                        className={`w-5 h-5 mt-0.5 ${pattern.color}`}
                      />

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className="font-medium text-sm">
                            {pattern.name}
                          </h4>
                          <div className="flex items-center gap-2">
                            {attempt.duration && (
                              <span className="text-xs text-gray-500">
                                {(attempt.duration / 1000).toFixed(1)}s
                              </span>
                            )}
                            {getStatusIcon(attempt.status)}
                          </div>
                        </div>

                        <p className="text-sm text-gray-600 mb-1">
                          {attempt.recoveryStrategy}
                        </p>
                        <p className="text-xs text-gray-500">
                          {attempt.timestamp.toLocaleTimeString()}
                        </p>

                        {attempt.resourcesUsed && (
                          <div className="flex gap-4 mt-2 text-xs text-gray-500">
                            <span>
                              CPU: {attempt.resourcesUsed.cpu.toFixed(1)} cores
                            </span>
                            <span>
                              Memory: {attempt.resourcesUsed.memory.toFixed(1)}{" "}
                              GB
                            </span>
                            <span>
                              Storage:{" "}
                              {attempt.resourcesUsed.storage.toFixed(1)} GB
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
