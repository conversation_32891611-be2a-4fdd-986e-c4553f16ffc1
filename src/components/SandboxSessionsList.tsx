import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "./ui/card";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./ui/table";
import { Input } from "./ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import {
  Search,
  Filter,
  ExternalLink,
  Clock,
  User,
  Container,
  Zap,
  Cloud,
  Play,
  Square,
  Pause,
} from "lucide-react";

interface SessionRecord {
  sessionId: string;
  studentId: string;
  studentName: string;
  projectId: string;
  projectName: string;
  mode: "UV" | "DOCKER" | "FARGATE";
  status: "Active" | "Idle" | "Terminated" | "Failed";
  startedAt: string;
  endedAt?: string;
  duration: number; // in minutes
  cpuUsage?: number;
  memoryUsage?: number;
  notebookUrl?: string;
}

interface SandboxSessionsListProps {
  projectId?: string; // If provided, filter by project
  onJoinSession?: (session: SessionRecord) => void;
}

// API call to fetch sessions
const fetchSessions = async (projectId?: string): Promise<SessionRecord[]> => {
  // Mock implementation for demo - replace with real API call
  await new Promise((resolve) => setTimeout(resolve, 800));

  // Mock sessions data
  const mockSessions: SessionRecord[] = [
    {
      sessionId: "sess-1",
      studentId: "student-1",
      studentName: "Rahul Sharma",
      projectId: "proj-1",
      projectName: "Mall Customer Segmentation",
      mode: "DOCKER",
      status: "Active",
      startedAt: "2024-01-15T10:30:00Z",
      duration: 45,
      cpuUsage: 65,
      memoryUsage: 78,
      notebookUrl: "/studio/user/student-1/lab/tree/work",
    },
    {
      sessionId: "sess-2",
      studentId: "student-2",
      studentName: "Priya Patel",
      projectId: "proj-1",
      projectName: "Mall Customer Segmentation",
      mode: "FARGATE",
      status: "Idle",
      startedAt: "2024-01-15T09:15:00Z",
      duration: 120,
      cpuUsage: 15,
      memoryUsage: 45,
      notebookUrl: "/studio/user/student-2/lab/tree/work",
    },
    {
      sessionId: "sess-3",
      studentId: "student-3",
      studentName: "Arjun Kumar",
      projectId: "proj-1",
      projectName: "Mall Customer Segmentation",
      mode: "UV",
      status: "Terminated",
      startedAt: "2024-01-14T14:20:00Z",
      endedAt: "2024-01-14T16:45:00Z",
      duration: 145,
    },
  ];

  return mockSessions;
};

export default function SandboxSessionsList({
  projectId,
  onJoinSession,
}: SandboxSessionsListProps) {
  const [sessions, setSessions] = useState<SessionRecord[]>([]);
  const [filteredSessions, setFilteredSessions] = useState<SessionRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [modeFilter, setModeFilter] = useState<string>("all");

  const getModeIcon = (mode: string) => {
    switch (mode) {
      case "UV":
        return <Zap className="w-4 h-4" />;
      case "DOCKER":
        return <Container className="w-4 h-4" />;
      case "FARGATE":
        return <Cloud className="w-4 h-4" />;
      default:
        return <Container className="w-4 h-4" />;
    }
  };

  const getModeColor = (mode: string) => {
    switch (mode) {
      case "UV":
        return "bg-yellow-100 text-yellow-800";
      case "DOCKER":
        return "bg-blue-100 text-blue-800";
      case "FARGATE":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Active":
        return <Play className="w-4 h-4 text-green-600" />;
      case "Idle":
        return <Pause className="w-4 h-4 text-yellow-600" />;
      case "Terminated":
        return <Square className="w-4 h-4 text-gray-600" />;
      case "Failed":
        return <Square className="w-4 h-4 text-red-600" />;
      default:
        return <Square className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "Idle":
        return "bg-yellow-100 text-yellow-800";
      case "Terminated":
        return "bg-gray-100 text-gray-800";
      case "Failed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${Math.round(minutes)}m`;
    }
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}h ${mins}m`;
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const isActiveSession = (session: SessionRecord) => {
    return ["Active", "Idle"].includes(session.status);
  };

  const canJoinSession = (session: SessionRecord) => {
    return isActiveSession(session) && session.notebookUrl;
  };

  // Filter sessions based on search and filters
  useEffect(() => {
    let filtered = sessions;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (session) =>
          session.studentName
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          session.projectName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((session) => session.status === statusFilter);
    }

    // Mode filter
    if (modeFilter !== "all") {
      filtered = filtered.filter((session) => session.mode === modeFilter);
    }

    setFilteredSessions(filtered);
  }, [sessions, searchTerm, statusFilter, modeFilter]);

  // Load sessions data
  useEffect(() => {
    const loadSessions = async () => {
      setLoading(true);
      try {
        const data = await fetchSessions(projectId);
        setSessions(data);
      } catch (error) {
        console.error("Failed to load sessions:", error);
        // Mock data for demo
        const mockSessions: SessionRecord[] = [
          {
            sessionId: "sess-1",
            studentId: "student-1",
            studentName: "Rahul Sharma",
            projectId: "proj-1",
            projectName: "Mall Customer Segmentation",
            mode: "DOCKER",
            status: "Active",
            startedAt: "2024-01-15T10:30:00Z",
            duration: 45,
            cpuUsage: 65,
            memoryUsage: 78,
            notebookUrl: "/studio/user/student-1/lab/tree/work",
          },
          {
            sessionId: "sess-2",
            studentId: "student-2",
            studentName: "Priya Patel",
            projectId: "proj-1",
            projectName: "Mall Customer Segmentation",
            mode: "FARGATE",
            status: "Idle",
            startedAt: "2024-01-15T09:15:00Z",
            duration: 120,
            cpuUsage: 15,
            memoryUsage: 45,
            notebookUrl: "/studio/user/student-2/lab/tree/work",
          },
          {
            sessionId: "sess-3",
            studentId: "student-3",
            studentName: "Arjun Kumar",
            projectId: "proj-1",
            projectName: "Mall Customer Segmentation",
            mode: "UV",
            status: "Terminated",
            startedAt: "2024-01-14T14:20:00Z",
            endedAt: "2024-01-14T16:45:00Z",
            duration: 145,
          },
        ];
        setSessions(mockSessions);
      } finally {
        setLoading(false);
      }
    };

    loadSessions();
  }, [projectId]);

  const handleJoinSession = (session: SessionRecord) => {
    onJoinSession?.(session);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading Sessions...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Container className="w-5 h-5" />
            Sandbox Sessions
            <Badge variant="secondary">{filteredSessions.length}</Badge>
          </CardTitle>
        </div>

        {/* Filters */}
        <div className="flex items-center gap-4 flex-wrap">
          <div className="relative flex-1 min-w-[200px]">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search students or projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="All Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="Active">Active</SelectItem>
              <SelectItem value="Idle">Idle</SelectItem>
              <SelectItem value="Terminated">Terminated</SelectItem>
              <SelectItem value="Failed">Failed</SelectItem>
            </SelectContent>
          </Select>

          <Select value={modeFilter} onValueChange={setModeFilter}>
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="All Modes" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Modes</SelectItem>
              <SelectItem value="UV">UV</SelectItem>
              <SelectItem value="DOCKER">Docker</SelectItem>
              <SelectItem value="FARGATE">Fargate</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent>
        {filteredSessions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Container className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No sessions found</p>
            <p className="text-sm mt-1">
              {sessions.length === 0
                ? "No sandbox sessions have been started yet."
                : "Try adjusting your search or filter criteria."}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Student</TableHead>
                  <TableHead>Project</TableHead>
                  <TableHead>Mode</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Started</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Resources</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSessions.map((session) => (
                  <TableRow key={session.sessionId}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-gray-500" />
                        <span className="font-medium">
                          {session.studentName}
                        </span>
                      </div>
                    </TableCell>

                    <TableCell>
                      <span className="text-sm text-gray-900">
                        {session.projectName}
                      </span>
                    </TableCell>

                    <TableCell>
                      <Badge
                        variant="outline"
                        className={getModeColor(session.mode)}
                      >
                        {getModeIcon(session.mode)}
                        <span className="ml-1">{session.mode}</span>
                      </Badge>
                    </TableCell>

                    <TableCell>
                      <Badge
                        variant="outline"
                        className={getStatusColor(session.status)}
                      >
                        {getStatusIcon(session.status)}
                        <span className="ml-1">{session.status}</span>
                      </Badge>
                    </TableCell>

                    <TableCell>
                      <div className="text-sm">
                        {formatDateTime(session.startedAt)}
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">
                          {formatDuration(session.duration)}
                        </span>
                      </div>
                    </TableCell>

                    <TableCell>
                      {isActiveSession(session) &&
                      (session.cpuUsage !== undefined ||
                        session.memoryUsage !== undefined) ? (
                        <div className="text-xs text-gray-600">
                          {session.cpuUsage && (
                            <div>CPU: {session.cpuUsage}%</div>
                          )}
                          {session.memoryUsage && (
                            <div>RAM: {session.memoryUsage}%</div>
                          )}
                        </div>
                      ) : (
                        <span className="text-xs text-gray-400">-</span>
                      )}
                    </TableCell>

                    <TableCell>
                      {canJoinSession(session) ? (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleJoinSession(session)}
                          className="flex items-center gap-1"
                        >
                          <ExternalLink className="w-3 h-3" />
                          Join
                        </Button>
                      ) : (
                        <span className="text-xs text-gray-400">
                          {session.status === "Terminated"
                            ? "Ended"
                            : "Unavailable"}
                        </span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
