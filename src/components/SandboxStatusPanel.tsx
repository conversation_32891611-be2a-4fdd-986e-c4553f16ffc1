import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Alert, AlertDescription } from "./ui/alert";
import {
  Activity,
  Clock,
  User,
  FileText,
  Square,
  RotateCcw,
  AlertCircle,
  CheckCircle,
  Timer,
  Cpu,
  HardDrive,
} from "lucide-react";
import { useSandboxSession } from "../contexts/SandboxSessionContext";
import { formatDistanceToNow } from "../helper/date-fns";

interface SandboxStatusPanelProps {
  className?: string;
  showCompactView?: boolean;
}

export function SandboxStatusPanel({
  className = "",
  showCompactView = false,
}: SandboxStatusPanelProps) {
  const {
    activeSessions,
    currentSession,
    shutdownSession,
    shutdownAllSessions,
    updateActivity,
  } = useSandboxSession();

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "text-green-600 bg-green-50 border-green-200";
      case "starting":
        return "text-blue-600 bg-blue-50 border-blue-200";
      case "shutting-down":
        return "text-orange-600 bg-orange-50 border-orange-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4" />;
      case "starting":
        return <Clock className="h-4 w-4" />;
      case "shutting-down":
        return <RotateCcw className="h-4 w-4 animate-spin" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  if (showCompactView) {
    return (
      <div className={`bg-white border-b border-gray-200 ${className}`}>
        <div className="px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Activity className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium">
                {activeSessions.length} Active Session
                {activeSessions.length !== 1 ? "s" : ""}
              </span>
              {currentSession && (
                <Badge variant="outline" className="text-xs">
                  Current: {currentSession.studentName}
                </Badge>
              )}
            </div>

            {activeSessions.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={shutdownAllSessions}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Square className="h-3 w-3 mr-1" />
                Shutdown All
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Activity className="h-5 w-5" />
          <span>Sandbox Sessions</span>
          {activeSessions.length > 0 && (
            <Badge variant="outline">{activeSessions.length} active</Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {activeSessions.length === 0 ? (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              No active sandbox sessions. Launch a sandbox to start reviewing
              student work.
            </AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-4">
            {/* Current Session Highlight */}
            {currentSession && (
              <Alert className="border-blue-200 bg-blue-50">
                <CheckCircle className="h-4 w-4 text-blue-600" />
                <AlertDescription className="text-blue-800">
                  <strong>Current Session:</strong> {currentSession.studentName}{" "}
                  - {currentSession.assignmentTitle}
                </AlertDescription>
              </Alert>
            )}

            {/* Sessions List */}
            <div className="space-y-3">
              {activeSessions.map((session) => (
                <div
                  key={session.id}
                  className={`border rounded-lg p-3 ${getStatusColor(
                    session.status
                  )} ${
                    session.id === currentSession?.id
                      ? "ring-2 ring-blue-300"
                      : ""
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(session.status)}
                      <div>
                        <div className="flex items-center space-x-2">
                          <User className="h-3 w-3" />
                          <span className="font-medium text-sm">
                            {session.studentName}
                          </span>
                          {session.id === currentSession?.id && (
                            <Badge variant="default" className="text-xs">
                              Current
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 text-xs text-gray-600 mt-1">
                          <FileText className="h-3 w-3" />
                          <span>{session.assignmentTitle}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <div className="text-right text-xs text-gray-600">
                        <div className="flex items-center space-x-1">
                          <Timer className="h-3 w-3" />
                          <span>
                            Started{" "}
                            {formatDistanceToNow(session.startTime, {
                              addSuffix: true,
                            })}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1 mt-1">
                          <Clock className="h-3 w-3" />
                          <span>
                            Active{" "}
                            {formatDistanceToNow(session.lastActivity, {
                              addSuffix: true,
                            })}
                          </span>
                        </div>
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => shutdownSession(session.id)}
                        disabled={session.status === "shutting-down"}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Square className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>

                  {/* Session Details */}
                  <div className="mt-2 grid grid-cols-3 gap-2 text-xs">
                    <div className="flex items-center space-x-1 text-gray-600">
                      <Cpu className="h-3 w-3" />
                      <span>2 vCPU</span>
                    </div>
                    <div className="flex items-center space-x-1 text-gray-600">
                      <HardDrive className="h-3 w-3" />
                      <span>4GB RAM</span>
                    </div>
                    <div className="flex items-center space-x-1 text-gray-600">
                      <Activity className="h-3 w-3" />
                      <span className="capitalize">{session.status}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Bulk Actions */}
            {activeSessions.length > 1 && (
              <div className="pt-3 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={shutdownAllSessions}
                  className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Square className="h-4 w-4 mr-2" />
                  Shutdown All Sessions ({activeSessions.length})
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
