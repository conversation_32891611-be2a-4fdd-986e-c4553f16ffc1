import {
  Home,
  Users,
  BookOpen,
  GraduationCap,
  Settings,
  FolderOpen,
  BarChart3,
  Shield,
  User as UserIcon,
  FileText,
  MessageSquare,
  Terminal,
  Code,
  UserPlusIcon,
  Clock,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import useAuth from "../Context/AuthContext";
import useNavigation from "../Context/NavigationContext";
import { useState } from "react";
import { useNavigate,useLocation } from "react-router-dom";
function MainSidebar() {
  const { user } = useAuth();
  const { currentPage, navigateTo } = useNavigation();
  const navigate = useNavigate()
  const location = useLocation()
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>(
    {}
  );

  if (!user) return null;

  const toggleMenu = (menuId: string) => {
    setExpandedMenus((prev) => ({
      ...prev,
      [menuId]: !prev[menuId],
    }));
  };

  const getMenuItems = () => {
    if (user.role === "admin") {
      return [
        { id: "dashboard", label: "Dashboard", icon: Home, path: "/" },
        { id: "user-management", label: "Users", icon: Users, path: "/user-management" },
        { id: "role-permissions", label: "Role Permissions", icon: UserPlusIcon, path: "/role-permissions" },
        { id: "courses", label: "Courses", icon: BookOpen, path: "/courses" },
        { id: "gradebook", label: "Gradebook", icon: GraduationCap, path: "/gradebook" },
        {
          id: "sandbox-management",
          label: "Sandbox Management",
          icon: Terminal,
          expandable: true,
          submenu: [
            { id: "sandbox-management", label: "Overview", icon: Terminal, path: "/sandbox-management" },
            { id: "time-extensions", label: "Time Extensions", icon: Clock, path: "/time-extensions" },
          ],
        },
      ];
    } else if (user.role === "instructor") {
      return {
        primary: [
          { id: "dashboard", label: "Dashboard", icon: Home, path: "/" },
          { id: "courses", label: "Courses", icon: BookOpen, path: "/courses" },
          { id: "gradebook", label: "Gradebook", icon: GraduationCap, path: "/gradebook" },
          { id: "submissions", label: "Submissions", icon: FileText, path: "/submissions" },
          { id: "communication", label: "Communication", icon: MessageSquare, path: "/communication" },
        ],
        management: [
          { id: "manage-projects", label: "Manage Projects", icon: FolderOpen, path: "/manage-projects" },
          { id: "user-management", label: "Students", icon: Users, path: "/user-management" },
          {
            id: "sandbox-management",
            label: "Sandbox Management",
            icon: Terminal,
            expandable: true,
            submenu: [
              { id: "sandbox-management", label: "Overview", icon: Terminal, path: "/sandbox-management" },
              { id: "time-extensions", label: "Time Extensions", icon: Clock, path: "/time-extensions" },
            ],
          },
        ],
      };
    } else {
      return [
        { id: "dashboard", label: "Dashboard", icon: Home, path: "/" },
        { id: "courses", label: "Courses", icon: BookOpen, path: "/courses" },
        // { id: "notebook", label: "Notebook", icon: Code, path: "/notebooks" },
        { id: "submissions", label: "Submissions", icon: FileText, path: "/submissions" },
        { id: "gradebook", label: "Grades", icon: GraduationCap, path: "/gradebook" },
        { id: "sandbox", label: "Sandbox", icon: Terminal, path: "/sandbox" },
      ];
    }
  }

  const adminToolsItems = [
    { id: "manage-projects", label: "Manage Projects", icon: FolderOpen, path: "/manage-projects" },
    { id: "sandbox-analytics", label: "Sandbox Analytics", icon: BarChart3, path: "/sandbox-analytics" },
    { id: "admin-reports", label: "Reports & Analytics", icon: BarChart3, path: "/admin-reports" },
    { id: "admin-audit", label: "Audit Logs", icon: Shield, path: "/admin-audit" },
    { id: "admin-settings", label: "System Settings", icon: Settings, path: "/admin-settings" },
  ];

  const renderMenuItem = (item: any, isSubmenu = false) => {
    const isActive = location.pathname === item.path;
    const isExpanded = expandedMenus[item.id];

    return (
      <li key={item.id} className={isSubmenu ? "ml-4" : ""}>
        <button
          onClick={() => {
            console.log("here")
            if (item.expandable) {
              toggleMenu(item.id);
            } else {
              console.log(item.path)
              navigate(item.path)
              //navigateTo(item.id);
            }
          }}
          className={`
            w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors
            ${isActive
              ? "bg-bits-blue text-white"
              : "text-gray-600 hover:bg-gray-100"
            }
            ${isSubmenu ? "text-xs pl-6" : ""}
          `}
        >
          <item.icon className={`${isSubmenu ? "w-3 h-3" : "w-4 h-4"} mr-3`} />
          <span className="flex-1 text-left">{item.label}</span>
          {item.expandable && (
            <div className="ml-auto">
              {isExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </div>
          )}
        </button>

        {item.expandable && item.submenu && isExpanded && (
          <ul className="space-y-1 mt-1">
            {item.submenu.map((subItem: any) => renderMenuItem(subItem, true))}
          </ul>
        )}
      </li>
    );
  };

  const menuItems: any = getMenuItems();

  return (
    // <div className="fixed left-0 z-40 bg-white border-gray-200 w-64 bg-sidebar border-r border-sidebar-border flex flex-col flex-shrink-0 z-30">
    <div className="fixed left-0 z-40 bg-white w-64 border-r border-gray-200 flex flex-col flex-shrink-0">
      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 overflow-y-auto">
        {user.role === "instructor" ? (
          // Instructor navigation with two sections
          <>
            {/* Primary Teaching Functions */}
            <div className="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
              Teaching & Grading
            </div>
            <ul className="space-y-1 mt-2">
              {menuItems.primary.map((item: any) => renderMenuItem(item))}
            </ul>

            {/* Management Functions */}
            <div className="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider mt-6">
              Course Management
            </div>
            <ul className="space-y-1 mt-2">
              {menuItems.management.map((item: any) => renderMenuItem(item))}
            </ul>
          </>
        ) : (
          // Regular navigation for admin and student roles
          <ul className="space-y-1">
            {(Array.isArray(menuItems) ? menuItems : []).map((item) =>
              renderMenuItem(item)
            )}
          </ul>
        )}

        {user.role === "admin" && (
          <>
            <div className="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider mt-6">
              Admin Tools
            </div>
            <ul className="space-y-1 mt-2">
              {adminToolsItems.map((item) => renderMenuItem(item))}
            </ul>
          </>
        )}
      </nav>
    </div>
  );
}

export default MainSidebar