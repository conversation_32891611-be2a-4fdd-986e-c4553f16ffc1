import React from "react";
import { But<PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import {
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  Play,
  Square,
  Activity,
  Clock,
  User,
  FileText,
  Keyboard,
} from "lucide-react";
import { useSubmissionNavigation } from "../contexts/SubmissionNavigationContext";
import { useSandboxSession } from "../contexts/SandboxSessionContext";
import { toast } from "sonner";

interface SubmissionNavigationControlsProps {
  onOpenSandbox?: () => void;
  showSandboxControls?: boolean;
  className?: string;
}

export function SubmissionNavigationControls({
  onOpenSandbox,
  showSandboxControls = true,
  className = "",
}: SubmissionNavigationControlsProps) {
  const {
    submissions,
    currentIndex,
    currentSubmission,
    goToNext,
    goToPrevious,
    goToSubmission,
    hasNext,
    hasPrevious,
    assignmentTitle,
  } = useSubmissionNavigation();

  const {
    currentSession,
    activeSessions,
    isLoading: sandboxLoading,
    shutdownAllSessions,
    getSessionStatus,
  } = useSandboxSession();

  const handleKeyboardNavigation = React.useCallback(
    (event: KeyboardEvent) => {
      // Only handle keyboard shortcuts when not typing in an input
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement
      ) {
        return;
      }

      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case "ArrowLeft":
            event.preventDefault();
            if (hasPrevious) {
              goToPrevious();
            }
            break;
          case "ArrowRight":
            event.preventDefault();
            if (hasNext) {
              goToNext();
            }
            break;
          case "Enter":
            event.preventDefault();
            if (onOpenSandbox && currentSubmission) {
              onOpenSandbox();
            }
            break;
        }
      }
    },
    [
      hasPrevious,
      hasNext,
      goToPrevious,
      goToNext,
      onOpenSandbox,
      currentSubmission,
    ]
  );

  React.useEffect(() => {
    document.addEventListener("keydown", handleKeyboardNavigation);
    return () =>
      document.removeEventListener("keydown", handleKeyboardNavigation);
  }, [handleKeyboardNavigation]);

  const handleSubmissionSelect = (submissionId: string) => {
    const index = submissions.findIndex((s) => s.id === submissionId);
    if (index >= 0) {
      goToSubmission(index);
    }
  };

  const currentSessionForSubmission = currentSubmission
    ? getSessionStatus(
        currentSubmission.studentId,
        currentSubmission.assignmentId
      )
    : null;

  const getSandboxStatusInfo = () => {
    if (!currentSubmission) return null;

    const session = currentSessionForSubmission;
    if (!session)
      return {
        status: "inactive",
        text: "Sandbox not running",
        color: "secondary",
      };

    switch (session.status) {
      case "starting":
        return { status: "starting", text: "Starting up...", color: "default" };
      case "active":
        return { status: "active", text: "Sandbox active", color: "success" };
      case "shutting-down":
        return {
          status: "shutting-down",
          text: "Shutting down...",
          color: "destructive",
        };
      default:
        return {
          status: "inactive",
          text: "Sandbox not running",
          color: "secondary",
        };
    }
  };

  const sandboxStatus = getSandboxStatusInfo();

  if (!currentSubmission) {
    return null;
  }

  return (
    <div className={`border-b border-gray-200 bg-white ${className}`}>
      <div className="px-6 py-4">
        {/* Assignment Context */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <FileText className="h-5 w-5 text-gray-500" />
            <div>
              <h2 className="font-medium text-gray-900">{assignmentTitle}</h2>
              <p className="text-sm text-gray-500">Reviewing submissions</p>
            </div>
          </div>

          {/* Active Sessions Indicator */}
          {activeSessions.length > 0 && (
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4 text-green-500" />
              <Badge
                variant="outline"
                className="text-green-700 border-green-200"
              >
                {activeSessions.length} active session
                {activeSessions.length !== 1 ? "s" : ""}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={shutdownAllSessions}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Square className="h-3 w-3 mr-1" />
                Shutdown All
              </Button>
            </div>
          )}
        </div>

        {/* Main Navigation Controls */}
        <div className="flex items-center justify-between">
          {/* Navigation Controls */}
          <div className="flex items-center space-x-4">
            {/* Previous/Next Buttons */}
            <div className="flex items-center space-x-1">
              <Button
                variant="outline"
                size="sm"
                onClick={goToPrevious}
                disabled={!hasPrevious}
                className="flex items-center space-x-1"
              >
                <ChevronLeft className="h-4 w-4" />
                <span>Previous</span>
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={goToNext}
                disabled={!hasNext}
                className="flex items-center space-x-1"
              >
                <span>Next</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Submission Counter & Selector */}
            <div className="flex items-center space-x-3">
              <Badge variant="outline">
                {currentIndex + 1} of {submissions.length}
              </Badge>

              <Select
                value={currentSubmission.id}
                onValueChange={handleSubmissionSelect}
              >
                <SelectTrigger className="w-64">
                  <SelectValue>
                    <div className="flex items-center space-x-2">
                      <User className="h-3 w-3" />
                      <span className="truncate">
                        {currentSubmission.studentName}
                      </span>
                    </div>
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {submissions.map((submission, index) => (
                    <SelectItem key={submission.id} value={submission.id}>
                      <div className="flex items-center justify-between w-full">
                        <span className="truncate">
                          {submission.studentName}
                        </span>
                        <div className="flex items-center space-x-2 ml-2">
                          <Badge
                            variant={
                              submission.status === "submitted"
                                ? "default"
                                : "secondary"
                            }
                            className="text-xs"
                          >
                            {submission.status}
                          </Badge>
                          {getSessionStatus(
                            submission.studentId,
                            submission.assignmentId
                          ) && <Activity className="h-3 w-3 text-green-500" />}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Sandbox Controls */}
          {showSandboxControls && (
            <div className="flex items-center space-x-3">
              {/* Sandbox Status */}
              {sandboxStatus && (
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={sandboxStatus.color as any}
                    className="flex items-center space-x-1"
                  >
                    {sandboxStatus.status === "active" && (
                      <Activity className="h-3 w-3" />
                    )}
                    {sandboxStatus.status === "starting" && (
                      <Clock className="h-3 w-3" />
                    )}
                    {sandboxStatus.status === "shutting-down" && (
                      <RotateCcw className="h-3 w-3 animate-spin" />
                    )}
                    <span>{sandboxStatus.text}</span>
                  </Badge>
                </div>
              )}

              {/* Open Sandbox Button */}
              <Button
                onClick={onOpenSandbox}
                disabled={
                  sandboxLoading || sandboxStatus?.status === "starting"
                }
                className="bg-bits-blue hover:bg-bits-blue/90 flex items-center space-x-2"
              >
                <Play className="h-4 w-4" />
                <span>
                  {currentSessionForSubmission?.status === "active"
                    ? "Open Sandbox"
                    : "Launch Sandbox"}
                </span>
              </Button>
            </div>
          )}
        </div>

        {/* Keyboard Shortcuts Hint */}
        <div className="mt-3 flex items-center space-x-4 text-xs text-gray-500">
          <div className="flex items-center space-x-1">
            <Keyboard className="h-3 w-3" />
            <span>Shortcuts:</span>
          </div>
          <span>Ctrl + ← Previous</span>
          <span>Ctrl + → Next</span>
          {onOpenSandbox && <span>Ctrl + Enter Open Sandbox</span>}
        </div>
      </div>
    </div>
  );
}
