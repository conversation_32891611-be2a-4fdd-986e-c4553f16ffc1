import React, { useState, use<PERSON>emo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./ui/card";
import { But<PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { Textarea } from "./ui/textarea";
import { Badge } from "./ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "./ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./ui/table";
import { Checkbox } from "./ui/checkbox";
import { toast } from "sonner";
import {
  Clock,
  Calendar,
  User,
  <PERSON><PERSON><PERSON>,
  FileText,
  Check,
  X,
  Filter,
  Search,
  History,
  AlertCircle,
  CheckCircle,
  XCircle,
  Timer,
} from "lucide-react";

// Mock data for extension requests
const mockExtensionRequests = [
  {
    id: "ext-001",
    studentId: "stud-001",
    studentName: "Rahul Sharma",
    studentEmail: "<EMAIL>",
    courseId: "course-001",
    courseName: "Data Science Fundamentals",
    projectId: "proj-001",
    projectName: "Customer Analytics Project",
    requestedDuration: "3 days",
    requestedDurationHours: 72,
    reason:
      "I need additional time to complete the data preprocessing phase due to unexpected issues with the dataset quality.",
    submittedAt: "2024-01-15T10:30:00Z",
    originalDueDate: "2024-01-18T23:59:00Z",
    status: "pending",
    priority: "normal",
    attachments: ["medical_certificate.pdf"],
  },
  {
    id: "ext-002",
    studentId: "stud-002",
    studentName: "Priya Patel",
    studentEmail: "<EMAIL>",
    courseId: "course-001",
    courseName: "Data Science Fundamentals",
    projectId: "proj-002",
    projectName: "Market Segmentation Analysis",
    requestedDuration: "5 days",
    requestedDurationHours: 120,
    reason:
      "Family emergency requiring travel. I have supporting documentation.",
    submittedAt: "2024-01-14T14:20:00Z",
    originalDueDate: "2024-01-20T23:59:00Z",
    status: "pending",
    priority: "high",
    attachments: ["emergency_documentation.pdf"],
  },
  {
    id: "ext-003",
    studentId: "stud-003",
    studentName: "Arjun Mehta",
    studentEmail: "<EMAIL>",
    courseId: "course-002",
    courseName: "Machine Learning Applications",
    projectId: "proj-003",
    projectName: "Predictive Modeling Project",
    requestedDuration: "2 days",
    requestedDurationHours: 48,
    reason:
      "Technical issues with cloud computing resources preventing model training completion.",
    submittedAt: "2024-01-16T09:15:00Z",
    originalDueDate: "2024-01-19T23:59:00Z",
    status: "pending",
    priority: "normal",
    attachments: [],
  },
  {
    id: "ext-004",
    studentId: "stud-004",
    studentName: "Sneha Kumar",
    studentEmail: "<EMAIL>",
    courseId: "course-001",
    courseName: "Data Science Fundamentals",
    projectId: "proj-001",
    projectName: "Customer Analytics Project",
    requestedDuration: "1 day",
    requestedDurationHours: 24,
    reason: "Minor illness affecting productivity. Expected to recover soon.",
    submittedAt: "2024-01-13T16:45:00Z",
    originalDueDate: "2024-01-18T23:59:00Z",
    status: "pending",
    priority: "low",
    attachments: [],
  },
];

// Mock data for extension history
const mockExtensionHistory = [
  {
    id: "ext-h001",
    studentId: "stud-005",
    studentName: "Vikram Singh",
    studentEmail: "<EMAIL>",
    courseId: "course-001",
    courseName: "Data Science Fundamentals",
    projectId: "proj-001",
    projectName: "Customer Analytics Project",
    requestedDuration: "2 days",
    grantedDuration: "2 days",
    reason: "Hardware failure during final testing phase.",
    submittedAt: "2024-01-10T11:20:00Z",
    processedAt: "2024-01-10T15:30:00Z",
    processedBy: "Dr. A. Sharma",
    status: "approved",
    reviewComments: "Valid technical issue. Extension granted.",
  },
  {
    id: "ext-h002",
    studentId: "stud-006",
    studentName: "Anjali Gupta",
    studentEmail: "<EMAIL>",
    courseId: "course-002",
    courseName: "Machine Learning Applications",
    projectId: "proj-003",
    projectName: "Predictive Modeling Project",
    requestedDuration: "7 days",
    grantedDuration: "0 days",
    reason: "Poor time management, left everything for the last minute.",
    submittedAt: "2024-01-08T22:10:00Z",
    processedAt: "2024-01-09T09:00:00Z",
    processedBy: "Dr. B. Patel",
    status: "rejected",
    reviewComments:
      "Insufficient justification. Students are expected to manage time effectively.",
  },
  {
    id: "ext-h003",
    studentId: "stud-007",
    studentName: "Rohan Joshi",
    studentEmail: "<EMAIL>",
    courseId: "course-001",
    courseName: "Data Science Fundamentals",
    projectId: "proj-002",
    projectName: "Market Segmentation Analysis",
    requestedDuration: "3 days",
    grantedDuration: "0 days",
    reason: "Missed the extension deadline.",
    submittedAt: "2024-01-05T10:00:00Z",
    processedAt: "2024-01-12T10:00:00Z",
    processedBy: "System",
    status: "expired",
    reviewComments:
      "Request expired due to late submission after project due date.",
  },
];

interface TimeExtensionManagementProps {
  userRole?: "admin" | "instructor";
}

export default function TimeExtensionManagement({
  userRole = "instructor",
}: TimeExtensionManagementProps) {
  const [selectedTab, setSelectedTab] = useState("pending");
  const [selectedRequests, setSelectedRequests] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [courseFilter, setCourseFilter] = useState("all");
  const [projectFilter, setProjectFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");

  // Modal states
  const [selectedRequest, setSelectedRequest] = useState<any>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [bulkAction, setBulkAction] = useState<"approve" | "reject">("approve");

  // Form states for request processing
  const [customDuration, setCustomDuration] = useState("");
  const [customDurationUnit, setCustomDurationUnit] = useState("days");
  const [reviewComments, setReviewComments] = useState("");

  // Get unique values for filters
  const uniqueCourses = useMemo(() => {
    const courses = [...mockExtensionRequests, ...mockExtensionHistory].map(
      (req) => ({
        id: req.courseId,
        name: req.courseName,
      })
    );
    return Array.from(new Map(courses.map((c) => [c.id, c])).values());
  }, []);

  const uniqueProjects = useMemo(() => {
    const projects = [...mockExtensionRequests, ...mockExtensionHistory].map(
      (req) => ({
        id: req.projectId,
        name: req.projectName,
      })
    );
    return Array.from(new Map(projects.map((p) => [p.id, p])).values());
  }, []);

  // Filter pending requests
  const filteredPendingRequests = useMemo(() => {
    return mockExtensionRequests.filter((request) => {
      const matchesSearch =
        searchTerm === "" ||
        request.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.studentEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.courseName.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCourse =
        courseFilter === "all" || request.courseId === courseFilter;
      const matchesProject =
        projectFilter === "all" || request.projectId === projectFilter;
      const matchesPriority =
        priorityFilter === "all" || request.priority === priorityFilter;

      return (
        matchesSearch && matchesCourse && matchesProject && matchesPriority
      );
    });
  }, [searchTerm, courseFilter, projectFilter, priorityFilter]);

  // Filter history
  const filteredHistory = useMemo(() => {
    return mockExtensionHistory.filter((request) => {
      const matchesSearch =
        searchTerm === "" ||
        request.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.studentEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.courseName.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCourse =
        courseFilter === "all" || request.courseId === courseFilter;
      const matchesProject =
        projectFilter === "all" || request.projectId === projectFilter;
      const matchesStatus =
        statusFilter === "all" || request.status === statusFilter;

      return matchesSearch && matchesCourse && matchesProject && matchesStatus;
    });
  }, [searchTerm, courseFilter, projectFilter, statusFilter]);

  const handleSelectRequest = (requestId: string, checked: boolean) => {
    if (checked) {
      setSelectedRequests((prev) => [...prev, requestId]);
    } else {
      setSelectedRequests((prev) => prev.filter((id) => id !== requestId));
    }
  };

  const handleSelectAllRequests = (checked: boolean) => {
    if (checked) {
      setSelectedRequests(filteredPendingRequests.map((req) => req.id));
    } else {
      setSelectedRequests([]);
    }
  };

  const handleViewDetails = (request: any) => {
    setSelectedRequest(request);
    setCustomDuration(request.requestedDurationHours.toString());
    setCustomDurationUnit("hours");
    setReviewComments("");
    setShowDetailsModal(true);
  };

  const handleProcessSingleRequest = (action: "approve" | "reject") => {
    const duration =
      action === "approve"
        ? `${customDuration} ${customDurationUnit}`
        : "0 hours";

    toast.success(`Request ${action}d successfully`);
    setShowDetailsModal(false);
    setSelectedRequest(null);
  };

  const handleBulkAction = () => {
    if (selectedRequests.length === 0) {
      toast.error("Please select at least one request");
      return;
    }

    setBulkAction("approve");
    setShowBulkModal(true);
  };

  const handleConfirmBulkAction = () => {
    toast.success(
      `${selectedRequests.length} requests ${bulkAction}d successfully`
    );
    setSelectedRequests([]);
    setShowBulkModal(false);
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800";
      case "normal":
        return "bg-blue-100 text-blue-800";
      case "low":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "approved":
        return "bg-green-100 text-green-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      case "expired":
        return "bg-gray-100 text-gray-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle className="w-4 h-4" />;
      case "rejected":
        return <XCircle className="w-4 h-4" />;
      case "expired":
        return <Timer className="w-4 h-4" />;
      case "pending":
        return <Clock className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="space-y-6 mt-8 ml-8">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Time Extension Requests
          </h1>
          <p className="text-gray-600 mt-2">
            Manage student requests for project deadline extensions
          </p>
        </div>
        <div className="flex gap-2">
          <Badge variant="outline" className="text-yellow-600">
            {filteredPendingRequests.length} Pending
          </Badge>
          <Badge variant="outline" className="text-gray-600">
            {filteredHistory.length} Processed
          </Badge>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="space-y-2">
              <Label>Search</Label>
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
                <Input
                  placeholder="Search students, projects..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Course</Label>
              <Select value={courseFilter} onValueChange={setCourseFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Courses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Courses</SelectItem>
                  {uniqueCourses.map((course) => (
                    <SelectItem key={course.id} value={course.id}>
                      {course.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Project</Label>
              <Select value={projectFilter} onValueChange={setProjectFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Projects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Projects</SelectItem>
                  {uniqueProjects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Priority</Label>
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Priorities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {selectedTab === "history" && (
              <div className="space-y-2">
                <Label>Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="expired">Expired</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="=flex grid-cols-2">
          <TabsTrigger value="pending" className="flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Pending Requests ({filteredPendingRequests.length})
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <History className="w-4 h-4" />
            History ({filteredHistory.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="space-y-4">
          {/* Bulk Actions */}
          {selectedRequests.length > 0 && (
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <span className="text-sm font-medium">
                      {selectedRequests.length} requests selected
                    </span>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => {
                          setBulkAction("approve");
                          handleBulkAction();
                        }}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Check className="w-4 h-4 mr-1" />
                        Bulk Approve
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => {
                          setBulkAction("reject");
                          handleBulkAction();
                        }}
                      >
                        <X className="w-4 h-4 mr-1" />
                        Bulk Reject
                      </Button>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSelectedRequests([])}
                  >
                    Clear Selection
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Pending Requests Table */}
          <Card>
            <CardHeader>
              <CardTitle>Pending Extension Requests</CardTitle>
              <CardDescription>
                Review and process student requests for project deadline
                extensions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={
                            selectedRequests.length ===
                              filteredPendingRequests.length &&
                            filteredPendingRequests.length > 0
                          }
                          onCheckedChange={handleSelectAllRequests}
                        />
                      </TableHead>
                      <TableHead>Student</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Project</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead>Due Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPendingRequests.map((request) => (
                      <TableRow key={request.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedRequests.includes(request.id)}
                            onCheckedChange={(checked) =>
                              handleSelectRequest(
                                request.id,
                                checked as boolean
                              )
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">
                              {request.studentName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {request.studentEmail}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{request.courseName}</div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{request.projectName}</div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {request.requestedDuration}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={getPriorityBadgeColor(request.priority)}
                          >
                            {request.priority}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {formatDate(request.submittedAt)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {formatDate(request.originalDueDate)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewDetails(request)}
                          >
                            View Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          {/* History Table */}
          <Card>
            <CardHeader>
              <CardTitle>Extension Request History</CardTitle>
              <CardDescription>
                View processed extension requests and their outcomes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Project</TableHead>
                      <TableHead>Requested</TableHead>
                      <TableHead>Granted</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Processed By</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredHistory.map((request) => (
                      <TableRow key={request.id}>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">
                              {request.studentName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {request.studentEmail}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{request.courseName}</div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{request.projectName}</div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {request.requestedDuration}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {request.grantedDuration}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={getStatusBadgeColor(request.status)}
                          >
                            <div className="flex items-center gap-1">
                              {getStatusIcon(request.status)}
                              {request.status}
                            </div>
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{request.processedBy}</div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {formatDate(request.processedAt)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewDetails(request)}
                          >
                            View Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Request Details Modal */}
      <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Extension Request Details</DialogTitle>
            <DialogDescription>
              Review and process the extension request
            </DialogDescription>
          </DialogHeader>

          {selectedRequest && (
            <div className="space-y-6">
              {/* Student and Project Info */}
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">
                      Student
                    </Label>
                    <div className="mt-1">
                      <div className="font-medium">
                        {selectedRequest.studentName}
                      </div>
                      <div className="text-sm text-gray-500">
                        {selectedRequest.studentEmail}
                      </div>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">
                      Course
                    </Label>
                    <div className="mt-1 font-medium">
                      {selectedRequest.courseName}
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">
                      Project
                    </Label>
                    <div className="mt-1 font-medium">
                      {selectedRequest.projectName}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">
                      Requested Duration
                    </Label>
                    <div className="mt-1">
                      <Badge variant="outline" className="text-sm">
                        {selectedRequest.requestedDuration}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">
                      Priority
                    </Label>
                    <div className="mt-1">
                      <Badge
                        className={getPriorityBadgeColor(
                          selectedRequest.priority
                        )}
                      >
                        {selectedRequest.priority}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">
                      Original Due Date
                    </Label>
                    <div className="mt-1 font-medium">
                      {formatDate(selectedRequest.originalDueDate)}
                    </div>
                  </div>
                </div>
              </div>

              {/* Reason */}
              <div>
                <Label className="text-sm font-medium text-gray-500">
                  Reason for Extension
                </Label>
                <div className="mt-2 p-3 bg-gray-50 rounded-md">
                  <p className="text-sm">{selectedRequest.reason}</p>
                </div>
              </div>

              {/* Attachments */}
              {selectedRequest.attachments &&
                selectedRequest.attachments.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">
                      Attachments
                    </Label>
                    <div className="mt-2 space-y-2">
                      {selectedRequest.attachments.map(
                        (attachment: string, index: number) => (
                          <div
                            key={index}
                            className="flex items-center gap-2 p-2 bg-gray-50 rounded-md"
                          >
                            <FileText className="w-4 h-4 text-gray-400" />
                            <span className="text-sm">{attachment}</span>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="ml-auto"
                            >
                              Download
                            </Button>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}

              {/* Only show processing controls for pending requests */}
              {selectedRequest.status === "pending" && (
                <>
                  {/* Duration Decision */}
                  <div className="space-y-4 border-t pt-4">
                    <Label className="text-sm font-medium">
                      Grant Extension Duration
                    </Label>
                    <div className="flex gap-4">
                      <div className="flex-1">
                        <Label className="text-xs text-gray-500">
                          Duration
                        </Label>
                        <Input
                          type="number"
                          value={customDuration}
                          onChange={(e) => setCustomDuration(e.target.value)}
                          placeholder="Enter duration"
                        />
                      </div>
                      <div className="w-32">
                        <Label className="text-xs text-gray-500">Unit</Label>
                        <Select
                          value={customDurationUnit}
                          onValueChange={setCustomDurationUnit}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="hours">Hours</SelectItem>
                            <SelectItem value="days">Days</SelectItem>
                            <SelectItem value="weeks">Weeks</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">
                      Leave as 0 to reject the request
                    </div>
                  </div>

                  {/* Review Comments */}
                  <div className="space-y-2">
                    <Label>Review Comments</Label>
                    <Textarea
                      value={reviewComments}
                      onChange={(e) => setReviewComments(e.target.value)}
                      placeholder="Add comments for the student..."
                      rows={3}
                    />
                  </div>
                </>
              )}

              {/* Show decision info for processed requests */}
              {selectedRequest.status !== "pending" && (
                <div className="border-t pt-4 space-y-3">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">
                      Decision
                    </Label>
                    <div className="mt-1">
                      <Badge
                        className={getStatusBadgeColor(selectedRequest.status)}
                      >
                        <div className="flex items-center gap-1">
                          {getStatusIcon(selectedRequest.status)}
                          {selectedRequest.status}
                        </div>
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">
                      Review Comments
                    </Label>
                    <div className="mt-2 p-3 bg-gray-50 rounded-md">
                      <p className="text-sm">
                        {selectedRequest.reviewComments}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            {selectedRequest?.status === "pending" ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => setShowDetailsModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="default"
                  className="bg-bits-red"
                  onClick={() => handleProcessSingleRequest("reject")}
                >
                  <X className="w-4 h-4 mr-1" />
                  Reject
                </Button>
                <Button
                  onClick={() => handleProcessSingleRequest("approve")}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Check className="w-4 h-4 mr-1" />
                  Approve
                </Button>
              </>
            ) : (
              <Button
                variant="outline"
                onClick={() => setShowDetailsModal(false)}
              >
                Close
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Action Modal */}
      <Dialog open={showBulkModal} onOpenChange={setShowBulkModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              Bulk {bulkAction === "approve" ? "Approve" : "Reject"} Requests
            </DialogTitle>
            <DialogDescription>
              You are about to {bulkAction} {selectedRequests.length} extension
              requests.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label>Selected Requests: {selectedRequests.length}</Label>
              <div className="text-sm text-gray-500">
                This action cannot be undone. All selected requests will be{" "}
                {bulkAction}d.
              </div>
            </div>

            {bulkAction === "approve" && (
              <div className="space-y-2">
                <Label>Default Extension Duration</Label>
                <div className="flex gap-4">
                  <div className="flex-1">
                    <Input
                      type="number"
                      value={customDuration}
                      onChange={(e) => setCustomDuration(e.target.value)}
                      placeholder="Enter duration"
                    />
                  </div>
                  <div className="w-32">
                    <Select
                      value={customDurationUnit}
                      onValueChange={setCustomDurationUnit}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="hours">Hours</SelectItem>
                        <SelectItem value="days">Days</SelectItem>
                        <SelectItem value="weeks">Weeks</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label>Bulk Review Comments</Label>
              <Textarea
                value={reviewComments}
                onChange={(e) => setReviewComments(e.target.value)}
                placeholder="Add comments that will be sent to all selected students..."
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBulkModal(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleConfirmBulkAction}
              className={
                bulkAction === "approve"
                  ? "bg-green-600 hover:bg-green-700"
                  : ""
              }
              variant={bulkAction === "reject" ? "destructive" : "default"}
            >
              {bulkAction === "approve" ? (
                <>
                  <Check className="w-4 h-4 mr-1" />
                  Bulk Approve
                </>
              ) : (
                <>
                  <X className="w-4 h-4 mr-1" />
                  Bulk Reject
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
