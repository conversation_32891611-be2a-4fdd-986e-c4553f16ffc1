import React from "react";
import { Badge } from "./ui/badge";
import { Label } from "./ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./ui/dialog";
import {
  Mail,
  Phone,
  MapPin,
  Calendar,
  User,
  GraduationCap,
  UserCheck,
  UserCog,
  ShieldCheck,
  FolderOpen,
  Clock,
  CheckCircle,
  AlertCircle,
  FileText,
} from "lucide-react";

// Updated interface to accommodate project data
interface ProjectData {
  id: string;
  name: string;
  status: "In Progress" | "Submitted" | "Pending" | "Completed" | "Not Started";
  grade?: string;
  submissionDate?: string;
  dueDate?: string;
  progress?: number;
}

interface UserProfileData {
  // Core fields
  studentId?: string;
  userId?: string;
  name: string;
  email: string;
  role: string;
  status?: string;

  // Contact & Personal
  phone?: string;
  address?: string;
  dateOfBirth?: string;
  startDate?: string;
  enrollmentDate?: string;

  // Academic (for students)
  currentGPA?: string | number;
  gpa?: string | number;
  totalCredits?: number;
  academicYear?: string;
  year?: string;
  department?: string;

  // Professional (for instructors/TAs)
  specialization?: string;
  experience?: string;

  // Course associations with projects
  courses?: Array<{
    name?: string;
    courseName?: string;
    code?: string;
    courseCode?: string;
    instructor: string;
    enrolledOn?: string;
    enrollmentDate?: string;
    currentGrade?: string;
    status?: string;
    role?: string;
    projects?: ProjectData[];
  }>;
}

interface UserProfileModalProps {
  user: UserProfileData | null;
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  fromPlatformGrade?: boolean;
}

export function UserProfileModal({
  user,
  trigger,
  open,
  onOpenChange,
  fromPlatformGrade = false,
}: UserProfileModalProps) {
  // Return early if no user data

  if (!user) {
    return null;
  }
  console.log(fromPlatformGrade, user);
  const getRoleIcon = (role: string) => {
    switch (role?.toLowerCase()) {
      case "student":
        return <GraduationCap className="h-4 w-4" />;
      case "ta":
        return <UserCheck className="h-4 w-4" />;
      case "instructor":
        return <UserCog className="h-4 w-4" />;
      case "admin":
        return <ShieldCheck className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const getProjectStatusIcon = (status: string) => {
    switch (status) {
      case "Completed":
      case "Submitted":
        return <CheckCircle className="h-4 w-4 text-bits-gold" />;
      case "In Progress":
        return <Clock className="h-4 w-4 text-bits-blue" />;
      case "Pending":
        return <AlertCircle className="h-4 w-4 text-bits-gold" />;
      case "Not Started":
        return <FileText className="h-4 w-4 text-muted-foreground" />;
      default:
        return <FileText className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getProjectStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-bits-completed text-bits-green-shade border-none";
      case "Submitted":
        return "bg-bits-gold/10 text-bits-gold border-bits-gold";
      case "In Progress":
        return "bg-bits-blue/10 text-bits-blue border-bits-blue";
      case "Pending":
        return "bg-bits-gold/10 text-bits-gold border-bits-gold";
      case "Not Started":
        return "bg-muted text-muted-foreground border-border";
      case "Overdue":
        return "bg-bits-overdue text-bits-strong-red border-none";
      default:
        return "bg-muted text-muted-foreground border-border";
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleDateString();
  };

  const getDisplayGPA = () => {
    const gpa = user.currentGPA || user.gpa;
    if (gpa === undefined || gpa === null || gpa === "N/A") return "N/A";

    // Convert to number and format to 1 decimal place
    const numericGPA = typeof gpa === "string" ? parseFloat(gpa) : gpa;
    return isNaN(numericGPA) ? "N/A" : numericGPA.toFixed(1);
  };

  const getDisplayYear = () => {
    return user.academicYear || user.year || "N/A";
  };

  const getDisplayUserId = () => {
    return user.studentId || user.userId || "N/A";
  };

  const getDisplayStartDate = () => {
    return user.startDate || user.enrollmentDate || "";
  };

  // Mock project data for demonstration (in real app, this would come from the API)
  const getMockProjectsForCourse = (courseId: string): ProjectData[] => {
    const projectSets = [
      [
        {
          id: "1",
          name: "Housing Price Prediction",
          status: "Submitted" as const,
          grade: "A-",
          submissionDate: "2024-01-15",
          progress: 100,
        },
        {
          id: "2",
          name: "Customer Segmentation",
          status: "In Progress" as const,
          dueDate: "2024-02-10",
          progress: 65,
        },
        {
          id: "3",
          name: "Sentiment Analysis",
          status: "Pending" as const,
          grade: "B+",
          submissionDate: "2024-01-10",
          progress: 100,
        },
      ],
      [
        {
          id: "4",
          name: "Neural Network Implementation",
          status: "In Progress" as const,
          dueDate: "2024-02-15",
          progress: 45,
        },
        {
          id: "5",
          name: "Computer Vision Project",
          status: "Not Started" as const,
          dueDate: "2024-03-01",
          progress: 0,
        },
      ],
      [
        {
          id: "6",
          name: "Database Design Project",
          status: "Completed" as const,
          grade: "A+",
          submissionDate: "2024-01-20",
          progress: 100,
        },
        {
          id: "7",
          name: "Query Optimization",
          status: "Submitted" as const,
          grade: "A",
          submissionDate: "2024-01-25",
          progress: 100,
        },
      ],
    ];

    // Simple hash to get consistent projects for each course
    let hash = 0;
    for (let i = 0; i < courseId.length; i++) {
      hash = courseId.charCodeAt(i) + ((hash << 5) - hash);
    }
    const index = Math.abs(hash) % projectSets.length;
    return projectSets[index];
  };

  // Handle controlled vs uncontrolled dialog
  const dialogProps = trigger ? {} : { open, onOpenChange };

  return (
    <Dialog {...dialogProps}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto bg-white border">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="h-5 w-5 text-blue-600" />
            <span>User Profile</span>
          </DialogTitle>
          <DialogDescription>
            Complete profile information for {user.name || "Unknown User"}.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Basic Information Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Full Name
              </Label>
              <p className="text-sm text-gray-900">
                {user.name || "Not specified"}
              </p>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Role</Label>
              <Badge variant="secondary" className="text-blue-600 bg-blue-50">
                {getRoleIcon(user.role)}
                <span className="ml-1 capitalize">
                  {user.role || "Unknown"}
                </span>
              </Badge>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                User ID
              </Label>
              <p className="text-sm text-gray-900">{getDisplayUserId()}</p>
            </div>
            {user.status && (
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  Status
                </Label>
                <Badge
                  variant="secondary"
                  className="text-green-600 bg-green-50"
                >
                  {user.status}
                </Badge>
              </div>
            )}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Email</Label>
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-gray-500" />
                <p className="text-sm text-blue-600">
                  {user.email || "Not specified"}
                </p>
              </div>
            </div>

            {fromPlatformGrade ? (
              <></>
            ) : (
              /* user.phone && (
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Phone</Label>
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <p className="text-sm text-gray-900">{user.phone}</p>
                </div>
              </div>
            ) */ <></>
            )}
            {fromPlatformGrade ? (
              <></>
            ) : (
              getDisplayStartDate() && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    Start Date
                  </Label>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <p className="text-sm text-gray-900">
                      {formatDate(getDisplayStartDate())}
                    </p>
                  </div>
                </div>
              )
            )}
            {!fromPlatformGrade ? (
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  Last login
                </Label>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <p className="text-sm text-gray-900">12:20 AM, 8/1/2024</p>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  Department
                </Label>
                <p className="text-sm text-gray-900">
                  {user.department || "Not specified"}
                </p>
              </div>
            )}
          </div>

          {/* Personal Information Section */}
          {/* 
          {fromPlatformGrade ? <></> :
            (user.dateOfBirth || user.address) && (
              <div className="border-t pt-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {user.dateOfBirth && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">Date of Birth</Label>
                      <p className="text-sm text-gray-900">{formatDate(user.dateOfBirth)}</p>
                    </div>
                  )}
                  {user.address && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">Address</Label>
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <p className="text-sm text-gray-900">{user.address}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )} */}

          {/* Professional Information Section (for instructors/TAs) */}
          {(user.specialization || user.experience) && (
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Professional Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {user.specialization && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">
                      Specialization
                    </Label>
                    <p className="text-sm text-gray-900">
                      {user.specialization}
                    </p>
                  </div>
                )}
                {user.experience && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">
                      Experience
                    </Label>
                    <p className="text-sm text-gray-900">{user.experience}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Course Association Section */}
          {(user.role?.toLowerCase() === "student" || user.courses?.length) && (
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {fromPlatformGrade
                  ? "Project Details"
                  : "Course & Project Association"}
              </h3>

              {/* Academic Stats for Students */}
              {/* {user.role?.toLowerCase() === 'student' && (
                <div className="grid grid-cols-3 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{getDisplayGPA()}</div>
                    <div className="text-sm text-gray-600">Current GPA</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{user.totalCredits || 'N/A'}</div>
                    <div className="text-sm text-gray-600">Total Credits</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{getDisplayYear()}</div>
                    <div className="text-sm text-gray-600">Academic year</div>
                  </div>
                </div>
              )} */}

              {/* Course Details with Projects */}
              {user.courses && user.courses.length > 0 && (
                <div
                  className="space-y-4"
                  style={{ backgroundColor: "#F9FAFB" }}
                >
                  {user.courses.map((course, index) => {
                    const courseProjects = getMockProjectsForCourse(
                      course.code || course.courseCode || `course-${index}`
                    );

                    return (
                      <div
                        key={index}
                        className="rounded-lg p-4 "
                        style={{ border: "1px solid #E4E7EC" }}
                      >
                        {/* Course Header */}
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-gray-900">
                            {course.name ||
                              course.courseName ||
                              "Unknown Course"}
                          </h4>
                          {/* <Badge variant="outline" className={getProjectStatusColor("Completed")}>

                            {fromPlatformGrade ? <p >Completed</p> : course.code || course.courseCode || 'N/A'}
                          </Badge> */}
                        </div>
                        {fromPlatformGrade ? (
                          <p className="pb-4 text-sm">
                            {course.name ||
                              course.courseName ||
                              "Unknown Course"}
                            <span>
                               • {course.instructor || "Not specified"}
                            </span>{" "}
                          </p>
                        ) : (
                          <></>
                        )}

                        {/* Course Details */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-gray-600">
                          {fromPlatformGrade ? (
                            ""
                          ) : (
                            <div>
                              <span className="font-semibold text-black">
                                Instructor:
                              </span>{" "}
                              {course.instructor || "Not specified"}
                            </div>
                          )}
                          {fromPlatformGrade ? (
                            <></>
                          ) : (
                            <div>
                              <span className="font-medium">Enrolled On:</span>{" "}
                              {formatDate(
                                course.enrolledOn || course.enrollmentDate
                              )}
                            </div>
                          )}
                          {fromPlatformGrade ? (
                            <>
                              <h1 className="font-bold text-2xl">
                                {course.currentGrade}
                                <span className=" text-lg font-medium text-bits-grey-600">
                                  {" "}
                                  Grade
                                </span>
                              </h1>
                            </>
                          ) : (
                            // course.currentGrade && (
                            //   <div>
                            //     <span className="font-medium">Current Grade:</span> {' '}
                            //     <Badge variant="outline" className={
                            //       course.currentGrade.startsWith('A') ? 'text-green-600 border-green-600' :
                            //         course.currentGrade.startsWith('B') ? 'text-blue-600 border-blue-600' :
                            //           'text-yellow-600 border-yellow-600'
                            //     }>
                            //       {course.currentGrade}
                            //     </Badge>
                            //   </div>
                            // )
                            <></>
                          )}
                          {fromPlatformGrade ? (
                            <></>
                          ) : (
                            // course.role && (
                            //   <div>
                            //     <span className="font-medium">Role:</span> {' '}
                            //     <Badge variant="outline" className="text-xs">
                            //       {course.role}
                            //     </Badge>
                            //   </div>
                            // )
                            <></>
                          )}
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-gray-600">
                          {fromPlatformGrade ? (
                            <div className="flex items-center gap-1 text-bits-grey-600 mt-5">
                              <Calendar className="w-4 h-4" />
                              <p className="text-xs">12 Jun - 25 Jul 2025</p>
                            </div>
                          ) : null}
                          {fromPlatformGrade ? (
                            <div className="flex text-bits-grey-600 mt-5">
                              <p className="flex  items-center gap-1 text-black text-xs">
                                <strong>Enrolled On:</strong>{" "}
                                <p className="text-xs">12/08/2025</p>
                              </p>
                            </div>
                          ) : null}
                        </div>

                        {/* Projects Section */}
                        {user.role?.toLowerCase() === "student" &&
                          courseProjects.length > 0 && (
                            <div className="mt-4 pt-3 border-t border-gray-200">
                              <div className="flex items-center space-x-2 mb-3">
                                {/* <FolderOpen className="h-4 w-4 text-blue-600" /> */}
                                <h5 className="font-medium text-gray-900">
                                  {" "}
                                  Projects
                                </h5>
                                {/* <Badge variant="secondary" className="text-xs">
                                {courseProjects.length} projects
                              </Badge> */}
                                <h2 className="font-medium">
                                  ({courseProjects.length})
                                </h2>
                              </div>

                              <div className="space-y-2">
                                {courseProjects.map((project) => (
                                  <div
                                    key={project.id}
                                    className="bg-white rounded border p-3"
                                  >
                                    <div className="flex items-center justify-between mb-2">
                                      <div className="flex items-center space-x-2">
                                        {getProjectStatusIcon(project.status)}
                                        <span className="font-medium text-sm">
                                          {project.name}
                                        </span>
                                      </div>
                                      <Badge
                                        variant="outline"
                                        className={getProjectStatusColor(
                                          project.status
                                        )}
                                      >
                                        {project.status}
                                      </Badge>
                                    </div>

                                    <div className="flex items-center justify-between text-xs text-gray-600">
                                      <div className="flex items-center space-x-4">
                                        {project.grade && (
                                          <span>
                                            <span className="font-medium">
                                              Grade:
                                            </span>{" "}
                                            <span
                                              className={
                                                project.grade.startsWith("A")
                                                  ? "text-green-600 font-medium"
                                                  : project.grade.startsWith(
                                                      "B"
                                                    )
                                                  ? "text-blue-600 font-medium"
                                                  : "text-yellow-600 font-medium"
                                              }
                                            >
                                              {project.grade}
                                            </span>
                                          </span>
                                        )}
                                        {project.submissionDate && (
                                          <span>
                                            <span className="font-medium">
                                              Submitted:
                                            </span>{" "}
                                            {formatDate(project.submissionDate)}
                                          </span>
                                        )}
                                        {project.dueDate &&
                                          !project.submissionDate && (
                                            <span className="flex gap-1">
                                              <span className="font-medium">
                                                {" "}
                                                <Calendar className="w-4 h-4" />
                                              </span>{" "}
                                              {formatDate(project.dueDate)}
                                            </span>
                                          )}
                                      </div>

                                      {project.progress !== undefined && (
                                        <div className="flex items-center space-x-2">
                                          {/* <span className="font-medium">Progress:</span> */}
                                          {/* <div className="w-12 h-2 bg-gray-200 rounded-full overflow-hidden">
                                          <div
                                            className={`h-full transition-all duration-300 ${project.progress === 100 ? 'bg-green-500' :
                                              project.progress >= 50 ? 'bg-blue-500' :
                                                'bg-yellow-500'
                                              }`}
                                            style={{ width: `${project.progress}%` }}
                                          />
                                        </div> */}
                                          {/* <span className="text-xs">{project.progress}%</span> */}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default UserProfileModal;
