import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>rigger,
} from "@/src/components/ui/dialog";
import { Check, Edit, X } from "lucide-react";
import { Roles } from "../pages/RolesPermissionsListPage";
import { Card } from "./ui/card";
import { EditPermissionsModal } from "./EditPermissionsModal";
import { Button } from "./ui/button";

export function ViewPermissionsModal({
  role,
  onSave,
  trigger,
}: {
  role: Roles;
  onSave: (updatedRole: Roles) => void;
  trigger: React.ReactNode;
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-medium">
            {role.roleName} Permissions
          </DialogTitle>
        </DialogHeader>

        <Card>
          <div className="space-y-4 max-h-[60vh] overflow-y-auto p-4">
            {role.permissions.map((permission) => (
              <div key={permission.id}>
                {/* Parent */}
                <div className="flex items-center gap-2">
                  {permission.isChecked ? (
                    <Check className="h-4 w-4 text-green-600" />
                  ) : (
                    <X className="h-4 w-4 text-red-600" />
                  )}
                  <span className="font-medium text-sm text-bits-grey-600">
                    {permission.name}
                  </span>
                </div>

                {/* Nested */}
                {permission.nestedPermissions && (
                  <div className="ml-6 mt-2 space-y-1">
                    {permission.nestedPermissions.map((nestedPermission) => (
                      <div
                        key={nestedPermission.name}
                        className="flex items-center gap-2"
                      >
                        {nestedPermission.isChecked ? (
                          <Check className="h-4 w-4 text-bits-green-success-500" />
                        ) : (
                          <X className="h-4 w-4 text-bits-error" />
                        )}
                        <span className="font-medium text-sm text-bits-grey-600">
                          {nestedPermission.name}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </Card>
        {/* Edit opens Edit modal */}
        {role.roleName !== "SA" && (
          <EditPermissionsModal
            role={role}
            onSave={onSave}
            trigger={
              <Button className="w-40">
                <Edit className="h-5 w-5 cursor-pointer text-bits-grey-700 hover:text-blue-700 transition-colors" />
                <span className="text-md font-semibold text-bits-grey-700">
                  Edit Permission
                </span>
              </Button>
            }
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
