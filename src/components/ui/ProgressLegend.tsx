import React from 'react';

const statusColorMap = {
   submitted: 'bg-bits-light-green',
  overdue: 'bg-bits-error',
  ongoing: 'bg-bits-orange',
  pending: 'bg-gray-300',
  graded: 'bg-bits-blue',
};

const legendItems = [
  { status: 'pending', label: 'Pending' },
  { status: 'ongoing', label: 'On Going' },
  { status: 'submitted', label: 'Submitted' },
  { status: 'overdue', label: 'Overdue' },
  { status: 'graded', label: 'Graded' },
];

interface ProgressGuideProps {
  className?: string;
  barWidth?: string;
  barHeight?: string;
}

const ProgressLegend: React.FC<ProgressGuideProps> = ({
  className = "flex flex-wrap gap-x-16 gap-y-6 mx-8 mb-4 text-[12px] text-muted-foreground",
  barWidth = "w-8",
  barHeight = "h-1.5",
}) => {
  return (
    <div className={className}>
      {legendItems.map(item => (
        <div key={item.status} className="flex items-center gap-3">
          <div className={`${barWidth} ${barHeight} rounded-full ${statusColorMap[item.status as keyof typeof statusColorMap]}`} />
          <span >{item.label}</span>
        </div>
      ))}
    </div>
  );
};

export default ProgressLegend ;