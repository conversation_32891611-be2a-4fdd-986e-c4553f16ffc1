"use client";

import { Calendar } from "lucide-react";
import React, { useState } from "react";

interface Progress {
  status: "pending" | "ongoing" | "submitted" | "overdue" | "graded";
  checkpoint?: string;
  startDate?: string;
  endDate?: string;
}

interface SegmentedProgressProps {
  progress: Progress[];
  totalCheckpoints: number;
  className?: string;
}

const statusColorMap = {
  submitted: "bg-bits-light-green",
  overdue: "bg-bits-error",
  ongoing: "bg-bits-orange",
  pending: "bg-gray-300",
  graded: "bg-bits-blue",
};
const statusTextColorMap = {
  submitted: "text-bits-light-green",
  overdue: "text-bits-red",
  ongoing: "text-bits-orange",
  pending: "text-gray-900",
  graded: "text-black-400",
};
const statusBgColorMap = {
  submitted: "bg-green-100",
  overdue: "bg-red-100",
  ongoing: "bg-orange-100",
  pending: "bg-gray-300",
  graded: "bg-blue-100",
};

function formatDateRange(start: string, end: string): string {
  const startDate = new Date(start);
  const endDate = new Date(end);

  const startFormatted = new Intl.DateTimeFormat("en-GB", {
    day: "2-digit",
    month: "short",
  }).format(startDate);

  const endFormatted = new Intl.DateTimeFormat("en-GB", {
    day: "2-digit",
    month: "short",
    year: "numeric",
  }).format(endDate);

  return `${startFormatted} - ${endFormatted}`;
}


const SegmentedProgress: React.FC<SegmentedProgressProps> = ({
  progress,
  totalCheckpoints,
  className = "",
}) => {
  if (!progress || totalCheckpoints <= 0) {
    return null;
  }

  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const pendingCount = Math.max(0, totalCheckpoints - progress.length);
  const pendingCheckpoints: Progress[] = Array(pendingCount).fill({
    status: "pending",
  });
  const fullProgress = [...progress, ...pendingCheckpoints];

  return (
    <div className={`relative flex w-full h-2 gap-1 ${className}`}>
      {fullProgress.map((checkpoint, index) => (
        <div
          key={index}
          className={`flex-1 h-full rounded-full relative group ${
            statusColorMap[checkpoint.status] || "bg-gray-200"
          }`}
          onMouseEnter={() => setHoveredIndex(index)}
          onMouseLeave={() => setHoveredIndex(null)}
        >
          {hoveredIndex === index && (
            <div
              className={`absolute z-10 bottom-full mb-2 w-[max-content] max-w-[200px] px-2
              ${
                index < 2
                  ? "left-0 translate-x-0"
                  : index > fullProgress.length - 3
                  ? "right-0 translate-x-0"
                  : "left-1/2 -translate-x-1/2"
              }`}
            >
              {/* The tooltip now uses a single element for the card and the triangle. */}
              <div className="relative p-4 shadow-md bg-white rounded-lg w-auto max-w-[200px] ">
                {index < 2 ? (
                  // Left-aligned tooltip: arrow on left
                  <div className="absolute left-4 -bottom-2 w-0 h-0 border-x-8 border-x-transparent border-t-8 border-t-white" />
                ) : index > fullProgress.length - 3 ? (
                  // Right-aligned tooltip: arrow on right
                  <div className="absolute right-4 -bottom-2 w-0 h-0 border-x-8 border-x-transparent border-t-8 border-t-white" />
                ) : (
                  // Centered tooltip: arrow in center
                  <div className="absolute left-1/2 -translate-x-1/2 -bottom-2 w-0 h-0 border-x-8 border-x-transparent border-t-8 border-t-white" />
                )}
                <div className="flex flex-col flex-1 gap-1">
                  <div className="flex items-center p-0">
                    <div className="flex text-sm font-semibold flex-wrap">
                      {fullProgress[hoveredIndex]?.checkpoint || `Checkpoint ${hoveredIndex + 1}`}
                    </div>
                    <div
                      className={`flex p-1 ml-4 rounded-md text-xs ${
                        statusBgColorMap[checkpoint.status] || "bg-gray-200"
                      } ${
                        statusTextColorMap[checkpoint.status] || "text-black-800"
                      }`}
                    >
                      {fullProgress[hoveredIndex]?.status}
                    </div>
                </div>
                <div className="mt-4 p-0">
                  <div className="flex items-end text-xs">
                    <Calendar className="h-4 w-4 mr-2"/>
                    {fullProgress[hoveredIndex]?.startDate &&
                    fullProgress[hoveredIndex]?.endDate
                      ? formatDateRange(
                          fullProgress[hoveredIndex].startDate,
                          fullProgress[hoveredIndex].endDate
                        )
                      : "Dates not available"}
                  </div>
                </div>
                </div>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default SegmentedProgress;
