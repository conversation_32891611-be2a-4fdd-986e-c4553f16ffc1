import React, { createContext, useContext } from 'react';

interface CustomTabsProps {
  value: string;
  onValueChange: (value: string) => void;
  children: React.ReactNode;
  className?: string;
}

interface CustomTabsListProps {
  children: React.ReactNode;
  className?: string;
}

interface CustomTabsTriggerProps {
  value: string;
  children: React.ReactNode;
  className?: string;
}

interface CustomTabsContentProps {
  value: string;
  children: React.ReactNode;
  className?: string;
}

interface CustomTabsContextType {
  activeTab: string;
  onTabChange: (value: string) => void;
}

const CustomTabsContext = createContext<CustomTabsContextType | null>(null);

const useCustomTabs = () => {
  const context = useContext(CustomTabsContext);
  if (!context) {
    throw new Error('CustomTabs components must be used within CustomTabs');
  }
  return context;
};

export function CustomTabs({ value, onValueChange, children, className = "" }: CustomTabsProps) {
  return (
    <CustomTabsContext.Provider value={{ activeTab: value, onTabChange: onValueChange }}>
      <div className={`${className}`}>
        {children}
      </div>
    </CustomTabsContext.Provider>
  );
}

export function CustomTabsList({ children, className = "" }: CustomTabsListProps) {
  return (
    <div className={`flex items-center bg-gray-200 rounded-lg p-2 mb-8 w-fit ${className}`}>
      {children}
    </div>
  );
}

export function CustomTabsTrigger({ value, children, className = "" }: CustomTabsTriggerProps) {
  const { activeTab, onTabChange } = useCustomTabs();
  
  return (
    <button
      onClick={() => onTabChange(value)}
      className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
        activeTab === value
          ? 'bg-white text-gray-900 shadow-sm'
          : 'text-gray-600 hover:text-gray-900'
      } ${className}`}
    >
      {children}
    </button>
  );
}

export function CustomTabsContent({ value, children, className = "" }: CustomTabsContentProps) {
  const { activeTab } = useCustomTabs();
  
  if (activeTab !== value) return null;
  
  return (
    <div className={className}>
      {children}
    </div>
  );
}