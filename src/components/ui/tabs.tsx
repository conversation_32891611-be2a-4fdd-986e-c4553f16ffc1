"use client";

import * as React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";

import { cn } from "../../lib/utils";

function Tabs({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Root>) {
  return (
    <TabsPrimitive.Root
      data-slot="tabs"
      className={cn("flex flex-col gap-2", className)}
      {...props}
    />
  );
}

function TabsList({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.List>) {
  return (
    // <TabsPrimitive.List
    //   data-slot="tabs-list"
    //   className={cn(
    //     "bg-white text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-xl p-[3px] border border-gray-200 flex",
    //     className,
    //   )}
    //   {...props}
    // />
    <TabsPrimitive.List
      className="flex bg-bits-grey-200 rounded-md p-1 space-x-2"
      {...props}
    />
  );
}

function TabsTrigger({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {
  return (
    <TabsPrimitive.Trigger
      data-slot="tabs-trigger"
      // className={cn(
      //   "data-[state=active]:bg-card data-[state=active]:text-bits-blue dark:data-[state=active]:text-bits-gold focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-xl border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
      //   className,
      // )}
      className={cn(
        "data-[state=active]:bg-white data-[state=active]:text-gray-900",
        "bg-transparent text-gray-700 hover:bg-gray-100",
        "rounded-md px-4 py-2 text-sm font-medium",
        "transition-colors duration-150",
        "focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        "disabled:opacity-50 disabled:pointer-events-none",
        className
      )}
      // className={cn(
      //   "data-[state=active]:bg-gray-100 data-[state=active]:text-gray-900",
      //   "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring",
      //   "text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5",
      //   "rounded-xl border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap",
      //   "transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50",
      //   "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
      //   className
      // )}
      {...props}
    />
  );
}

// function TabsContent({
//   className,
//   ...props
// }: React.ComponentProps<typeof TabsPrimitive.Content>) {
//   return (
//     <TabsPrimitive.Content
//       data-slot="tabs-content"
//       className={cn("flex-1 outline-none bg-white border border-gray-200 rounded-b-xl", className)}
//       {...props}
//     />
//   );
// }

function TabsContent({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Content>) {
  return (
    <TabsPrimitive.Content
      data-slot="tabs-content"
      className={cn("flex-1 outline-none", className)}
      {...props}
    />
  );
}

export { Tabs, TabsList, TabsTrigger, TabsContent };
