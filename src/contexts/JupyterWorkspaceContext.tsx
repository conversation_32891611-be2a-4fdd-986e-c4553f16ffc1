import React, { createContext, useContext, useState, useCallback } from "react";

interface JupyterWorkspaceState {
  projectId: string | null;
  kernelId: string | null;
  sessionId: string | null;
  workspaceUrl: string | null;
  status: "none" | "provisioning" | "active" | "error" | "stopping";
  instanceId: string | null;
  createdAt: string | null;
  lastActivity: string | null;
  folderPath: string | null;
  notebookPath: string | null;
  serverReady: boolean;
  kernelExecutionState: string | null;
  errorMessage?: string;
}

interface JupyterWorkspaceContextType {
  workspace: JupyterWorkspaceState;
  setWorkspace: (workspace: Partial<JupyterWorkspaceState>) => void;
  updateWorkspace: (updates: Partial<JupyterWorkspaceState>) => void;
  resetWorkspace: () => void;
  isWorkspaceReady: () => boolean;
  updateActivity: () => void;
}

const initialWorkspaceState: JupyterWorkspaceState = {
  projectId: null,
  kernelId: null,
  sessionId: null,
  workspaceUrl: null,
  status: "none",
  instanceId: null,
  createdAt: null,
  lastActivity: null,
  folderPath: null,
  notebookPath: null,
  serverReady: false,
  kernelExecutionState: null,
};

const JupyterWorkspaceContext =
  createContext<JupyterWorkspaceContextType | null>(null);

export const useJupyterWorkspace = () => {
  const context = useContext(JupyterWorkspaceContext);
  if (!context) {
    throw new Error(
      "useJupyterWorkspace must be used within a JupyterWorkspaceProvider"
    );
  }
  return context;
};

export function JupyterWorkspaceProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [workspace, setWorkspaceState] = useState<JupyterWorkspaceState>(
    initialWorkspaceState
  );

  const setWorkspace = useCallback(
    (newWorkspace: Partial<JupyterWorkspaceState>) => {
      setWorkspaceState((prev) => ({ ...prev, ...newWorkspace }));
    },
    []
  );

  const updateWorkspace = useCallback(
    (updates: Partial<JupyterWorkspaceState>) => {
      setWorkspaceState((prev) => ({ ...prev, ...updates }));
    },
    []
  );

  const resetWorkspace = useCallback(() => {
    setWorkspaceState(initialWorkspaceState);
  }, []);

  const isWorkspaceReady = useCallback(() => {
    return workspace.status === "active" && workspace.kernelId !== null;
  }, [workspace.status, workspace.kernelId]);

  const updateActivity = useCallback(() => {
    setWorkspaceState((prev) => ({
      ...prev,
      lastActivity: new Date().toISOString(),
    }));
  }, []);

  const contextValue: JupyterWorkspaceContextType = {
    workspace,
    setWorkspace,
    updateWorkspace,
    resetWorkspace,
    isWorkspaceReady,
    updateActivity,
  };

  return (
    <JupyterWorkspaceContext.Provider value={contextValue}>
      {children}
    </JupyterWorkspaceContext.Provider>
  );
}
