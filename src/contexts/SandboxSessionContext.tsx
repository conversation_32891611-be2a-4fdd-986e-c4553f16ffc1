import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';

interface SandboxSession {
  id: string;
  studentId: string;
  studentName: string;
  assignmentId: string;
  assignmentTitle: string;
  notebookUrl?: string;
  submissionId: string;
  startTime: Date;
  lastActivity: Date;
  status: 'starting' | 'active' | 'shutting-down' | 'shutdown';
}

interface SandboxSessionContextType {
  activeSessions: SandboxSession[];
  currentSession: SandboxSession | null;
  isLoading: boolean;
  startSession: (session: Omit<SandboxSession, 'id' | 'startTime' | 'lastActivity' | 'status'>) => Promise<SandboxSession>;
  shutdownSession: (sessionId: string, graceful?: boolean) => Promise<void>;
  shutdownAllSessions: () => Promise<void>;
  switchToSession: (newSession: Omit<SandboxSession, 'id' | 'startTime' | 'lastActivity' | 'status'>) => Promise<SandboxSession>;
  updateActivity: (sessionId: string) => void;
  getSessionStatus: (studentId: string, assignmentId: string) => SandboxSession | null;
}

const SandboxSessionContext = createContext<SandboxSessionContextType | null>(null);

export const useSandboxSession = () => {
  const context = useContext(SandboxSessionContext);
  if (!context) {
    throw new Error('useSandboxSession must be used within a SandboxSessionProvider');
  }
  return context;
};

export function SandboxSessionProvider({ children }: { children: React.ReactNode }) {
  const [activeSessions, setActiveSessions] = useState<SandboxSession[]>([]);
  const [currentSession, setCurrentSession] = useState<SandboxSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Auto-cleanup idle sessions (after 30 minutes of inactivity)
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      const idleThreshold = 30 * 60 * 1000; // 30 minutes

      setActiveSessions(prev => {
        const activeSessionsToShutdown = prev.filter(session => {
          const isIdle = now.getTime() - session.lastActivity.getTime() > idleThreshold;
          return isIdle && session.status === 'active';
        });

        // Shutdown idle sessions
        activeSessionsToShutdown.forEach(session => {
          shutdownSession(session.id, true);
        });

        return prev;
      });
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, []);

  const generateSessionId = () => {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const startSession = useCallback(async (sessionData: Omit<SandboxSession, 'id' | 'startTime' | 'lastActivity' | 'status'>): Promise<SandboxSession> => {
    setIsLoading(true);
    
    try {
      // Check if session already exists for this student/assignment
      const existingSession = activeSessions.find(s => 
        s.studentId === sessionData.studentId && 
        s.assignmentId === sessionData.assignmentId &&
        s.status !== 'shutdown'
      );

      if (existingSession && existingSession.status === 'active') {
        setCurrentSession(existingSession);
        updateActivity(existingSession.id);
        toast.success(`Resumed existing sandbox for ${sessionData.studentName}`);
        return existingSession;
      }

      const newSession: SandboxSession = {
        ...sessionData,
        id: generateSessionId(),
        startTime: new Date(),
        lastActivity: new Date(),
        status: 'starting'
      };

      setActiveSessions(prev => [...prev, newSession]);
      setCurrentSession(newSession);

      toast.loading(`Starting sandbox for ${sessionData.studentName}...`, {
        id: `start-${newSession.id}`
      });

      // Simulate sandbox startup
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Update session status to active
      setActiveSessions(prev => prev.map(s => 
        s.id === newSession.id 
          ? { ...s, status: 'active' as const, notebookUrl: `/sandbox/${s.studentId}/${s.assignmentId}` }
          : s
      ));

      const activeSession = { ...newSession, status: 'active' as const, notebookUrl: `/sandbox/${newSession.studentId}/${newSession.assignmentId}` };
      setCurrentSession(activeSession);

      toast.success(`Sandbox ready for ${sessionData.studentName}`, {
        id: `start-${newSession.id}`
      });

      return activeSession;
    } catch (error) {
      toast.error(`Failed to start sandbox for ${sessionData.studentName}`, {
        id: `start-${sessionData.studentId}`
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [activeSessions]);

  const shutdownSession = useCallback(async (sessionId: string, graceful = true): Promise<void> => {
    const session = activeSessions.find(s => s.id === sessionId);
    if (!session) return;

    // Update session status to shutting down
    setActiveSessions(prev => prev.map(s => 
      s.id === sessionId 
        ? { ...s, status: 'shutting-down' as const }
        : s
    ));

    if (graceful) {
      toast.loading(`Shutting down sandbox for ${session.studentName}...`, {
        id: `shutdown-${sessionId}`
      });
    }

    try {
      // Simulate shutdown process
      await new Promise(resolve => setTimeout(resolve, graceful ? 1500 : 500));

      // Remove from active sessions
      setActiveSessions(prev => prev.filter(s => s.id !== sessionId));
      
      if (currentSession?.id === sessionId) {
        setCurrentSession(null);
      }

      if (graceful) {
        toast.success(`Sandbox for ${session.studentName} has been shut down`, {
          id: `shutdown-${sessionId}`
        });
      }
    } catch (error) {
      toast.error(`Failed to shutdown sandbox for ${session.studentName}`, {
        id: `shutdown-${sessionId}`
      });
      
      // Revert status on error
      setActiveSessions(prev => prev.map(s => 
        s.id === sessionId 
          ? { ...s, status: 'active' as const }
          : s
      ));
    }
  }, [activeSessions, currentSession]);

  const shutdownAllSessions = useCallback(async (): Promise<void> => {
    const activeSessionIds = activeSessions
      .filter(s => s.status === 'active')
      .map(s => s.id);

    if (activeSessionIds.length === 0) return;

    toast.loading(`Shutting down ${activeSessionIds.length} active session(s)...`, {
      id: 'shutdown-all'
    });

    try {
      await Promise.all(activeSessionIds.map(id => shutdownSession(id, false)));
      toast.success('All sessions have been shut down', {
        id: 'shutdown-all'
      });
    } catch (error) {
      toast.error('Some sessions failed to shut down', {
        id: 'shutdown-all'
      });
    }
  }, [activeSessions, shutdownSession]);

  const switchToSession = useCallback(async (newSessionData: Omit<SandboxSession, 'id' | 'startTime' | 'lastActivity' | 'status'>): Promise<SandboxSession> => {
    // Shutdown current session if it exists and is different
    if (currentSession && 
        (currentSession.studentId !== newSessionData.studentId || 
         currentSession.assignmentId !== newSessionData.assignmentId)) {
      
      toast.info(`Switching from ${currentSession.studentName} to ${newSessionData.studentName}`);
      await shutdownSession(currentSession.id, true);
    }

    // Start new session
    return await startSession(newSessionData);
  }, [currentSession, shutdownSession, startSession]);

  const updateActivity = useCallback((sessionId: string) => {
    setActiveSessions(prev => prev.map(s => 
      s.id === sessionId 
        ? { ...s, lastActivity: new Date() }
        : s
    ));
  }, []);

  const getSessionStatus = useCallback((studentId: string, assignmentId: string): SandboxSession | null => {
    return activeSessions.find(s => 
      s.studentId === studentId && 
      s.assignmentId === assignmentId &&
      s.status !== 'shutdown'
    ) || null;
  }, [activeSessions]);

  const contextValue: SandboxSessionContextType = {
    activeSessions,
    currentSession,
    isLoading,
    startSession,
    shutdownSession,
    shutdownAllSessions,
    switchToSession,
    updateActivity,
    getSessionStatus
  };

  return (
    <SandboxSessionContext.Provider value={contextValue}>
      {children}
    </SandboxSessionContext.Provider>
  );
}