import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';

interface Submission {
  id: string;
  studentId: string;
  studentName: string;
  assignmentId: string;
  assignmentTitle: string;
  submissionDate: string;
  status: 'submitted' | 'late' | 'draft';
  grade?: number;
  feedback?: string;
  summary?: string;
  files?: Array<{ name: string; type: string; size: number }>;
  metrics?: {
    accuracy?: string;
    executionTime?: string;
  };
}

interface SubmissionNavigationContextType {
  submissions: Submission[];
  currentSubmission: Submission | null;
  currentIndex: number;
  assignmentTitle: string;
  isLoading: boolean;
  hasNext: boolean;
  hasPrevious: boolean;
  goToNext: () => void;
  goToPrevious: () => void;
  goToSubmission: (index: number) => void;
  setSubmissions: (submissions: Submission[], assignmentTitle: string) => void;
  refreshSubmissions: () => Promise<void>;
}

const SubmissionNavigationContext = createContext<SubmissionNavigationContextType | null>(null);

export const useSubmissionNavigation = () => {
  const context = useContext(SubmissionNavigationContext);
  if (!context) {
    throw new Error('useSubmissionNavigation must be used within a SubmissionNavigationProvider');
  }
  return context;
};

export function SubmissionNavigationProvider({ children }: { children: React.ReactNode }) {
  const [submissions, setSubmissionsState] = useState<Submission[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [assignmentTitle, setAssignmentTitle] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const currentSubmission = submissions.length > 0 ? submissions[currentIndex] : null;
  const hasNext = currentIndex < submissions.length - 1;
  const hasPrevious = currentIndex > 0;

  const goToNext = useCallback(() => {
    if (hasNext) {
      setCurrentIndex(prev => prev + 1);
    }
  }, [hasNext]);

  const goToPrevious = useCallback(() => {
    if (hasPrevious) {
      setCurrentIndex(prev => prev - 1);
    }
  }, [hasPrevious]);

  const goToSubmission = useCallback((index: number) => {
    if (index >= 0 && index < submissions.length) {
      setCurrentIndex(index);
    }
  }, [submissions.length]);

  const setSubmissions = useCallback((newSubmissions: Submission[], newAssignmentTitle: string) => {
    setSubmissionsState(newSubmissions);
    setAssignmentTitle(newAssignmentTitle);
    setCurrentIndex(0); // Reset to first submission
  }, []);

  const refreshSubmissions = useCallback(async () => {
    setIsLoading(true);
    try {
      // In a real app, this would fetch fresh data from the API
      // For now, we'll simulate a refresh
      await new Promise(resolve => setTimeout(resolve, 1000));
      // The actual refresh logic would update the submissions array
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'ArrowLeft':
            event.preventDefault();
            goToPrevious();
            break;
          case 'ArrowRight':
            event.preventDefault();
            goToNext();
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [goToNext, goToPrevious]);

  const contextValue: SubmissionNavigationContextType = {
    submissions,
    currentSubmission,
    currentIndex,
    assignmentTitle,
    isLoading,
    hasNext,
    hasPrevious,
    goToNext,
    goToPrevious,
    goToSubmission,
    setSubmissions,
    refreshSubmissions
  };

  return (
    <SubmissionNavigationContext.Provider value={contextValue}>
      {children}
    </SubmissionNavigationContext.Provider>
  );
}