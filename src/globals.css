@tailwind base;
@tailwind components;
@tailwind utilities;

@custom-variant dark (&:is(.dark *));

:root {
  --font-size: 14px;
  --background: #fafafa;
  --foreground: oklch(0.145 0 0);
  --card: #ffffff;
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: #2b2b88;
  --primary-foreground: #ffffff;
  --secondary: oklch(0.95 0.0058 264.53);
  --secondary-foreground: #030213;
  --muted: #ececf0;
  --muted-foreground: #475467;
  --accent: #b78a2d;
  --accent-foreground: #ffffff;
  --destructive: #cf2027;
  --destructive-foreground: #ffffff;
  --border: #d0d5dd;
  --input: var(--background);
  --input-background: var(--background);
  --popover: var(--background);
  --switch-background: #cbced4;
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: #2b2b88;
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);

  /* BITS Pilani Brand Colors */
  --bits-blue: #100892;
  --bits-orange: #fdb022;
  --bits-green: #00a63e;
  --bits-error: #f04438;
  --bits-light-green: #32d583;
  --bits-gold: #b78a2d;
  --bits-warning: #f79009;
  --bits-red: #ed1c24;
  --bits-red-100: #fde1e2;
  --bits-red-600: #d30e15;
  --bits-light-blue: #5ccae8;
  --bits-light-yellow: #ffe275;
  --bits-blue-500: #2e90fa;
  --bits-blue-neutral-900: #172b4d;
  --bits-blue-50: #eff8ff;
  --bits-blue-800: #1a165a;
  --bits-blue-25: #f5fbff;
  --bits-blue-100: #e4e3fe;
  --bits-blue-italic: #5046ef;
  --bits-green-success-25: #f6fef9;
  --bits-green-success-500: #12b76a;
  --bits-grey: #0a0a0a;
  --bits-grey-900: #101828;
  --bits-grey-700: #344054;
  --bits-grey-600: #717182;
  --bits-grey-300: #d0d5dd;
  --bits-grey-500: #667085;
  --bits-grey-200: #e4e7ec;
  --bits-grey-100: #f2f4f7;
  --bits-orange: #fdb022;
  --bits-indigo: #9e77ed;
  --bits-indigo-600: #7f56d9;
  --bits-green-shade: #00a63e;
  --bits-completed: #ecfdf3;
  --bits-overdue: #fef3f2;
  --bits-strong-red: #b42318;
  --bits-warning-50: #fffaeb;

  /* Make brand color variables available globally for utility classes */
  --color-bits-grey: var(--bits-grey);
  --color-bits-blue: var(--bits-blue);
  --color-bits-orange: var(--bits-orange);
  --color-bits-indigo: var(--bits-indigo);
  --color-bits-indigo-600: var(--bits-indigo-600);
  --color-bits-green: var(--bits-green);
  --color-bits-warning: var(--bits-warning);
  --color-bits-light-green: var(--bits-light-green);
  --color-bits-error: var(--bits-error);
  --color-bits-gold: var(--bits-gold);
  --color-bits-red: var(--bits-red);
  --color-bits-red-100: var(--bits-red-100);
  --color-bits-red-600: var(--bits-red-600);
  --color-bits-light-blue: var(--bits-light-blue);
  --color-bits-light-yellow: var(--bits-light-yellow);
  --color-bits-blue-500: var(--bits-blue-500);
  --color-bits-blue-neutral-900: var(--bits-blue-neutral-900);
  --color-bits-blue-50: var(--bits-blue-50);
  --color-bits-blue-25: var(--bits-blue-25);
  --color-bits-blue-100: var(--bits-blue-100);
  --color-bits-blue-italic: var(--bits-blue-italic);
  --color-bits-blue-800: var(--bits-blue-800);
  --color-bits-grey: var(--bits-grey);
  --color-bits-grey-900: var(--bits-grey-900);
  --color-bits-grey-700: var(--bits-grey-700);
  --color-bits-grey-300: var(--bits-grey-300);
  --color-bits-grey-500: var(--bits-grey-500);
  --color-bits-grey-200: var(--bits-grey-200);
  --color-bits-grey-100: var(--bits-grey-100);
  --color-bits-grey-600: var(--bits-grey-600);
  --color-bits-green-success-25: var(--bits-green-success-25);
  --color-bits-green-success-500: var(--bits-green-success-500);
  --color-bits-green-shade: var(--bits-green-shade);
  --color-bits-completed: var(--bits-completed);
  --color-bits-overdue: var(--bits-overdue);
  --color-bits-strong-red: var(--bits-strong-red);
  --color-bits-warning-50: var(--bits-warning-50);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: #2b2b88;
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: #b78a2d;
  --accent-foreground: oklch(0.985 0 0);
  --destructive: #cf2027;
  --destructive-foreground: oklch(0.985 0 0);
  --border: rgba(208, 213, 221, 1);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: #2b2b88;
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
  /* Make brand color variables available globally for utility classes in dark mode */
  --color-bits-blue: var(--bits-blue);
  --color-bits-gold: var(--bits-gold);
  --color-bits-red: var(--bits-red);
  --color-bits-indigo: var(--bits-indigo);
  --color-bits-indigo-600: var(--bits-indigo-600);
  --color-bits-red-100: var(--bits-red-100);
  --color-bits-red-600: var(--bits-red-600);
  --color-bits-light-blue: var(--bits-light-blue);
  --color-bits-light-yellow: var(--bits-light-yellow);
  --color-bits-blue-500: var(--bits-blue-500);
  --color-bits-blue-neutral-900: var(--bits-blue-neutral-900);
  --color-bits-blue-50: var(--bits-blue-50);
  --color-bits-blue-25: var(--bits-blue-25);
  --color-bits-blue-100: var(--bits-blue-100);
  --color-bits-blue-italic: var(--bits-blue-italic);
  --color-bits-blue-800: var(--bits-blue-800);
  --color-bits-grey: var(--bits-grey);
  --color-bits-grey-900: var(--bits-grey-900);
  --color-bits-grey-700: var(--bits-grey-700);
  --color-bits-grey-300: var(--bits-grey-300);
  --color-bits-grey-500: var(--bits-grey-500);
  --color-bits-grey-200: var(--bits-grey-200);
  --color-bits-grey-100: var(--bits-grey-100);
  --color-bits-grey-600: var(--bits-grey-600);
  --color-bits-green-success-25: var(--bits-green-success-25);
  --color-bits-green-success-500: var(--bits-green-success-500);
  --color-bits-warning-50: var(--bits-warning-50);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-switch-background: var(--switch-background);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* BITS Brand Colors */
  --color-bits-blue: var(--bits-blue);
  --color-bits-gold: var(--bits-gold);
  --color-bits-red: var(--bits-red);
  --color-bits-indigo: var(--bits-indigo);
  --color-bits-indigo-600: var(--bits-indigo-600);
  --color-bits-red-100: var(--bits-red-100);
  --color-bits-red-600: var(--bits-red-600);
  --color-bits-light-blue: var(--bits-light-blue);
  --color-bits-light-yellow: var(--bits-light-yellow);
  --color-bits-blue-500: var(--bits-blue-500);
  --color-bits-blue-neutral-900: var(--bits-blue-neutral-900);
  --color-bits-blue-50: var(--bits-blue-50);
  --color-bits-blue-25: var(--bits-blue-25);
  --color-bits-blue-100: var(--bits-blue-100);
  --color-bits-blue-italic: var(--bits-blue-italic);
  --color-bits-blue-800: var(--bits-blue-800);
  --color-bits-grey-900: var(--bits-grey-900);
  --color-bits-grey-700: var(--bits-grey-700);
  --color-bits-grey-300: var(--bits-grey-300);
  --color-bits-grey-500: var(--bits-grey-500);
  --color-bits-grey-200: var(--bits-grey-200);
  --color-bits-grey-100: var(--bits-grey-100);
  --color-bits-grey-600: var(--bits-grey-600);
  --color-bits-green-success-25: var(--bits-green-success-25);
  --color-bits-green-success-500: var(--bits-green-success-500);
  --color-bits-completed: var(--bits-completed);
  --color-bits-overdue: var(--bits-overdue);
  --color-bits-strong-red: var(--bits-strong-red);
}

@layer base {
  * {
    @apply outline-ring/50;
    border-color: transparent;
  }

  /* Only apply borders to components that should have them */
  .border,
  .border-t,
  .border-r,
  .border-b,
  .border-l,
  .border-x,
  .border-y,
  .ring,
  .ring-offset-1,
  .ring-offset-2,
  .ring-offset-4,
  .ring-offset-8,
  .ring-1,
  .ring-2,
  .ring-4,
  .ring-8,
  .ring-offset-background,
  .ring-ring,
  .ring-offset-foreground,
  .ring-offset-transparent,
  .ring-offset-current,
  .ring-offset-0,
  .ring-offset-1,
  .ring-offset-2,
  .ring-offset-4,
  .ring-offset-8,
  .ring-offset-0,
  .ring-offset-1,
  .ring-offset-2,
  .ring-offset-4,
  .ring-offset-8 {
    @apply border-border;
  }

  /* Apply border to form elements */
  input,
  select,
  textarea,
  button,
  [role="button"],
  [role="combobox"],
  [role="listbox"],
  [role="menu"],
  [role="menuitem"],
  [role="menuitemcheckbox"],
  [role="menuitemradio"],
  [role="option"],
  [role="slider"],
  [role="spinbutton"],
  [role="switch"],
  [role="textbox"] {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Fix dropdown background */
  .select-dropdown,
  .select-dropdown *,
  .select-dropdown *:before,
  .select-dropdown *:after {
    background-color: var(--background);
  }

  /* Fix dropdown text color */
  .select-dropdown,
  .select-dropdown * {
    color: var(--foreground);
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

/**
 * Base typography. This is not applied to elements which have an ancestor with a Tailwind text class.
 */
@layer base {
  :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) {
    h1 {
      font-size: var(--text-2xl);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h2 {
      font-size: var(--text-xl);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h3 {
      font-size: var(--text-lg);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h4 {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    p {
      font-size: var(--text-base);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
    }

    label {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    button {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    input {
      font-size: var(--text-base);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
    }
  }
}

html {
  font-size: var(--font-size);
}

/* Custom BITS Pilani utility classes */
@layer utilities {
  .bg-bits-blue {
    background-color: var(--color-bits-blue);
  }

  .bg-bits-gold {
    background-color: var(--color-bits-gold);
  }

  .bg-bits-red {
    background-color: var(--color-bits-red);
  }

  .bg-bits-red-100 {
    background-color: var(--color-bits-red-100);
  }

  .bg-bits-error {
    background-color: var(--color-bits-error);
  }

  .bg-bits-green {
    background-color: var(--color-bits-green);
  }

  .bg-bits-orange {
    background-color: var(--color-bits-orange);
  }

  .bg-bits-light-green {
    background-color: var(--color-bits-light-green);
  }

  .bg-bits-blue-50 {
    background-color: var(--color-bits-blue-50);
  }

  .bg-bits-blue-25 {
    background-color: var(--color-bits-blue-25);
  }

  .bg-bits-blue-100 {
    background-color: var(--color-bits-blue-100);
  }

  .bg-bits-grey-200 {
    background-color: var(--color-bits-grey-200);
  }

  .bg-bits-grey-100 {
    background-color: var(--color-bits-grey-100);
  }

  .bg-bits-grey-500 {
    background-color: var(--color-bits-grey-500);
  }

  .bg-bits-grey-300 {
    background-color: var(--color-bits-grey-300);
  }

  .bg-bits-green-success-25 {
    background-color: var(--color-bits-green-success-25);
  }

  .bg-bits-completed {
    background-color: var(--color-bits-completed);
  }

  .bg-bits-warning-50 {
    background-color: var(--color-bits-warning-50);
  }

  .bg-bits-overdue {
    background-color: var(--color-bits-overdue);
  }

  .text-bits-green {
    color: var(--color-bits-green);
  }

  .text-bits-green-success-500 {
    color: var(--color-bits-green-success-500);
  }

  .text-bits-light-green {
    color: var(--color-bits-light-green);
  }

  .text-bits-blue {
    color: var(--color-bits-blue);
  }

  .text-bits-blue-italic {
    color: var(--color-bits-blue-italic);
  }

  .text-bits-orange {
    color: var(--color-bits-orange);
  }

  .text-bits-indigo {
    color: var(--color-bits-indigo);
  }

  .text-bits-indigo-600 {
    color: var(--color-bits-indigo-600);
  }

  .text-bits-gold {
    color: var(--color-bits-gold);
  }

  .text-bits-warning {
    color: var(--color-bits-warning);
  }

  .text-bits-red {
    color: var(--color-bits-red);
  }

  .text-bits-error {
    color: var(--color-bits-error);
  }

  .text-bits-red-600 {
    color: var(--color-bits-red-600);
  }

  .border-bits-blue {
    border-color: var(--color-bits-blue);
  }

  .border-grey-300 {
    border-color: var(--color-bits-grey-300);
  }

  .border-bits-gold {
    border-color: var(--color-bits-gold);
  }

  .text-bits-grey-900 {
    color: var(--color-bits-grey-900);
  }

  .text-bits-grey-700 {
    color: var(--color-bits-grey-700);
  }

  .text-bits-grey-600 {
    color: var(--color-bits-grey-600);
  }

  .text-bits-grey-300 {
    color: var(--color-bits-grey-300);
  }

  .text-bits-grey-500 {
    color: var(--color-bits-grey-500);
  }

  .text-bits-blue-500 {
    color: var(--color-bits-blue-500);
  }

  .text-bits-blue-800 {
    color: var(--color-bits-blue-800);
  }

  .text-bits-blue-neutral-900 {
    color: var(--color-bits-blue-neutral-900);
  }

  .text-bits-green-shade {
    color: var(--color-bits-green-shade);
  }

  .text-bits-strong-red {
    color: var(--color-bits-strong-red);
  }

  /* Text truncation utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Maximum width utilities for badges and small components */
  .max-w-20 {
    max-width: 5rem;
  }

  .max-w-24 {
    max-width: 6rem;
  }

  .max-w-28 {
    max-width: 7rem;
  }

  .max-w-32 {
    max-width: 8rem;
  }

  /* Width utilities for fixed sizing */
  .w-8 {
    width: 2rem;
  }

  .w-12 {
    width: 3rem;
  }

  .w-16 {
    width: 4rem;
  }

  .w-20 {
    width: 5rem;
  }

  .w-24 {
    width: 6rem;
  }

  /* Fixed height utilities for consistent card layouts */
  .h-72 {
    height: 18rem;
  }

  /* Max width for content areas */
  .max-w-\[calc\(100\%-5rem\)\] {
    max-width: calc(100% - 5rem);
  }
}

/* Base: hide scrollbar where possible */
.custom-scroll {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE 10+ */
}

.custom-scroll::-webkit-scrollbar {
  width: 0;
  /* WebKit (Chrome/Safari/Edge) */
  height: 0;
}

/* On hover: reveal a slim scrollbar */
.custom-scroll:hover {
  scrollbar-width: thin;
  /* Firefox shows slim bar */
}

.custom-scroll:hover::-webkit-scrollbar {
  width: 6px;
  /* Slim width */
}

.custom-scroll:hover::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scroll:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.25);
  /* Subtle thumb */
  border-radius: 9999px;
}

.custom-scroll:hover::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.35);
}

::selection {
  background-color: #338fff;
  color: #ffffff;
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.transparent-overlay::before,
.transparent-overlay + div,
[data-overlay] {
  background: transparent !important;
  backdrop-filter: none !important;
}

/* For Radix UI Dialog */
[data-radix-dialog-overlay] {
  background: transparent !important;
}

/* For shadcn/ui Dialog */
.fixed.inset-0.z-50.bg-background {
  background: transparent !important;
}