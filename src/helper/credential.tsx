import { User<PERSON><PERSON>,User } from "../components/Context/AuthContext";
export const mockUsers: { [key: string]: User } = {
  "<EMAIL>": {
    id: "1",
    name: "sharma",
    email: "<EMAIL>",
    role: "student",
  },
  "<EMAIL>": {
    id: "4",
    name: "rohan",
    email: "<EMAIL>",
    role: "student",
  },
  "<EMAIL>": {
    id: "2",
    name: "<PERSON><PERSON> <PERSON><PERSON>",
    email: "<EMAIL>",
    role: "instructor",
  },
  "<EMAIL>": {
    id: "3",
    name: "Admin User",
    email: "<EMAIL>",
    role: "admin",
  },
};

// Demo credentials for easy login
export const demoCredentials = [
  {
    title: "Admin User",
    email: "<EMAIL>",
    password: "password",
    role: "admin" as User<PERSON><PERSON>,
  },
  {
    title: "Inst<PERSON>ctor",
    email: "<EMAIL>",
    password: "password",
    role: "instructor" as <PERSON><PERSON><PERSON><PERSON>,
  },
  {
    title: "<PERSON>",
    email: "<EMAIL>",
    password: "password",
    role: "student" as Use<PERSON><PERSON><PERSON>,
  },
  {
    title: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    password: "password",
    role: "student" as User<PERSON>ole,
  },
];
