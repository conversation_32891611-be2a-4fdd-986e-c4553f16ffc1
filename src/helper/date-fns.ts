// Simple date utility functions to replace date-fns
export function formatDistanceToNow(date: Date, options?: { addSuffix?: boolean }): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const diffInSeconds = Math.floor(diff / 1000);
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);

  let result = '';
  
  if (diffInDays > 0) {
    result = diffInDays === 1 ? '1 day' : `${diffInDays} days`;
  } else if (diffInHours > 0) {
    result = diffInHours === 1 ? '1 hour' : `${diffInHours} hours`;
  } else if (diffInMinutes > 0) {
    result = diffInMinutes === 1 ? '1 minute' : `${diffInMinutes} minutes`;
  } else {
    result = 'less than a minute';
  }

  return options?.addSuffix ? `${result} ago` : result;
}


// Helper function to format timestamps
export function formatTimestamp(date: Date): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (seconds < 60) return 'Just now';
  if (minutes < 60) return `${minutes} minutes ago`;
  if (hours < 24) return `${hours} hours ago`;
  if (days === 1) return 'Yesterday';
  if (days < 7) return `${days} days ago`;
  return date.toLocaleTimeString();
}