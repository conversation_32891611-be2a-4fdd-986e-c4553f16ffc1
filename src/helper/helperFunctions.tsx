import { toast } from "sonner";

export const downloadSubmission = (submissionId: string, submissionData: any) => {
  // Create a zip file with submission contents
  generateSubmissionZip(submissionData)
    .then((blob) => {
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `submission_${submissionId}_${
        submissionData.assignmentTitle || "assignment"
      }.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success("Submission downloaded successfully");
    })
    .catch((error) => {
      console.error("Download failed:", error);
      toast.error("Failed to download submission");
    });
};

export const generateSubmissionZip = async (submissionData: any): Promise<Blob> => {
  // In a real implementation, this would use a library like JSZip
  // For now, we'll create a mock zip file with submission metadata
  const mockZipContent = {
    metadata: {
      submissionId: submissionData.id,
      assignmentTitle: submissionData.assignmentTitle,
      studentName: submissionData.studentName,
      submissionDate: submissionData.submissionDate,
      status: submissionData.status,
      grade: submissionData.grade,
      feedback: submissionData.feedback,
    },
    files: submissionData.files || [],
    notebooks: submissionData.notebooks || [],
    datasets: submissionData.datasets || [],
  };

  // Create a JSON file as a placeholder for the actual zip
  const jsonContent = JSON.stringify(mockZipContent, null, 2);
  return new Blob([jsonContent], { type: "application/json" });
};
