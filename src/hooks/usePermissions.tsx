import { useAuth } from "../App";
import {
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  extractPermissionKeys,
  getPermissionsByCategory,
  getPrimaryRoleName,
} from "../utils/authUtils";
import {
  BackendRole,
  AuthMeRole,
  AuthMeResponse,
  RefreshTokenRole,
} from "../api/authApi";
import { useAuthMeQuery } from "../api/authApi";

/**
 * Custom hook for permission checking and role management
 * Provides convenient methods to check user permissions and roles
 */
export function usePermissions() {
  const { user } = useAuth();
  const { data: authMeData } = useAuthMeQuery();

  // Get roles from authMe data if available, otherwise return empty array
  const userRoles: BackendRole[] | AuthMeRole[] | RefreshTokenRole[] = (
    authMeData as AuthMeResponse
  )?.success
    ? (authMeData as AuthMeResponse).user.roles
    : [];

  return {
    // Basic permission checks
    hasPermission: (permissionKey: string) =>
      hasPermission(userRoles, permissionKey),
    hasAnyPermission: (permissionKeys: string[]) =>
      hasAnyPermission(userRoles, permissionKeys),
    hasAllPermissions: (permissionKeys: string[]) =>
      hasAllPermissions(userRoles, permissionKeys),

    // Role information
    primaryRole: getPrimaryRoleName(userRoles),
    isAdmin: user?.role === "admin",
    isInstructor: user?.role === "instructor",
    isStudent: user?.role === "student",

    // Advanced permission data
    allPermissions: extractPermissionKeys(userRoles),
    permissionsByCategory:
      userRoles.length > 0 &&
      userRoles[0] !== undefined &&
      "permissions" in userRoles[0] &&
      userRoles[0].permissions &&
      userRoles[0].permissions.length > 0 &&
      typeof userRoles[0].permissions[0] === "object"
        ? getPermissionsByCategory(userRoles as BackendRole[] | AuthMeRole[])
        : {},

    // Raw data access
    userRoles,
    user,
  };
}

/**
 * Higher-order component for permission-based rendering
 * @param permissions - Permission key(s) required to render children
 * @param requireAll - If true, user must have ALL permissions. If false, user needs ANY permission
 * @param fallback - Component to render if permission check fails
 */
interface PermissionGateProps {
  permissions: string | string[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export function PermissionGate({
  permissions,
  requireAll = false,
  fallback = null,
  children,
}: PermissionGateProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions } =
    usePermissions();

  const permissionArray = Array.isArray(permissions)
    ? permissions
    : [permissions];

  let hasAccess: boolean;
  if (permissionArray.length === 1 && permissionArray[0]) {
    hasAccess = hasPermission(permissionArray[0]);
  } else if (requireAll) {
    hasAccess = hasAllPermissions(permissionArray);
  } else {
    hasAccess = hasAnyPermission(permissionArray);
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}

/**
 * Component for role-based rendering
 * @param roles - Role(s) required to render children
 * @param fallback - Component to render if role check fails
 */
interface RoleGateProps {
  roles: string | string[];
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export function RoleGate({ roles, fallback = null, children }: RoleGateProps) {
  const { user } = usePermissions();

  const roleArray = Array.isArray(roles) ? roles : [roles];
  const hasRole = user?.role ? roleArray.includes(user.role) : false;

  return hasRole ? <>{children}</> : <>{fallback}</>;
}
