import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.tsx";
import "./globals.css";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { JupyterWorkspaceProvider } from "./contexts/JupyterWorkspaceContext";
import { <PERSON>rowserRouter  } from "react-router-dom";
const queryClient = new QueryClient();

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter basename="/">
    <QueryClientProvider client={queryClient}>
      <JupyterWorkspaceProvider>
        <App />
      </JupyterWorkspaceProvider>
    </QueryClientProvider>
    </BrowserRouter>
  </React.StrictMode>
);
