import React, { useState } from "react";
import {
  Card,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { But<PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import {
  Home,
  Users,
  BookOpen,
  BarChart3,
  Settings,
  FileText,
  Shield,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Server,
  Eye,
  Plus,
  GraduationCap,
  Activity,
} from "lucide-react";
import { useNavigation } from "../App";
//import useNavigation from "../components/Context/NavigationContext";
const mockStats = {
  totalUsers: 1245,
  activeUsers: 1180,
  totalCourses: 45,
  activeCourses: 38,
  totalProjects: 156,
  systemHealth: 98.5,
};

const mockCourses = [
  {
    id: "1",
    name: "Data Science 101",
    instructor: "Dr<PERSON> <PERSON><PERSON>",
    students: 30,
    status: "active",
  },
  {
    id: "2",
    name: "Machine Learning Basics",
    instructor: "<PERSON><PERSON> <PERSON><PERSON>",
    students: 25,
    status: "active",
  },
  {
    id: "3",
    name: "Deep Learning Advanced",
    instructor: "<PERSON><PERSON> <PERSON><PERSON>",
    students: 20,
    status: "active",
  },
];

function DashboardContent() {
  const { navigateTo } = useNavigation();

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-bits-blue mb-2">
          Admin Dashboard
        </h2>
        <p className="text-muted-foreground">
          System overview and platform management
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-bits-blue">
              {mockStats.totalUsers}
            </div>
            <p className="text-sm text-muted-foreground">Total Users</p>
            <p className="text-xs text-green-600">+85 this month</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-bits-gold">
              {mockStats.activeCourses}
            </div>
            <p className="text-sm text-muted-foreground">Active Courses</p>
            <p className="text-xs text-muted-foreground">
              of {mockStats.totalCourses} total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {mockStats.totalProjects}
            </div>
            <p className="text-sm text-muted-foreground">Total Projects</p>
            <p className="text-xs text-green-600">142 submitted</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-bits-red">
              {mockStats.systemHealth}%
            </div>
            <p className="text-sm text-muted-foreground">System Health</p>
            <p className="text-xs text-green-600">All systems operational</p>
          </CardContent>
        </Card>
      </div>

      {/* Security Alert */}
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <div className="flex-1">
              <h4 className="font-medium text-red-800">Security Alert</h4>
              <p className="text-sm text-red-700">
                3 active security alerts require your attention
              </p>
            </div>
            <Button
              size="sm"
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-100"
              onClick={() => navigateTo("admin-audit")}
            >
              Review Alerts
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => navigateTo("manage-projects")}
        >
          <CardContent className="p-6 text-center">
            <FileText className="h-8 w-8 mx-auto mb-2 text-bits-blue" />
            <h3 className="font-semibold mb-1">Manage Projects</h3>
            <p className="text-sm text-muted-foreground">
              View and manage all platform projects
            </p>
          </CardContent>
        </Card>

        <Card
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => navigateTo("admin-reports")}
        >
          <CardContent className="p-6 text-center">
            <BarChart3 className="h-8 w-8 mx-auto mb-2 text-bits-gold" />
            <h3 className="font-semibold mb-1">Reports & Analytics</h3>
            <p className="text-sm text-muted-foreground">
              Generate comprehensive reports
            </p>
          </CardContent>
        </Card>

        <Card
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => navigateTo("admin-audit")}
        >
          <CardContent className="p-6 text-center">
            <Shield className="h-8 w-8 mx-auto mb-2 text-bits-red" />
            <h3 className="font-semibold mb-1">Audit & Security</h3>
            <p className="text-sm text-muted-foreground">
              Monitor system security and activities
            </p>
          </CardContent>
        </Card>

        <Card
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => navigateTo("gradebook")}
        >
          <CardContent className="p-6 text-center">
            <GraduationCap className="h-8 w-8 mx-auto mb-2 text-green-600" />
            <h3 className="font-semibold mb-1">Platform Gradebook</h3>
            <p className="text-sm text-muted-foreground">
              Monitor all student grades
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Recent Activity</CardTitle>
            <Button
              size="sm"
              variant="outline"
              onClick={() => navigateTo("admin-audit")}
            >
              <Activity className="h-4 w-4 mr-2" />
              View All Audit Logs
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="font-medium">New course created</p>
                <p className="text-sm text-muted-foreground">
                  Dr. A. Sharma created "Advanced Data Science"
                </p>
              </div>
              <span className="text-xs text-muted-foreground ml-auto">
                2 hours ago
              </span>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
              <Users className="h-5 w-5 text-blue-600" />
              <div>
                <p className="font-medium">Bulk user enrollment</p>
                <p className="text-sm text-muted-foreground">
                  25 new students added to Machine Learning course
                </p>
              </div>
              <span className="text-xs text-muted-foreground ml-auto">
                4 hours ago
              </span>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-red-50 rounded-lg">
              <Shield className="h-5 w-5 text-red-600" />
              <div>
                <p className="font-medium">Security event detected</p>
                <p className="text-sm text-muted-foreground">
                  Multiple failed login attempts from IP *************
                </p>
              </div>
              <span className="text-xs text-muted-foreground ml-auto">
                6 hours ago
              </span>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="font-medium">System alert</p>
                <p className="text-sm text-muted-foreground">
                  High AWS usage detected - automatic scaling initiated
                </p>
              </div>
              <span className="text-xs text-muted-foreground ml-auto">
                8 hours ago
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Platform Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Courses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockCourses.map((course) => (
                <div
                  key={course.id}
                  className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                >
                  <div>
                    <p className="font-medium">{course.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {course.instructor} • {course.students} students
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className="bg-green-100 text-green-800">
                      95% completion
                    </Badge>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        navigateTo("view-course", { courseId: course.id })
                      }
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span>Server Uptime</span>
                <span className="font-semibold text-green-600">99.8%</span>
              </div>

              <div className="flex justify-between items-center">
                <span>Active Users</span>
                <span className="font-semibold">{mockStats.activeUsers}</span>
              </div>

              <div className="flex justify-between items-center">
                <span>Storage Usage</span>
                <span className="font-semibold">750GB / 1TB</span>
              </div>

              <div className="flex justify-between items-center">
                <span>API Response Time</span>
                <span className="font-semibold text-green-600">145ms</span>
              </div>

              <div className="flex justify-between items-center">
                <span>Security Alerts</span>
                <span className="font-semibold text-red-600">3 active</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function AdminDashboard() {
  return (
    <div className="p-6 space-y-6">
      <DashboardContent />
    </div>
  );
}
