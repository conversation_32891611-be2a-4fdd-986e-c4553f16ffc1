import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Input } from "../components/ui/input";
import {
  ArrowLeft,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  Users,
  BookOpen,
  Calendar,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
} from "lucide-react";
//import { useNavigation } from "../App";
import useNavigation from "../components/Context/NavigationContext";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "../components/ui/tabs";
import { useNavigate } from "react-router-dom";

interface Project {
  id: string;
  title: string;
  course: string;
  instructor: string;
  description: string;
  dueDate: string;
  createdDate: string;
  totalStudents: number;
  submissions: number;
  status: "active" | "closed" | "draft";
  difficulty: "beginner" | "intermediate" | "advanced";
  isTemplate: boolean;
  tags: string[];
}

interface ProjectTemplate {
  id: string;
  title: string;
  description: string;
  difficulty: "beginner" | "intermediate" | "advanced";
  estimatedHours: number;
  tags: string[];
  usageCount: number;
  lastUsed: string;
  createdBy: string;
}

// Mock data
const mockProjects: Project[] = [
  {
    id: "1",
    title: "Linear Regression Analysis",
    course: "Data Science 101",
    instructor: "Dr. A. Sharma",
    description:
      "Build a classification model using supervised learning techniques",
    dueDate: "2025-01-15",
    createdDate: "2024-12-01",
    totalStudents: 30,
    submissions: 25,
    status: "active",
    difficulty: "beginner",
    isTemplate: false,
    tags: ["machine-learning", "regression", "python"],
  },
  {
    id: "2",
    title: "Neural Networks Implementation",
    course: "Deep Learning",
    instructor: "Dr. C. Singh",
    description: "Implement a neural network from scratch using NumPy",
    dueDate: "2024-12-30",
    createdDate: "2024-11-15",
    totalStudents: 20,
    submissions: 20,
    status: "closed",
    difficulty: "advanced",
    isTemplate: true,
    tags: ["deep-learning", "neural-networks", "numpy"],
  },
  {
    id: "3",
    title: "Data Visualization Dashboard",
    course: "Data Science 101",
    instructor: "Dr. A. Sharma",
    description:
      "Create an interactive dashboard using modern visualization libraries",
    dueDate: "2025-02-01",
    createdDate: "2024-12-10",
    totalStudents: 30,
    submissions: 0,
    status: "active",
    difficulty: "intermediate",
    isTemplate: false,
    tags: ["visualization", "dashboard", "plotly"],
  },
];

const mockTemplates: ProjectTemplate[] = [
  {
    id: "t1",
    title: "Basic ML Classification Template",
    description:
      "A starter template for building classification models with common datasets",
    difficulty: "beginner",
    estimatedHours: 10,
    tags: ["classification", "scikit-learn", "pandas"],
    usageCount: 15,
    lastUsed: "2024-12-01",
    createdBy: "Dr. A. Sharma",
  },
  {
    id: "t2",
    title: "Deep Learning Computer Vision",
    description: "Template for image classification using CNNs with TensorFlow",
    difficulty: "advanced",
    estimatedHours: 25,
    tags: ["computer-vision", "cnn", "tensorflow"],
    usageCount: 8,
    lastUsed: "2024-11-28",
    createdBy: "Dr. C. Singh",
  },
  {
    id: "t3",
    title: "Time Series Analysis",
    description: "Complete template for time series forecasting and analysis",
    difficulty: "intermediate",
    estimatedHours: 15,
    tags: ["time-series", "forecasting", "arima"],
    usageCount: 12,
    lastUsed: "2024-12-05",
    createdBy: "Dr. B. Patel",
  },
];

export default function AdminProjectsPage() {
  const { goBack, navigateTo } = useNavigation();
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [difficultyFilter, setDifficultyFilter] = useState("all");
  const [selectedTab, setSelectedTab] = useState("projects");

  const filteredProjects = mockProjects.filter((project) => {
    const matchesSearch =
      project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.course.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.instructor.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" || project.status === statusFilter;
    const matchesDifficulty =
      difficultyFilter === "all" || project.difficulty === difficultyFilter;
    return matchesSearch && matchesStatus && matchesDifficulty;
  });

  const filteredTemplates = mockTemplates.filter(
    (template) =>
      template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "closed":
        return "bg-gray-100 text-gray-800";
      case "draft":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "beginner":
        return "bg-green-100 text-green-800";
      case "intermediate":
        return "bg-yellow-100 text-yellow-800";
      case "advanced":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const projectStats = {
    total: mockProjects.length,
    active: mockProjects.filter((p) => p.status === "active").length,
    closed: mockProjects.filter((p) => p.status === "closed").length,
    templates: mockProjects.filter((p) => p.isTemplate).length,
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={goBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-bits-blue">
              Project Management
            </h1>
            <p className="text-muted-foreground">
              Manage all projects and templates across the platform
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
          <Button
            className="bg-bits-gold hover:bg-bits-gold/90"
            onClick={() => navigateTo("create-project")}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Project
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-bits-blue">
              {projectStats.total}
            </div>
            <p className="text-sm text-muted-foreground">Total Projects</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {projectStats.active}
            </div>
            <p className="text-sm text-muted-foreground">Active Projects</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-gray-600">
              {projectStats.closed}
            </div>
            <p className="text-sm text-muted-foreground">Completed</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-bits-gold">
              {projectStats.templates}
            </div>
            <p className="text-sm text-muted-foreground">Templates</p>
          </CardContent>
        </Card>
      </div>

      <Tabs
        value={selectedTab}
        onValueChange={setSelectedTab}
        className="space-y-6"
      >
        <TabsList>
          <TabsTrigger value="projects">All Projects</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="projects" className="space-y-6">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search projects..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9"
                  />
                </div>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={difficultyFilter}
                  onValueChange={setDifficultyFilter}
                >
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by difficulty" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Difficulties</SelectItem>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Projects Table */}
          <Card>
            <CardHeader>
              <CardTitle>Projects ({filteredProjects.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Project</TableHead>
                    <TableHead>Course</TableHead>
                    <TableHead>Instructor</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Difficulty</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredProjects.map((project) => (
                    <TableRow key={project.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{project.title}</p>
                          <div className="flex space-x-1 mt-1">
                            {project.tags.slice(0, 2).map((tag, index) => (
                              <Badge
                                key={index}
                                variant="outline"
                                className="text-xs"
                              >
                                {tag}
                              </Badge>
                            ))}
                            {project.isTemplate && (
                              <Badge
                                variant="outline"
                                className="text-xs bg-blue-50 text-blue-700"
                              >
                                Template
                              </Badge>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{project.course}</TableCell>
                      <TableCell>{project.instructor}</TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(project.status)}>
                          {project.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={getDifficultyColor(project.difficulty)}
                        >
                          {project.difficulty}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-sm">
                            {project.submissions}/{project.totalStudents}{" "}
                            submitted
                          </div>
                          <div className="w-20 bg-gray-200 rounded-full h-1">
                            <div
                              className="bg-bits-blue h-1 rounded-full"
                              style={{
                                width: `${
                                  (project.submissions /
                                    project.totalStudents) *
                                  100
                                }%`,
                              }}
                            />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(project.dueDate).toLocaleDateString()}
                          {new Date(project.dueDate) < new Date() &&
                            project.status === "active" && (
                              <div className="text-red-600 text-xs">
                                Overdue
                              </div>
                            )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>{
                              navigate(`/project-details/${project.id}`)
                              // navigateTo("project-details", {
                              //   projectId: project.id,
                              // })
                            }
                            }
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>{
                              navigate(`/edit-project/${project.id}`)
                              navigateTo("edit-project", {
                                projectId: project.id,
                              })}
                            }
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>
                  Project Templates ({filteredTemplates.length})
                </CardTitle>
                <Button
                  className="bg-bits-gold hover:bg-bits-gold/90"
                  onClick={() => navigateTo("create-project")}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Template
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {filteredTemplates.map((template) => (
                  <div key={template.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-medium">{template.title}</h4>
                        <p className="text-sm text-muted-foreground">
                          {template.description}
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Created by {template.createdBy} • Last used:{" "}
                          {new Date(template.lastUsed).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge
                          className={getDifficultyColor(template.difficulty)}
                        >
                          {template.difficulty}
                        </Badge>
                        <Badge variant="outline">
                          {template.estimatedHours}h
                        </Badge>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex space-x-1">
                        {template.tags.map((tag, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="text-xs"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="text-sm text-muted-foreground">
                          Used {template.usageCount} times
                        </div>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4 mr-2" />
                            Preview
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              navigateTo("edit-project", {
                                projectId: template.id,
                              })
                            }
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            className="bg-bits-blue hover:bg-bits-blue/90"
                          >
                            Use Template
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>Project Completion Trends</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">85%</div>
                    <p className="text-sm text-muted-foreground">
                      Average completion rate
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>This Month</span>
                      <span>92%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Last Month</span>
                      <span>78%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>3 Months Ago</span>
                      <span>85%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Popular Technologies</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>Python</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-bits-blue h-2 rounded-full"
                          style={{ width: "90%" }}
                        />
                      </div>
                      <span className="text-sm">90%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>TensorFlow</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-bits-gold h-2 rounded-full"
                          style={{ width: "65%" }}
                        />
                      </div>
                      <span className="text-sm">65%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Scikit-learn</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: "75%" }}
                        />
                      </div>
                      <span className="text-sm">75%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div className="flex-1">
                    <p className="font-medium">Project completed</p>
                    <p className="text-sm text-muted-foreground">
                      Neural Networks Implementation - 20/20 students submitted
                    </p>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    1 hour ago
                  </span>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                  <Plus className="h-5 w-5 text-blue-600" />
                  <div className="flex-1">
                    <p className="font-medium">New template created</p>
                    <p className="text-sm text-muted-foreground">
                      Dr. A. Sharma created "Advanced NLP Pipeline"
                    </p>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    3 hours ago
                  </span>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                  <div className="flex-1">
                    <p className="font-medium">Deadline approaching</p>
                    <p className="text-sm text-muted-foreground">
                      Data Visualization Dashboard due in 2 days - 15/30
                      submitted
                    </p>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    6 hours ago
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
