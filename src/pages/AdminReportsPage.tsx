import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import {
  Download,
  Calendar,
  Users,
  BookOpen,
  TrendingUp,
  BarChart3,
  FileText,
  PieChart,
  Activity,
  Clock,
  Award,
  Filter,
  Layers,
  Zap,
  BarChart,
  Eye,
  Plus,
  CheckCircle,
  Cpu,
  UserCheck,
  Clipboard,
  Save,
} from "lucide-react";
//import { useNavigation } from "../App";
import useNavigation from "../components/Context/NavigationContext";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "../components/ui/tabs";
// import { DatePicker } from "./ui/calendar";
import { Progress } from "../components/ui/progress";

interface ReportMetrics {
  students: {
    total: number;
    active: number;
    newThisMonth: number;
    graduatedThisMonth: number;
  };
  courses: {
    total: number;
    active: number;
    completed: number;
    averageEnrollment: number;
  };
  projects: {
    total: number;
    submitted: number;
    graded: number;
    averageGrade: string;
  };
  system: {
    serverUptime: number;
    storageUsed: number;
    totalStorage: number;
    awsCosts: number;
  };
}

interface PerformanceData {
  course: string;
  instructor: string;
  enrolledStudents: number;
  completionRate: number;
  averageGrade: string;
  satisfactionScore: number;
}

const mockMetrics: ReportMetrics = {
  students: {
    total: 1250,
    active: 1180,
    newThisMonth: 85,
    graduatedThisMonth: 32,
  },
  courses: {
    total: 45,
    active: 38,
    completed: 7,
    averageEnrollment: 28,
  },
  projects: {
    total: 156,
    submitted: 142,
    graded: 128,
    averageGrade: "B+",
  },
  system: {
    serverUptime: 99.8,
    storageUsed: 750,
    totalStorage: 1000,
    awsCosts: 2450,
  },
};

const mockPerformanceData: PerformanceData[] = [
  {
    course: "Data Science 101",
    instructor: "Dr. A. Sharma",
    enrolledStudents: 30,
    completionRate: 95,
    averageGrade: "A-",
    satisfactionScore: 4.6,
  },
  {
    course: "Machine Learning Basics",
    instructor: "Dr. B. Patel",
    enrolledStudents: 25,
    completionRate: 88,
    averageGrade: "B+",
    satisfactionScore: 4.3,
  },
  {
    course: "Deep Learning Advanced",
    instructor: "Dr. C. Singh",
    enrolledStudents: 20,
    completionRate: 85,
    averageGrade: "B",
    satisfactionScore: 4.1,
  },
];

export default function AdminReportsPage() {
  const { goBack } = useNavigation();
  const [selectedTab, setSelectedTab] = useState("overview");
  const [selectedPeriod, setSelectedPeriod] = useState("this-month");
  const [selectedCourse, setSelectedCourse] = useState("all");

  const generateReport = (reportType: string) => {
    console.log(`Generating ${reportType} report...`);
    // Here you would typically call an API to generate the report
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Reports & Analytics</h1>
          <p className="text-md text-bits-grey-600">
            Comprehensive platform insights and data analysis
          </p>
        </div>
        <div className="flex space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-30 text-sm font-semibold text-bits-grey">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="this-week">This Week</SelectItem>
              <SelectItem value="this-month">This Month</SelectItem>
              <SelectItem value="this-quarter">This Quarter</SelectItem>
              <SelectItem value="this-year">This Year</SelectItem>
              <SelectItem value="custom">Custom Range</SelectItem>
            </SelectContent>
          </Select>
          <Button className="bg-bits-gold hover:bg-bits-gold/90">
            <Download className="h-4 w-4 mr-2" />
            Export All Reports
          </Button>
        </div>
      </div>

      <Tabs
        value={selectedTab}
        onValueChange={setSelectedTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-4 text-sm-medium text-bits-grey">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="academic">Academic</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
          <TabsTrigger value="custom">Custom Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div>
                    <div className="text-3xl font-bold text-bits-blue-neutral-900 mb-3">
                      {mockMetrics.students.total}
                    </div>
                    <p className="text-sm font-normal text-bits-grey-600">
                      Total Students
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div>
                    <div className="text-3xl font-bold text-bits-blue-neutral-900 mb-3">
                      {mockMetrics.courses.active}
                    </div>
                    <p className="text-sm font-normal text-bits-grey-600">
                      Active Courses
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div>
                    <div className="text-3xl font-bold text-bits-blue-neutral-900 mb-3">
                      {mockMetrics.projects.submitted}
                    </div>
                    <p className="text-sm font-normal text-bits-grey-600">
                      Projects Submitted
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div>
                    <div className="text-3xl font-bold text-bits-blue-neutral-900 mb-3">
                      {mockMetrics.system.serverUptime}
                    </div>
                    <p className="text-sm font-normal text-bits-grey-600">
                      System Uptime
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts and Trends */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Layers className="h-5 w-5 text-bits-warning" />
                  <span className="text-lg font-medium text-bits-grey">
                    Student Enrollment Trends
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-md font-normal text-bits-grey-600">
                      Active Students
                    </span>
                    <div className="flex flex-row gap-3 items-center space-x-2">
                      <Progress
                        value={95}
                        className="w-32 h-2 text-bits-blue bg-bits-grey-300"
                      />
                      <div className="text-xs font-normal text-bits-grey-600 w-10">
                        {mockMetrics.students.active}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-md font-normal text-bits-grey-600">
                      New Enrollments
                    </span>
                    <div className="flex flex-row gap-3 items-center space-x-2">
                      <Progress
                        value={85}
                        className="w-32 h-2 text-bits-blue bg-bits-grey-300 "
                      />
                      <div className="text-xs font-normal text-bits-grey-600 w-10">
                        +{mockMetrics.students.newThisMonth}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-bits-blue" />
                  <span className="text-lg font-medium text-bits-grey">
                    Top Performing Projects
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  {mockPerformanceData.map((course, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 "
                    >
                      <div>
                        <p className="font-medium text-md">{course.course}</p>
                        <p className="text-sm font-normal text-bits-grey-600">
                          {course.instructor} • {course.enrolledStudents}{" "}
                          students
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-xs font-medium text-bits-green">
                          {course.completionRate}% completion
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>
                <div className="flex flex-row gap-3 items-center">
                  <Zap className="h-5 w-5 text-bits-blue" />
                  <span className="text-lg font-medium text-bits-grey">
                    Quick Report Generation
                  </span>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => generateReport("student-performance")}
                >
                  <Users className="size-6 text-bits-blue" />
                  <span className="text-xl font-semibold text-bits-blue-neutral-900">
                    Student Performance Report
                  </span>
                </Button>

                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => generateReport("course-analytics")}
                >
                  <BookOpen className="size-6 text-bits-blue" />
                  <span className="text-xl font-semibold text-bits-blue-neutral-900">
                    Course Analytics
                  </span>
                </Button>

                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => generateReport("system-usage")}
                >
                  <Activity className="size-6  text-bits-blue" />
                  <span className="text-xl font-semibold text-bits-blue-neutral-900">
                    System Usage Report
                  </span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="academic" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>
                      <div className="flex flex-row gap-2 items-center">
                        <BarChart className="h-5 w-5 text-bits-blue" />
                        <span className="text-lg font-medium text-bits-grey">
                          Project Performance Analysis
                        </span>
                      </div>
                    </CardTitle>
                    <Select
                      value={selectedCourse}
                      onValueChange={setSelectedCourse}
                    >
                      <SelectTrigger className="w-48  ">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="text-md font-normal text-bits-grey-500">
                        <SelectItem value="all">All Courses</SelectItem>
                        <SelectItem value="ds101">Data Science 101</SelectItem>
                        <SelectItem value="ml101">
                          Machine Learning Basics
                        </SelectItem>
                        <SelectItem value="dl201">
                          Deep Learning Advanced
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockPerformanceData.map((course, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <h4 className="font-semibold text-xl text-bits-blue-neutral-900">
                              {course.course}
                            </h4>
                            <p className="text-sm font-normal text-bits-grey-600">
                              {course.instructor}
                            </p>
                          </div>
                          <Badge className="text-xs font-medium bg-bits-green text-white rounded-xl">
                            {course.averageGrade}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-3 gap-4">
                          <div>
                            <p className="text-sm font-semibold text-bits-blue-neutral-900">
                              Enrollment
                            </p>
                            <p className="text-lg font-normal text-bits-grey-600">
                              {course.enrolledStudents}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm font-semibold text-bits-blue-neutral-900">
                              Completion Rate
                            </p>
                            <div className="flex items-center space-x-2">
                              <Progress
                                value={course.completionRate}
                                className="flex-1 h-2 text-bits-blue bg-bits-grey-300"
                              />
                              <span className="text-lg font-normal text-bits-grey-600">
                                {course.completionRate}%
                              </span>
                            </div>
                          </div>
                          <div>
                            <p className="text-sm font-semibold text-bits-blue-neutral-900">
                              Satisfaction
                            </p>
                            <p className="text-lg font-normal text-bits-grey-600">
                              {course.satisfactionScore}/5.0
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>
                    <div className="flex flex-row gap-2 items-center">
                      <TrendingUp className="h-5 w-5 text-bits-blue" />
                      <span className="text-lg font-medium text-bits-grey">
                        Grade Distribution
                      </span>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-md regular text-bits-grey-600">
                        A (90-100%)
                      </span>
                      <div className="flex items-center space-x-2">
                        <Progress
                          value={35}
                          className="w-20 h-2 text-bits-blue bg-bits-grey-300"
                        />
                        <span className="text-xs text-bits-grey-600 w-10">
                          35
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-md regular text-bits-grey-600">
                        B (80-89%)
                      </span>
                      <div className="flex items-center space-x-2">
                        <Progress
                          value={40}
                          className="w-20 h-2 text-bits-blue bg-bits-grey-300"
                        />
                        <span className="text-xs text-bits-grey-600 w-10">
                          40
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-md regular text-bits-grey-600">
                        C (70-79%)
                      </span>
                      <div className="flex items-center space-x-2">
                        <Progress
                          value={20}
                          className="w-20 h-2 text-bits-blue bg-bits-grey-300"
                        />
                        <span className="text-xs text-bits-grey-600 w-10">
                          20
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-md regular text-bits-grey-600">
                        D/F (&lt;70%)
                      </span>
                      <div className="flex items-center space-x-2">
                        <Progress
                          value={5}
                          className="w-20 h-2 text-bits-blue bg-bits-grey-300"
                        />
                        <span className="text-xs text-bits-grey-600 w-10">
                          5
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>
                    <div className="flex flex-row gap-2 items-center">
                      <Eye className="h-5 w-5 text-bits-blue" />
                      <span className="text-lg font-medium text-bits-grey">
                        Academic Insights
                      </span>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="p-3 bg-bits-green-success-25 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-bits-green" />
                        <span className="text-sm font-medium">
                          Project completed
                        </span>
                      </div>
                      <p className="text-xs text-bits-grey-600 mt-1 ml-6">
                        Neural Networks Implementation - 20/20 students
                        submitted
                      </p>
                    </div>

                    <div className="p-3 bg-bits-blue-50 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Plus className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium">
                          New template created
                        </span>
                      </div>
                      <p className="text-xs text-bits-grey-600 mt-1 ml-6">
                        Dr. A. Sharma created "Advanced NLP Pipeline"
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="system" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Cpu className="h-6 w-6 text-bits-warning" />
                  <span className="text-lg font-medium text-bits-grey">
                    System Performance
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-md text-bits-grey-600">
                    Server Uptime
                  </span>
                  <div className="text-right">
                    <div className="text-md font-medium text-bits-green">
                      {mockMetrics.system.serverUptime}%
                    </div>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-md text-bits-grey-600">
                    Active Users
                  </span>
                  <div className="text-right">
                    <div className="text-md font-medium text-bits-grey">
                      1180
                    </div>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-md text-bits-grey-600">
                    Storage Usage
                  </span>
                  <div className="text-right">
                    <div className="text-md font-medium text-bits-grey">
                      {mockMetrics.system.storageUsed}GB /{" "}
                      {mockMetrics.system.totalStorage}GB
                    </div>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-md text-bits-grey-600">
                    API Response Time
                  </span>
                  <div className="text-right">
                    <div className="text-md font-medium text-bits-green">
                      145ms
                    </div>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-md text-bits-grey-600">
                    Security Alerts
                  </span>
                  <div className="text-right">
                    <div className="text-md font-medium text-bits-red">
                      3 active
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>
                  <div className="flex flex-row gap-2 items-center">
                    <UserCheck className="h-6 w-6 text-bits-warning" />
                    <span className="text-lg font-medium text-bits-grey">
                      Usage Statistics
                    </span>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-md text-bits-grey-600">
                    Daily Active Users
                  </span>
                  <span className="text-md font-medium text-bits-grey">
                    1,180
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-md text-bits-grey-600">
                    Peak Concurrent Users
                  </span>
                  <span className="text-md font-medium text-bits-grey">
                    485
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-md text-bits-grey-600">
                    Data Transfer (Daily)
                  </span>
                  <span className="text-md font-medium text-bits-grey">
                    2.3 TB
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-md text-bits-grey-600">
                    Sandbox Sessions
                  </span>
                  <span className="text-md font-medium text-bits-grey">
                    342
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>
                <div className="flex flex-row  gap-2 items-center">
                  <Zap className="h-5 w-5 text-bits-blue" />
                  <span className="text-lg font-medium text-bits-grey">
                    System Health Monitoring
                  </span>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="w-3 h-3 bg-bits-green rounded-full"></div>
                    <span className="text-md font-semibold text-bits-blue-neutral-900">
                      Database
                    </span>
                  </div>
                  <p className="text-sm text-bits-grey-600">
                    All systems operational
                  </p>
                  <p className="text-sm text-bits-green">Response time: 12ms</p>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-md font-semibold text-bits-blue-neutral-900">
                      API Services
                    </span>
                  </div>
                  <p className="text-sm text-bits-grey-600">
                    All endpoints healthy
                  </p>
                  <p className="text-sm text-bits-green">Avg response: 145ms</p>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="w-3 h-3 bg-bits-red rounded-full"></div>
                    <span className="text-md font-semibold text-bits-blue-neutral-900">
                      AWS Resources
                    </span>
                  </div>
                  <p className="text-sm text-bits-grey-600">
                    High usage detected
                  </p>
                  <p className="text-sm text-bits-red">CPU: 78%, Memory: 65%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="custom" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>
                <div className="flex flex-row gap-2 items-center">
                  <Clipboard className="h-5 w-5 text-bits-blue" />
                  <span className="text-lg font-medium text-bits-grey">
                    Custom Report Builder
                  </span>
                </div>
              </CardTitle>
              <CardDescription className="text-sm text-bits-grey-600">
                Create tailored reports based on specific criteria
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label
                    className="text-sm font-medium text-bits-grey-700"
                    htmlFor="report-name"
                  >
                    Report Name
                  </Label>
                  <Input
                    id="report-name"
                    placeholder="Enter report name"
                    className="text-md  text-bits-grey-500 bg-bits-grey-100"
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="report-type"
                    className="text-sm font-medium text-bits-grey-700"
                  >
                    Report Type
                  </Label>
                  <Select>
                    <SelectTrigger className="bg-bits-grey-100">
                      <SelectValue
                        className="text-md text-bits-grey-500"
                        placeholder="Select report type"
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="student-progress">
                        Student Progress
                      </SelectItem>
                      <SelectItem value="course-analytics">
                        Course Analytics
                      </SelectItem>
                      <SelectItem value="system-metrics">
                        System Metrics
                      </SelectItem>
                      <SelectItem value="financial-summary">
                        Financial Summary
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="date-range"
                    className="text-sm font-medium text-bits-grey-700"
                  >
                    Date Range
                  </Label>
                  <Select>
                    <SelectTrigger className="bg-bits-grey-100">
                      <SelectValue
                        placeholder="Select date range"
                        className="text-md text-bits-grey-500"
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="last-7-days">Last 7 Days</SelectItem>
                      <SelectItem value="last-30-days">Last 30 Days</SelectItem>
                      <SelectItem value="last-quarter">Last Quarter</SelectItem>
                      <SelectItem value="last-year">Last Year</SelectItem>
                      <SelectItem value="custom">Custom Range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="format"
                    className="text-sm font-medium text-bits-grey-700"
                  >
                    Output Format
                  </Label>
                  <Select>
                    <SelectTrigger className="bg-bits-grey-100">
                      <SelectValue
                        placeholder="Select format"
                        className="text-md text-bits-grey-500"
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                      <SelectItem value="csv">CSV</SelectItem>
                      <SelectItem value="json">JSON</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-start space-x-2 pt-4">
                <Button className="bg-bits-blue hover:bg-bits-blue/90">
                  <Download className="h-4 w-4 mr-2" />
                  <span className="text-sm font-semibold text-white">
                    Generate Report
                  </span>
                </Button>
                <Button variant="outline">
                  <span className="text-sm font-semibold text-bits-grey">
                    Save Template
                  </span>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>
                <div className="flex flex-row gap-2 items-center">
                  <Save className="h-5 w-5 text-bits-blue" />
                  <span className="text-lg font-medium text-bits-grey">
                    Saved Templates
                  </span>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="text-xl font-semibold text-bits-blue-neutral-900">
                      Monthly Performance Summary
                    </p>
                    <p className="text-sm text-bits-grey-600">
                      Student progress and course completion rates
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-sm font-semibold text-bits-grey-700"
                    >
                      Edit
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-sm font-semibold text-white bg-bits-blue"
                    >
                      Generate
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
