import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { Switch } from "../components/ui/switch";
import { Textarea } from "../components/ui/textarea";
import { Progress } from "../components/ui/progress";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../components/ui/dialog";
import { Checkbox } from "../components/ui/checkbox";
import {
  Settings,
  Users,
  Shield,
  Server,
  Database,
  Bell,
  Mail,
  Key,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Eye,
  EyeOff,
  Search,
  Plus,
  Edit,
  Cpu,
  MemoryStick,
  HardDrive,
  Zap,
  Clock,
  DollarSign,
  BarChart3,
} from "lucide-react";
import { useNavi<PERSON> } from "../App";
import {
  Tabs,
  Tabs<PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "../components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import { Separator } from "../components/ui/separator";
import { toast } from "sonner";

interface SystemSettings {
  general: {
    platformName: string;
    supportEmail: string;
    timeZone: string;
    language: string;
    maintenanceMode: boolean;
  };
  security: {
    passwordMinLength: number;
    sessionTimeout: number;
    twoFactorRequired: boolean;
    allowedDomains: string[];
    maxLoginAttempts: number;
  };
  aws: {
    region: string;
    instanceType: string;
    autoScaling: boolean;
    storageLimit: number;
    backupRetention: number;
  };
  notifications: {
    emailEnabled: boolean;
    smsEnabled: boolean;
    pushEnabled: boolean;
    adminAlerts: boolean;
  };
}

interface ResourceProfile {
  id: string;
  name: string;
  instanceType: string;
  vCpu: number;
  ramGB: number;
  gpuCount: number;
  diskGB: number;
  idleTimeoutMin: number;
  maxRuntimeHrs: number;
  costBudgetUSD: number;
  tags: string[];
  allowedPackages: string[];
  active: boolean;
}

interface PlatformQuotas {
  maxSandboxesPerUser: number;
  totalvCPUPool: number;
  monthlyCostCap: number;
  currentSandboxes: number;
  currentvCPU: number;
  currentMonthlyCost: number;
}

const initialSettings: SystemSettings = {
  general: {
    platformName: "BITS Pilani Data Science Platform",
    supportEmail: "<EMAIL>",
    timeZone: "Asia/Kolkata",
    language: "en",
    maintenanceMode: false,
  },
  security: {
    passwordMinLength: 8,
    sessionTimeout: 120,
    twoFactorRequired: false,
    allowedDomains: ["bits.edu"],
    maxLoginAttempts: 5,
  },
  aws: {
    region: "ap-south-1",
    instanceType: "t3.medium",
    autoScaling: true,
    storageLimit: 1000,
    backupRetention: 30,
  },
  notifications: {
    emailEnabled: false,
    smsEnabled: false,
    pushEnabled: false,
    adminAlerts: false,
  },
};

const mockResourceProfiles: ResourceProfile[] = [
  {
    id: "1",
    name: "Light CPU - Basic",
    instanceType: "t3.medium",
    vCpu: 2,
    ramGB: 4,
    gpuCount: 0,
    diskGB: 20,
    idleTimeoutMin: 30,
    maxRuntimeHrs: 4,
    costBudgetUSD: 2.5,
    tags: ["basic", "cpu-light"],
    allowedPackages: ["pandas", "numpy", "matplotlib", "scikit-learn"],
    active: true,
  },
  {
    id: "2",
    name: "GPU Heavy - ML Training",
    instanceType: "p3.2xlarge",
    vCpu: 8,
    ramGB: 32,
    gpuCount: 1,
    diskGB: 100,
    idleTimeoutMin: 60,
    maxRuntimeHrs: 12,
    costBudgetUSD: 25,
    tags: ["gpu", "ml-training"],
    allowedPackages: ["tensorflow", "pytorch", "cuda", "pandas", "numpy"],
    active: true,
  },
  {
    id: "3",
    name: "Standard CPU - Data Analysis",
    instanceType: "m5.large",
    vCpu: 4,
    ramGB: 8,
    gpuCount: 0,
    diskGB: 50,
    idleTimeoutMin: 45,
    maxRuntimeHrs: 8,
    costBudgetUSD: 5,
    tags: ["standard", "data-analysis"],
    allowedPackages: ["pandas", "numpy", "matplotlib", "seaborn", "plotly"],
    active: true,
  },
];

const mockPlatformQuotas: PlatformQuotas = {
  maxSandboxesPerUser: 3,
  totalvCPUPool: 100,
  monthlyCostCap: 1000,
  currentSandboxes: 24,
  currentvCPU: 68,
  currentMonthlyCost: 650,
};

const availablePackages = [
  "pandas",
  "numpy",
  "matplotlib",
  "seaborn",
  "plotly",
  "scikit-learn",
  "tensorflow",
  "pytorch",
  "keras",
  "jupyter",
  "scipy",
  "statsmodels",
  "opencv-python",
];

function EditResourceProfileModal({
  profile,
  isOpen,
  onClose,
  onSave,
}: {
  profile?: ResourceProfile | undefined;
  isOpen: boolean;
  onClose: () => void;
  onSave: (profile: ResourceProfile) => void;
}) {
  const [formData, setFormData] = useState<ResourceProfile>(
    profile || {
      id: "",
      name: "",
      instanceType: "t3.medium",
      vCpu: 2,
      ramGB: 4,
      gpuCount: 0,
      diskGB: 20,
      idleTimeoutMin: 30,
      maxRuntimeHrs: 4,
      costBudgetUSD: 2.5,
      tags: [],
      allowedPackages: [],
      active: true,
    }
  );

  const [newTag, setNewTag] = useState("");

  const handleSave = () => {
    if (!formData.name.trim() || !formData.instanceType) {
      toast.error("Please fill in all required fields");
      return;
    }

    const profileToSave = {
      ...formData,
      id: formData.id || Date.now().toString(),
    };

    onSave(profileToSave);
    onClose();
    toast.success(
      `Resource profile ${profile ? "updated" : "created"} successfully`
    );
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()],
      }));
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove),
    }));
  };

  const togglePackage = (pkg: string) => {
    setFormData((prev) => ({
      ...prev,
      allowedPackages: prev.allowedPackages.includes(pkg)
        ? prev.allowedPackages.filter((p) => p !== pkg)
        : [...prev.allowedPackages, pkg],
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto bg-white border">
        <DialogHeader>
          <DialogTitle>
            {profile ? "Edit Resource Profile" : "Create Resource Profile"}
          </DialogTitle>
          <DialogDescription>
            Configure the computational resources and constraints for this
            profile.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h4 className="font-medium">Basic Information</h4>

            <div className="space-y-2">
              <Label htmlFor="profileName">Profile Name *</Label>
              <Input
                id="profileName"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder="e.g., Light CPU - Basic"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="instanceType">Instance Type *</Label>
              <Select
                value={formData.instanceType}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, instanceType: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="t3.medium">t3.medium</SelectItem>
                  <SelectItem value="m5.large">m5.large</SelectItem>
                  <SelectItem value="p3.2xlarge">p3.2xlarge</SelectItem>
                  <SelectItem value="c5.large">c5.large</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="vCpu">vCPU</Label>
                <Input
                  id="vCpu"
                  type="number"
                  min="1"
                  value={formData.vCpu}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      vCpu: Number(e.target.value),
                    }))
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="ramGB">RAM (GB)</Label>
                <Input
                  id="ramGB"
                  type="number"
                  min="1"
                  value={formData.ramGB}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      ramGB: Number(e.target.value),
                    }))
                  }
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="gpuCount">GPU Count</Label>
                <Input
                  id="gpuCount"
                  type="number"
                  min="0"
                  value={formData.gpuCount}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      gpuCount: Number(e.target.value),
                    }))
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="diskGB">Disk (GB)</Label>
                <Input
                  id="diskGB"
                  type="number"
                  min="10"
                  value={formData.diskGB}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      diskGB: Number(e.target.value),
                    }))
                  }
                />
              </div>
            </div>
          </div>

          {/* Limits & Budget */}
          <div className="space-y-4">
            <h4 className="font-medium">Limits & Budget</h4>

            <div className="space-y-2">
              <Label htmlFor="idleTimeout">Idle Timeout (minutes)</Label>
              <Input
                id="idleTimeout"
                type="number"
                min="5"
                value={formData.idleTimeoutMin}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    idleTimeoutMin: Number(e.target.value),
                  }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxRuntime">Max Runtime (hours)</Label>
              <Input
                id="maxRuntime"
                type="number"
                min="1"
                value={formData.maxRuntimeHrs}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    maxRuntimeHrs: Number(e.target.value),
                  }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="costBudget">Cost Budget (USD)</Label>
              <Input
                id="costBudget"
                type="number"
                min="0.1"
                step="0.1"
                value={formData.costBudgetUSD}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    costBudgetUSD: Number(e.target.value),
                  }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label>Tags</Label>
              <div className="flex gap-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add tag"
                  onKeyDown={(e) =>
                    e.key === "Enter" && (e.preventDefault(), addTag())
                  }
                />
                <Button type="button" onClick={addTag} size="sm">
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-1 mt-2">
                {formData.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="text-red-500 hover:text-red-700"
                    >
                      ×
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Allowed Packages */}
          <div className="col-span-2 space-y-4">
            <h4 className="font-medium">Allowed Packages</h4>
            <div className="grid grid-cols-3 gap-2">
              {availablePackages.map((pkg) => (
                <div key={pkg} className="flex items-center space-x-2">
                  <Checkbox
                    id={pkg}
                    checked={formData.allowedPackages.includes(pkg)}
                    onCheckedChange={() => togglePackage(pkg)}
                  />
                  <Label htmlFor={pkg} className="text-sm">
                    {pkg}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="bg-bits-blue hover:bg-bits-blue/90"
          >
            {profile ? "Update Profile" : "Create Profile"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default function AdminSettingsPage() {
  const [settings, setSettings] = useState<SystemSettings>(initialSettings);
  const [selectedTab, setSelectedTab] = useState("general");
  const [showApiKeys, setShowApiKeys] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);

  // Resource Profile state
  const [resourceProfiles, setResourceProfiles] =
    useState<ResourceProfile[]>(mockResourceProfiles);
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingProfile, setEditingProfile] = useState<ResourceProfile | null>(
    null
  );

  // Platform Quotas state
  const [platformQuotas, setPlatformQuotas] =
    useState<PlatformQuotas>(mockPlatformQuotas);

  const updateSetting = (
    section: keyof SystemSettings,
    key: string,
    value: any
  ) => {
    setSettings((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value,
      },
    }));
    setUnsavedChanges(true);
  };

  const saveSettings = () => {
    console.log("Saving settings:", settings);
    setUnsavedChanges(false);
    toast.success("Settings saved successfully!");
  };

  const resetToDefaults = () => {
    setSettings(initialSettings);
    setUnsavedChanges(true);
    toast.info("Settings reset to defaults");
  };

  const filteredProfiles = resourceProfiles.filter(
    (profile) =>
      profile.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      profile.instanceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      profile.tags.some((tag) =>
        tag.toLowerCase().includes(searchTerm.toLowerCase())
      )
  );

  const handleSaveProfile = (profile: ResourceProfile) => {
    if (profile.id && resourceProfiles.find((p) => p.id === profile.id)) {
      // Update existing profile
      setResourceProfiles((prev) =>
        prev.map((p) => (p.id === profile.id ? profile : p))
      );
    } else {
      // Create new profile
      setResourceProfiles((prev) => [
        ...prev,
        { ...profile, id: Date.now().toString() },
      ]);
    }
  };

  const toggleProfileStatus = (profileId: string) => {
    setResourceProfiles((prev) =>
      prev.map((p) => (p.id === profileId ? { ...p, active: !p.active } : p))
    );
  };

  const updateQuotas = () => {
    console.log("Updating quotas:", platformQuotas);
    toast.success("Platform quotas updated successfully!");
  };

  const getUsagePercentage = (current: number, max: number) => {
    return Math.min((current / max) * 100, 100);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-semibold">System Settings</h1>
          <p className="text-bits-grey-600 text-md">
            Configure platform settings and integrations
          </p>
        </div>
      </div>

      <Tabs
        value={selectedTab}
        onValueChange={setSelectedTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="aws">AWS Config</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-bits-blue" />
                <span className="text-bits-grey text-lg font-medium">
                  General Settings
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label
                    className="text-bits-grey-700 text-sm font-medium"
                    htmlFor="platform-name"
                  >
                    Platform Name
                  </Label>
                  <Input
                    id="platform-name"
                    className="bg-bits-grey-100 text-md text-bits-grey-500"
                    value={settings.general.platformName}
                    onChange={(e) =>
                      updateSetting("general", "platformName", e.target.value)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="support-email"
                    className="text-bits-grey-700 text-sm font-medium"
                  >
                    Support Email
                  </Label>
                  <Input
                    id="support-email"
                    type="email"
                    className="bg-bits-grey-100 text-md text-bits-grey-500"
                    value={settings.general.supportEmail}
                    onChange={(e) =>
                      updateSetting("general", "supportEmail", e.target.value)
                    }
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-md font-medium">
                    Maintenance Mode
                  </Label>
                  <p className="text-sm font-md text-bits-grey-600">
                    Enable to show maintenance message to users
                  </p>
                </div>
                <Switch
                  className="bg-bits-grey-100"
                  checked={settings.general.maintenanceMode}
                  onCheckedChange={(checked) =>
                    updateSetting("general", "maintenanceMode", checked)
                  }
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-bits-blue" />
                <span className="text-bits-grey text-lg font-medium">
                  Security Settings
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="password-length">
                    Minimum Password Length
                  </Label>
                  <Input
                    id="password-length"
                    type="number"
                    min="6"
                    max="20"
                    className="bg-bits-grey-100 text-md text-bits-grey-500"
                    value={settings.security.passwordMinLength}
                    onChange={(e) =>
                      updateSetting(
                        "security",
                        "passwordMinLength",
                        parseInt(e.target.value)
                      )
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="session-timeout"
                    className="text-sm font-medium text-bits-grey-700"
                  >
                    Session Timeout (minutes)
                  </Label>
                  <Input
                    id="session-timeout"
                    type="number"
                    min="15"
                    max="480"
                    className="bg-bits-grey-100 text-md text-bits-grey-500"
                    value={settings.security.sessionTimeout}
                    onChange={(e) =>
                      updateSetting(
                        "security",
                        "sessionTimeout",
                        parseInt(e.target.value)
                      )
                    }
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-md font-medium">
                    Two-Factor Authentication
                  </Label>
                  <p className="text-sm text-bits-grey-600">
                    Require 2FA for all users
                  </p>
                </div>
                <Switch
                  checked={settings.security.twoFactorRequired}
                  onCheckedChange={(checked) =>
                    updateSetting("security", "twoFactorRequired", checked)
                  }
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="aws" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-bits-blue" />
                <span className="text-lg font-medium text-bits-grey">
                  AWS Configuration
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="aws-region"
                    className="text-sm font-medium text-bits-grey-700"
                  >
                    AWS Region
                  </Label>
                  <Select
                    value={settings.aws.region}
                    onValueChange={(value) =>
                      updateSetting("aws", "region", value)
                    }
                  >
                    <SelectTrigger className="bg-bits-grey-100 text-md text-bits-grey-500">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ap-south-1">
                        Asia Pacific (Mumbai)
                      </SelectItem>
                      <SelectItem value="us-east-1">
                        US East (N. Virginia)
                      </SelectItem>
                      <SelectItem value="us-west-2">
                        US West (Oregon)
                      </SelectItem>
                      <SelectItem value="eu-west-1">
                        Europe (Ireland)
                      </SelectItem>
                      <SelectItem value="ap-southeast-1">
                        Asia Pacific (Singapore)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="instance-type"
                    className="text-sm font-medium text-bits-grey-700"
                  >
                    Default Instance Type
                  </Label>
                  <Select
                    value={settings.aws.instanceType}
                    onValueChange={(value) =>
                      updateSetting("aws", "instanceType", value)
                    }
                  >
                    <SelectTrigger className="bg-bits-grey-100 text-md text-bits-grey-500">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="t3.micro">
                        t3.micro (1 vCPU, 1 GB RAM)
                      </SelectItem>
                      <SelectItem value="t3.small">
                        t3.small (2 vCPU, 2 GB RAM)
                      </SelectItem>
                      <SelectItem value="t3.medium">
                        t3.medium (2 vCPU, 4 GB RAM)
                      </SelectItem>
                      <SelectItem value="t3.large">
                        t3.large (2 vCPU, 8 GB RAM)
                      </SelectItem>
                      <SelectItem value="m5.large">
                        m5.large (2 vCPU, 8 GB RAM)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-md font-medium">Auto Scaling</Label>
                  <p className="text-sm text-bits-grey-600">
                    Automatically scale resources based on demand
                  </p>
                </div>
                <Switch
                  checked={settings.aws.autoScaling}
                  onCheckedChange={(checked) =>
                    updateSetting("aws", "autoScaling", checked)
                  }
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bell className="h-5 w-5 text-bits-blue" />
                <span className="text-lg font-medium text-bits-grey">
                  Notification Settings
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-md font-medium">
                      Email Notifications
                    </Label>
                    <p className="text-sm text-bits-grey-600">
                      Send email notifications to users
                    </p>
                  </div>
                  <Switch
                    checked={settings.notifications.emailEnabled}
                    onCheckedChange={(checked) =>
                      updateSetting("notifications", "emailEnabled", checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-md font-medium">
                      Push Notifications
                    </Label>
                    <p className="text-sm text-bits-grey-600">
                      Browser push notifications
                    </p>
                  </div>
                  <Switch
                    checked={settings.notifications.pushEnabled}
                    onCheckedChange={(checked) =>
                      updateSetting("notifications", "pushEnabled", checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-md font-medium">Admin Alerts</Label>
                    <p className="text-sm text-bits-grey-600">
                      System alerts for administrators
                    </p>
                  </div>
                  <Switch
                    checked={settings.notifications.adminAlerts}
                    onCheckedChange={(checked) =>
                      updateSetting("notifications", "adminAlerts", checked)
                    }
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      <div className="flex items-center space-x-2 text-bits-grey">
        {unsavedChanges && <Badge variant="destructive">Unsaved Changes</Badge>}
        <Button variant="outline" onClick={resetToDefaults}>
          <RefreshCw className="h-4 w-4 mr-1" />
          <span className="text-sm font-semibold">Reset</span>
        </Button>
        <Button
          onClick={saveSettings}
          className="bg-bits-blue hover:bg-bits-blue/90 text-white"
        >
          <Save className="h-4 w-4 mr-1" />
          <span className="text-sm font-semibold">Save Settings</span>
        </Button>
      </div>
    </div>
  );
}
