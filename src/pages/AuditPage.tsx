import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import {
  Search,
  Download,
  AlertTriangle,
  Shield,
  Eye,
  Calendar,
  Clock,
  User,
  Settings,
  FileText,
  LogIn,
  LogOut,
  Edit,
  Trash2,
  Upload,
  Database,
  Key,
  CheckCircle,
  XCircle,
  Info,
  Activity,
  File,
} from "lucide-react";
//import { useNavigation } from "../App";
import useNavigation from "../components/Context/NavigationContext";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTit<PERSON>,
  DialogTrigger,
} from "../components/ui/dialog";
import { Separator } from "../components/ui/separator";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "../components/ui/pagination";

// Types
interface AuditEvent {
  id: string;
  timestamp: string;
  userId: string;
  userName: string;
  userEmail: string;
  userRole: "admin" | "instructor" | "student";
  action: string;
  category:
    | "authentication"
    | "user_management"
    | "academic"
    | "system"
    | "security"
    | "data";
  description: string;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  severity: "low" | "medium" | "high" | "critical";
  metadata?: any;
}

interface SecurityAlert {
  id: string;
  timestamp: string;
  type:
    | "suspicious_login"
    | "failed_authentication"
    | "privilege_escalation"
    | "data_access_violation"
    | "system_intrusion";
  severity: "low" | "medium" | "high" | "critical";
  title: string;
  description: string;
  source: string;
  status: "active" | "investigating" | "resolved" | "false_positive";
  assignedTo?: string;
  relatedEvents: string[];
}

interface AuditStats {
  totalEvents: number;
  securityEvents: number;
  failedEvents: number;
  criticalEvents: number;
  activeAlerts: number;
}

// Mock data
const mockAuditEvents: AuditEvent[] = [
  {
    id: "1",
    timestamp: "2025-01-12T14:30:00Z",
    userId: "admin_1",
    userName: "Admin User",
    userEmail: "<EMAIL>",
    userRole: "admin",
    action: "User Created",
    category: "user_management",
    description: "Created new instructor account for Dr. Smith",
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    success: true,
    severity: "low",
    metadata: { createdUserId: "instructor_15", email: "<EMAIL>" },
  },
  {
    id: "2",
    timestamp: "2025-01-12T14:25:00Z",
    userId: "student_123",
    userName: "Rahul Sharma",
    userEmail: "<EMAIL>",
    userRole: "student",
    action: "Project Submission",
    category: "academic",
    description: "Submitted final project for Data Science 101",
    ipAddress: "*********",
    userAgent:
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15",
    success: true,
    severity: "low",
    metadata: { projectId: "ds101_final", fileSize: "2.3MB" },
  },
  {
    id: "3",
    timestamp: "2025-01-12T14:20:00Z",
    userId: "unknown",
    userName: "Unknown User",
    userEmail: "<EMAIL>",
    userRole: "student",
    action: "Failed Login",
    category: "authentication",
    description: "Failed login attempt with invalid credentials",
    ipAddress: "*************",
    userAgent: "curl/7.68.0",
    success: false,
    severity: "medium",
    metadata: { attemptedEmail: "<EMAIL>", reason: "invalid_password" },
  },
  {
    id: "4",
    timestamp: "2025-01-12T14:15:00Z",
    userId: "instructor_5",
    userName: "Dr. A. Sharma",
    userEmail: "<EMAIL>",
    userRole: "instructor",
    action: "Grade Assignment",
    category: "academic",
    description: "Graded assignment for student submission",
    ipAddress: "************",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    success: true,
    severity: "low",
    metadata: {
      assignmentId: "ml_basics_hw1",
      studentId: "student_456",
      grade: "A-",
    },
  },
  {
    id: "5",
    timestamp: "2025-01-12T14:10:00Z",
    userId: "system",
    userName: "System",
    userEmail: "<EMAIL>",
    userRole: "admin",
    action: "Database Backup",
    category: "system",
    description: "Automated daily database backup completed",
    ipAddress: "127.0.0.1",
    userAgent: "System/1.0",
    success: true,
    severity: "low",
    metadata: { backupSize: "450MB", duration: "3.2s" },
  },
  {
    id: "6",
    timestamp: "2025-01-12T14:05:00Z",
    userId: "admin_1",
    userName: "Admin User",
    userEmail: "<EMAIL>",
    userRole: "admin",
    action: "Security Settings Modified",
    category: "security",
    description: "Updated password policy requirements",
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    success: true,
    severity: "high",
    metadata: { changes: ["min_length: 8->12", "special_chars: required"] },
  },
  {
    id: "7",
    timestamp: "2025-01-12T14:00:00Z",
    userId: "student_789",
    userName: "Priya Patel",
    userEmail: "<EMAIL>",
    userRole: "student",
    action: "Data Export",
    category: "data",
    description: "Exported personal academic data",
    ipAddress: "*********",
    userAgent:
      "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15",
    success: true,
    severity: "medium",
    metadata: { exportType: "academic_transcript", format: "PDF" },
  },
  {
    id: "8",
    timestamp: "2025-01-12T13:55:00Z",
    userId: "unknown",
    userName: "Unknown User",
    userEmail: "<EMAIL>",
    userRole: "student",
    action: "Unauthorized Access Attempt",
    category: "security",
    description: "Attempted to access admin panel without proper authorization",
    ipAddress: "**************",
    userAgent: "python-requests/2.25.1",
    success: false,
    severity: "critical",
    metadata: { blockedUrl: "/admin/users", attempt_count: 5 },
  },
];

const auditStats: AuditStats = {
  totalEvents: 1247,
  securityEvents: 23,
  failedEvents: 15,
  criticalEvents: 3,
  activeAlerts: 2,
};

// Helper functions
const getCategoryIcon = (category: string) => {
  switch (category) {
    case "authentication":
      return <LogIn className="h-4 w-4 text-bits-blue" />;
    case "user_management":
      return <File className="h-4 w-4 text-bits-indigo-600" />;
    case "academic":
      return <FileText className="h-4 w-4 text-bits-gold" />;
    case "system":
      return <Settings className="h-4 w-4 text-muted-foreground" />;
    case "security":
      return <Shield className="h-4 w-4 text-bits-red" />;
    case "data":
      return <Database className="h-4 w-4 text-bits-gold" />;
    default:
      return <Activity className="h-4 w-4 text-muted-foreground" />;
  }
};

const getSeverityColor = (severity: string) => {
  switch (severity) {
    case "critical":
      return "bg-bits-red/10 text-bits-red";
    case "high":
      return "bg-bits-gold/10 text-bits-gold";
    case "medium":
      return "bg-bits-gold/10 text-bits-gold";
    case "low":
      return "bg-bits-completed text-bits-green";
    default:
      return "bg-muted text-muted-foreground";
  }
};

// Audit Event Detail Dialog
interface AuditEventDialogProps {
  event: AuditEvent;
}

function AuditEventDialog({ event }: AuditEventDialogProps) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          <Eye className="h-4 w-4 text-bits-grey" />
          <span className="text-sm font-semibold text-bits-grey-700">
            View Details
          </span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl bg-white border">
        <DialogHeader>
          <DialogTitle>Audit Event Details</DialogTitle>
          <DialogDescription>
            Detailed information about this audit event
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">Event ID</Label>
              <p className="text-sm text-muted-foreground font-mono">
                {event.id}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Timestamp</Label>
              <p className="text-sm text-muted-foreground">
                {new Date(event.timestamp).toLocaleString()}
              </p>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">User</Label>
              <p className="text-sm text-muted-foreground">{event.userName}</p>
              <p className="text-xs text-muted-foreground">
                Role: {event.userRole}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Action</Label>
              <p className="text-sm text-muted-foreground">{event.action}</p>
            </div>
          </div>

          <div>
            <Label className="text-sm font-medium">Description</Label>
            <p className="text-sm text-muted-foreground">{event.description}</p>
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">IP Address</Label>
              <p className="text-sm text-muted-foreground font-mono">
                {event.ipAddress}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Category</Label>
              <div className="flex items-center space-x-2">
                {getCategoryIcon(event.category)}
                <span className="text-sm text-muted-foreground capitalize">
                  {event.category.replace("_", " ")}
                </span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">Severity</Label>
              <Badge className={getSeverityColor(event.severity)}>
                {event.severity}
              </Badge>
            </div>
            <div>
              <Label className="text-sm font-medium">Status</Label>
              <div className="flex items-center space-x-2">
                {event.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <span className="text-sm text-muted-foreground">
                  {event.success ? "Success" : "Failed"}
                </span>
              </div>
            </div>
          </div>

          <div>
            <Label className="text-sm font-medium">User Agent</Label>
            <p className="text-xs text-muted-foreground font-mono break-all">
              {event.userAgent}
            </p>
          </div>

          {event.metadata && (
            <>
              <Separator />
              <div>
                <Label className="text-sm font-medium">
                  Additional Metadata
                </Label>
                <pre className="text-xs text-muted-foreground bg-gray-50 p-2 rounded mt-1 overflow-x-auto">
                  {JSON.stringify(event.metadata, null, 2)}
                </pre>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default function AuditPage() {
  const { goBack } = useNavigation();
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [severityFilter, setSeverityFilter] = useState("all");
  const [userFilter, setUserFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("Today");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Filter events based on search and filters
  const filteredEvents = mockAuditEvents.filter((event) => {
    const matchesSearch =
      searchTerm === "" ||
      event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.action.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory =
      categoryFilter === "all" || event.category === categoryFilter;
    const matchesSeverity =
      severityFilter === "all" || event.severity === severityFilter;
    const matchesUser = userFilter === "all" || event.userRole === userFilter;

    return matchesSearch && matchesCategory && matchesSeverity && matchesUser;
  });

  // Pagination
  const totalPages = Math.ceil(filteredEvents.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentEvents = filteredEvents.slice(startIndex, endIndex);

  // Reset pagination when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, categoryFilter, severityFilter, userFilter]);

  const renderPagination = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(
          <PaginationItem key={i}>
            <PaginationLink
              onClick={() => setCurrentPage(i)}
              isActive={currentPage === i}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }
    } else {
      // Always show first page
      pages.push(
        <PaginationItem key={1}>
          <PaginationLink
            onClick={() => setCurrentPage(1)}
            isActive={currentPage === 1}
          >
            1
          </PaginationLink>
        </PaginationItem>
      );

      // Add ellipsis if needed
      if (currentPage > 3) {
        pages.push(<PaginationEllipsis key="ellipsis1" />);
      }

      // Add pages around current page
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);

      for (let i = start; i <= end; i++) {
        pages.push(
          <PaginationItem key={i}>
            <PaginationLink
              onClick={() => setCurrentPage(i)}
              isActive={currentPage === i}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }

      // Add ellipsis if needed
      if (currentPage < totalPages - 2) {
        pages.push(<PaginationEllipsis key="ellipsis2" />);
      }

      // Always show last page
      if (totalPages > 1) {
        pages.push(
          <PaginationItem key={totalPages}>
            <PaginationLink
              onClick={() => setCurrentPage(totalPages)}
              isActive={currentPage === totalPages}
            >
              {totalPages}
            </PaginationLink>
          </PaginationItem>
        );
      }
    }

    return (
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              className={
                currentPage === 1 ? "pointer-events-none opacity-50" : ""
              }
            />
          </PaginationItem>
          {pages}
          <PaginationItem>
            <PaginationNext
              onClick={() =>
                setCurrentPage(Math.min(totalPages, currentPage + 1))
              }
              className={
                currentPage === totalPages
                  ? "pointer-events-none opacity-50"
                  : ""
              }
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    );
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-semibold">Audit & Security</h1>
          <p className="text-bits-grey-600 text-md font-normal">
            Monitor system activities and security events
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2 text-bits-grey" />
            <span className="text-sm font-semibold text-bits-grey">Export</span>
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div>
                <div className="text-3xl font-bold text-bits-blue-neutral-900">
                  {auditStats.totalEvents}
                </div>
                <p className="text-sm font-normal text-bits-grey-600">
                  Total Events
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div>
                <div className="text-3xl font-bold text-bits-blue-neutral-900">
                  {auditStats.securityEvents}
                </div>
                <p className="text-sm font-normal text-bits-grey-600">
                  Security Events
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div>
                <div className="text-3xl font-bold text-bits-blue-neutral-900">
                  {auditStats.failedEvents}
                </div>
                <p className="text-sm font-normal text-bits-grey-600">
                  Failed Events
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div>
                <div className="text-3xl font-bold text-bits-blue-neutral-900">
                  {auditStats.criticalEvents}
                </div>
                <p className="text-sm font-normal text-bits-grey-600">
                  Critical Events
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div>
                <div className="text-3xl font-bold text-bits-blue-neutral-900">
                  {auditStats.activeAlerts}
                </div>
                <p className="text-sm font-normal text-bits-grey-600">
                  Active Alerts
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Audit Events with Integrated Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <div className="flex flex-row gap-20 items-start">
              <span className="text-lg font-medium text-bits-grey">
                Audit Events
              </span>
              <div className="flex flex-col justify-end">
                <div className="flex flex-row justify-end gap-2">
                  <div className="w-60 text-bits-grey-600">
                    <Select
                      value={categoryFilter}
                      onValueChange={setCategoryFilter}
                    >
                      <SelectTrigger className="mt-1 bg-bits-grey-100">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        <SelectItem value="authentication">
                          Authentication
                        </SelectItem>
                        <SelectItem value="user_management">
                          User Management
                        </SelectItem>
                        <SelectItem value="academic">Academic</SelectItem>
                        <SelectItem value="system">System</SelectItem>
                        <SelectItem value="security">Security</SelectItem>
                        <SelectItem value="data">Data</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="w-60 text-bits-grey-600">
                    <Select value={userFilter} onValueChange={setUserFilter}>
                      <SelectTrigger className="mt-1 bg-bits-grey-100">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Users</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="instructor">Instructor</SelectItem>
                        <SelectItem value="student">Student</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="w-60 text-bits-grey-600">
                    <Select
                      value={severityFilter}
                      onValueChange={setSeverityFilter}
                    >
                      <SelectTrigger className="mt-1 bg-bits-grey-100">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Severities</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="w-20 text-bits-grey-600">
                    <Select
                      value={severityFilter}
                      onValueChange={setSeverityFilter}
                    >
                      <SelectTrigger className="mt-1 bg-bits-grey-100">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Today</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex justify-end mt-3">
                  <div className="md:col-span-2 w-80">
                    <div className="relative mt-1">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search by description, user, or action..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 bg-bits-grey-100 text-md text-bits-grey-600"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search and Filters Section */}

          {/* Events Table */}
          {/* <Table> */}
          <div className="overflow-x-auto">
            <TableHeader>
              <TableRow className="text-sm font-medium">
                <TableHead className="text-bits-grey">Timestamp</TableHead>
                <TableHead className="text-bits-grey">User</TableHead>
                <TableHead className="text-bits-grey">Action</TableHead>
                <TableHead className="text-bits-grey">Category</TableHead>
                <TableHead className="text-bits-grey">Severity</TableHead>
                <TableHead className="text-bits-grey">Status</TableHead>
                <TableHead className="text-bits-grey">IP Address</TableHead>
                <TableHead className="text-bits-grey">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentEvents.map((event) => (
                <TableRow key={event.id}>
                  <TableCell className="font-normal text-sm text-bits-grey-600">
                    {new Date(event.timestamp).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="text-md font-medium text-bits-grey">
                        {event.userName}
                      </div>
                      <div className="text-sm font-normal text-bits-grey-600">
                        {event.userEmail}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="text-md font-medium text-bits-grey">
                        {event.action}
                      </div>
                      <div className="text-sm font-normal text-bits-grey-600">
                        {event.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getCategoryIcon(event.category)}
                      <span className="capitalize text-sm font-normal text-bits-grey-600">
                        {event.category.replace("_", " ")}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getSeverityColor(event.severity)}>
                      {event.severity}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {event.success ? (
                        <CheckCircle className="h-4 w-4 text-bits-green" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <span className="text-sm text-bits-grey-600">
                        {event.success ? "Success" : "Failed"}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-bits-grey-600">
                        {event.ipAddress}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <AuditEventDialog event={event} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </div>
          {/* </Table> */}

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Label className="text-sm">Items per page:</Label>
              <Select
                value={itemsPerPage.toString()}
                onValueChange={(value) => {
                  setItemsPerPage(Number(value));
                  setCurrentPage(1);
                }}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="text-sm text-muted-foreground">
              Showing {startIndex + 1} to{" "}
              {Math.min(endIndex, filteredEvents.length)} of{" "}
              {filteredEvents.length} events
            </div>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center pt-4">{renderPagination()}</div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
