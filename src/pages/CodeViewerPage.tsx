import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import {
  CustomTabs,
  CustomTabsList,
  CustomTabsTrigger,
  CustomTabsContent,
} from "../components/ui/custom-tabs";
import { ScrollArea } from "../components/ui/scroll-area";
import { Separator } from "../components/ui/separator";
import {
  ArrowLeft,
  Download,
  FileText,
  Code,
  User,
  Calendar,
  Eye,
  ExternalLink,
  RefreshCw,
  Copy,
  Play,
  Terminal,
  FolderOpen,
  Database,
  BarChart3,
  Search,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
//import { useNavigation } from "../App";
import useNavigation from "../components/Context/NavigationContext";
import { formatDistanceToNow } from "../helper/date-fns";
import { toast } from "sonner";

interface CodeViewerPageProps {
  studentId?: string;
  studentName?: string;
  assignmentId?: string;
  assignmentTitle?: string;
  submissionId?: string;
}

// Mock code files data
const mockCodeFiles = [
  {
    id: "notebook1",
    name: "mall_customers_analysis.ipynb",
    type: "jupyter",
    language: "python",
    size: "2.4 KB",
    lastModified: new Date("2024-12-01T10:30:00"),
    content: `{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Mall Customers Segmentation Analysis\\n",
    "\\n",
    "This notebook analyzes customer data from a shopping mall to identify different customer segments using clustering algorithms."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 1,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import necessary libraries\\n",
    "import pandas as pd\\n",
    "import numpy as np\\n",
    "import matplotlib.pyplot as plt\\n",
    "import seaborn as sns\\n",
    "from sklearn.cluster import KMeans\\n",
    "from sklearn.preprocessing import StandardScaler\\n",
    "\\n",
    "# Load the dataset\\n",
    "df = pd.read_csv('mall_customers.csv')\\n",
    "print(f'Dataset shape: {df.shape}')\\n",
    "df.head()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 2,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Data exploration\\n",
    "print('Dataset Info:')\\n",
    "print(df.info())\\n",
    "print('\\nMissing values:')\\n",
    "print(df.isnull().sum())\\n",
    "print('\\nStatistical summary:')\\n",
    "print(df.describe())"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 3,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Feature engineering and clustering\\n",
    "# Select features for clustering\\n",
    "features = ['Annual Income (k$)', 'Spending Score (1-100)']\\n",
    "X = df[features]\\n",
    "\\n",
    "# Standardize the features\\n",
    "scaler = StandardScaler()\\n",
    "X_scaled = scaler.fit_transform(X)\\n",
    "\\n",
    "# Apply K-means clustering\\n",
    "kmeans = KMeans(n_clusters=5, random_state=42)\\n",
    "clusters = kmeans.fit_predict(X_scaled)\\n",
    "\\n",
    "# Add cluster labels to dataframe\\n",
    "df['Cluster'] = clusters\\n",
    "\\n",
    "print(f'Cluster distribution:')\\n",
    "print(df['Cluster'].value_counts().sort_index())"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}`,
    outputs: [
      {
        type: "text",
        content:
          "Dataset shape: (200, 5)\nDataset Info:\n<class 'pandas.core.frame.DataFrame'>\nRangeIndex: 200 entries, 0 to 199\nData columns (total 5 columns):\n # Column              Non-Null Count  Dtype\n--- ------              --------------  -----\n 0   CustomerID          200 non-null    int64\n 1   Gender             200 non-null    object\n 2   Age                200 non-null    int64\n 3   Annual Income (k$)  200 non-null    int64\n 4   Spending Score      200 non-null    int64\ndtypes: int64(4), object(1)\nmemory usage: 7.9+ KB",
      },
      {
        type: "chart",
        content:
          "Cluster distribution plot showing 5 distinct customer segments",
      },
    ],
  },
  {
    id: "script1",
    name: "data_preprocessing.py",
    type: "python",
    language: "python",
    size: "1.8 KB",
    lastModified: new Date("2024-12-01T09:15:00"),
    content: `import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder

def load_and_clean_data(file_path):
    """
    Load and clean the mall customers dataset
    """
    df = pd.read_csv(file_path)
    
    # Remove any duplicate rows
    df = df.drop_duplicates()
    
    # Handle missing values if any
    df = df.dropna()
    
    # Encode categorical variables
    le = LabelEncoder()
    df['Gender_encoded'] = le.fit_transform(df['Gender'])
    
    return df

def normalize_features(df, features):
    """
    Normalize numerical features for clustering
    """
    scaler = StandardScaler()
    normalized_features = scaler.fit_transform(df[features])
    
    return normalized_features, scaler

def calculate_customer_metrics(df):
    """
    Calculate additional customer metrics
    """
    df['Income_to_Spending_Ratio'] = df['Annual Income (k$)'] / df['Spending Score (1-100)']
    df['Age_Group'] = pd.cut(df['Age'], bins=[0, 30, 50, 100], labels=['Young', 'Middle', 'Senior'])
    
    return df

if __name__ == "__main__":
    # Load and process data
    data = load_and_clean_data('mall_customers.csv')
    processed_data = calculate_customer_metrics(data)
    
    print("Data preprocessing completed successfully!")
    print(f"Final dataset shape: {processed_data.shape}")
`,
  },
  {
    id: "config1",
    name: "requirements.txt",
    type: "text",
    language: "text",
    size: "0.3 KB",
    lastModified: new Date("2024-11-30T14:20:00"),
    content: `pandas==1.3.3
numpy==1.21.2
matplotlib==3.4.3
seaborn==0.11.2
scikit-learn==1.0.2
jupyter==1.0.0
plotly==5.3.1
`,
  },
];

export default function CodeViewerPage({
  studentId,
  studentName,
  assignmentId,
  assignmentTitle,
  submissionId,
}: CodeViewerPageProps) {
  const { goBack } = useNavigation();
  const [activeFile, setActiveFile] = useState(mockCodeFiles[0]);
  const [activeTab, setActiveTab] = useState("code");
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Simulate loading student's code
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, [submissionId]);

  const handleCopyCode = () => {
    navigator.clipboard.writeText(activeFile.content);
    toast.success("Code copied to clipboard");
  };

  const handleDownloadFile = () => {
    const blob = new Blob([activeFile.content], { type: "text/plain" });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = activeFile.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    toast.success("File downloaded successfully");
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case "jupyter":
        return <FileText className="h-4 w-4 text-orange-500" />;
      case "python":
        return <Code className="h-4 w-4 text-blue-500" />;
      case "text":
        return <FileText className="h-4 w-4 text-gray-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const renderNotebookContent = (content: string) => {
    try {
      const notebook = JSON.parse(content);
      return (
        <div className="space-y-4">
          {notebook.cells?.map((cell: any, index: number) => (
            <div key={index} className="border rounded-lg overflow-hidden">
              {cell.cell_type === "markdown" && (
                <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
                  <div className="flex items-center mb-2">
                    <FileText className="h-4 w-4 text-blue-600 mr-2" />
                    <span className="text-sm font-medium text-blue-800">
                      Markdown Cell
                    </span>
                  </div>
                  <div className="prose text-sm">
                    {cell.source?.join("").replace(/\\n/g, "\n")}
                  </div>
                </div>
              )}

              {cell.cell_type === "code" && (
                <div className="bg-gray-50">
                  <div className="flex items-center justify-between p-3 bg-gray-100 border-b">
                    <div className="flex items-center">
                      <Terminal className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-sm font-medium text-gray-700">
                        Code Cell [{cell.execution_count || " "}]
                      </span>
                    </div>
                    <Button variant="ghost" size="sm" onClick={handleCopyCode}>
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                  <ScrollArea className="max-h-96">
                    <pre className="p-4 text-sm font-mono text-gray-800 overflow-x-auto">
                      <code>{cell.source?.join("")}</code>
                    </pre>
                  </ScrollArea>

                  {/* Mock outputs */}
                  {cell.execution_count && (
                    <div className="border-t bg-white">
                      <div className="p-3 border-b bg-gray-50">
                        <span className="text-xs font-medium text-gray-600">
                          Output:
                        </span>
                      </div>
                      <div className="p-4">
                        <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                          {activeFile.outputs?.[index - 1]?.content ||
                            "Code executed successfully"}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      );
    } catch (error) {
      return (
        <div className="text-center py-8">
          <FileText className="h-12 w-12 text-gray-300 mx-auto mb-3" />
          <p className="text-gray-500">Unable to parse notebook content</p>
        </div>
      );
    }
  };

  const renderCodeContent = (content: string, language: string) => {
    return (
      <div className="border rounded-lg overflow-hidden">
        <div className="flex items-center justify-between p-3 bg-gray-100 border-b">
          <div className="flex items-center">
            <Code className="h-4 w-4 text-blue-600 mr-2" />
            <span className="text-sm font-medium text-gray-700">
              {activeFile.name}
            </span>
            <Badge variant="outline" className="ml-2">
              {language}
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" onClick={handleCopyCode}>
              <Copy className="h-3 w-3 mr-1" />
              Copy
            </Button>
            <Button variant="ghost" size="sm" onClick={handleDownloadFile}>
              <Download className="h-3 w-3 mr-1" />
              Download
            </Button>
          </div>
        </div>
        <div className="max-h-[500px] overflow-auto">
          <pre className="p-4 text-sm font-mono text-gray-800 overflow-x-auto">
            <code>{content}</code>
          </pre>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
          <p className="text-gray-500">Loading student code...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={goBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Submission
            </Button>

            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                Code Review
              </h1>
              <p className="text-sm text-gray-500">
                {studentName} • {assignmentTitle}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={handleDownloadFile}>
              <Download className="h-4 w-4 mr-2" />
              Download All
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex min-h-0">
        {/* File Explorer Sidebar */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col flex-shrink-0">
          <div className="p-4 border-b border-gray-200 flex-shrink-0">
            <h3 className="font-medium text-gray-900 mb-2">Submitted Files</h3>
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>{mockCodeFiles.length} files</span>
              <span>2.5 KB total</span>
            </div>
          </div>

          <ScrollArea className="flex-1">
            <div className="p-2">
              {mockCodeFiles.map((file) => (
                <div
                  key={file.id}
                  onClick={() => setActiveFile(file)}
                  className={`
                    flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors
                    ${
                      activeFile.id === file.id
                        ? "bg-blue-50 border border-blue-200"
                        : "hover:bg-gray-50"
                    }
                  `}
                >
                  {getFileIcon(file.type)}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {file.name}
                    </p>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <span>{file.size}</span>
                      <span>•</span>
                      <span>
                        {formatDistanceToNow(file.lastModified, {
                          addSuffix: true,
                        })}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>

        {/* Code Viewer */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* File Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-3 flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getFileIcon(activeFile.type)}
                <div>
                  <h2 className="font-medium text-gray-900">
                    {activeFile.name}
                  </h2>
                  <p className="text-sm text-gray-500">
                    {activeFile.size} • Modified{" "}
                    {formatDistanceToNow(activeFile.lastModified, {
                      addSuffix: true,
                    })}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Badge variant="outline">{activeFile.language}</Badge>
                <Button variant="ghost" size="sm" onClick={handleCopyCode}>
                  <Copy className="h-4 w-4 mr-1" />
                  Copy
                </Button>
                <Button variant="ghost" size="sm" onClick={handleDownloadFile}>
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
              </div>
            </div>
          </div>

          {/* Content Tabs and Content */}
          <div className="flex-1 flex flex-col bg-white min-h-0">
            <CustomTabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="flex-1 flex flex-col min-h-0"
            >
              {/* Tabs List */}
              <div className="px-6 pt-6 flex-shrink-0">
                <CustomTabsList>
                  <CustomTabsTrigger value="code">Code</CustomTabsTrigger>
                  <CustomTabsTrigger value="preview">Preview</CustomTabsTrigger>
                  <CustomTabsTrigger value="analysis">
                    Analysis
                  </CustomTabsTrigger>
                </CustomTabsList>
              </div>

              {/* Content Area */}
              <div className="flex-1 overflow-auto min-h-0 px-6 pb-6">
                <CustomTabsContent value="code" className="h-full">
                  {activeFile.type === "jupyter"
                    ? renderNotebookContent(activeFile.content)
                    : renderCodeContent(
                        activeFile.content,
                        activeFile.language
                      )}
                </CustomTabsContent>

                <CustomTabsContent value="preview" className="h-full">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Eye className="h-5 w-5 mr-2" />
                        Code Execution Preview
                      </CardTitle>
                      <CardDescription>
                        Simulated output and visualizations from the student's
                        code
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <h4 className="font-medium mb-2">Dataset Summary</h4>
                          <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                            Dataset shape: (200, 5) Features: CustomerID,
                            Gender, Age, Annual Income (k$), Spending Score
                            (1-100) Missing values: 0
                          </pre>
                        </div>

                        <div className="p-4 bg-blue-50 rounded-lg">
                          <h4 className="font-medium mb-2">
                            Clustering Results
                          </h4>
                          <p className="text-sm text-gray-700">
                            Successfully identified 5 distinct customer segments
                            using K-means clustering. Clusters distributed as:
                            [40, 39, 35, 43, 43] customers respectively.
                          </p>
                        </div>

                        <div className="p-4 bg-green-50 rounded-lg">
                          <h4 className="font-medium mb-2">
                            Model Performance
                          </h4>
                          <p className="text-sm text-gray-700">
                            Silhouette Score: 0.73 (Good clustering quality)
                            Inertia: 89.2 (Optimal number of clusters confirmed)
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </CustomTabsContent>

                <CustomTabsContent value="analysis" className="h-full">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <BarChart3 className="h-5 w-5 mr-2" />
                        Code Analysis
                      </CardTitle>
                      <CardDescription>
                        Automated analysis of the student's code quality and
                        approach
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div>
                          <h4 className="font-medium mb-3">
                            Code Quality Metrics
                          </h4>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="p-3 border rounded-lg">
                              <p className="text-sm text-gray-600">
                                Lines of Code
                              </p>
                              <p className="text-2xl font-semibold text-green-600">
                                87
                              </p>
                            </div>
                            <div className="p-3 border rounded-lg">
                              <p className="text-sm text-gray-600">
                                Documentation Coverage
                              </p>
                              <p className="text-2xl font-semibold text-blue-600">
                                85%
                              </p>
                            </div>
                            <div className="p-3 border rounded-lg">
                              <p className="text-sm text-gray-600">
                                Code Complexity
                              </p>
                              <p className="text-2xl font-semibold text-yellow-600">
                                Medium
                              </p>
                            </div>
                            <div className="p-3 border rounded-lg">
                              <p className="text-sm text-gray-600">
                                Best Practices
                              </p>
                              <p className="text-2xl font-semibold text-green-600">
                                Good
                              </p>
                            </div>
                          </div>
                        </div>

                        <Separator />

                        <div>
                          <h4 className="font-medium mb-3">Strengths</h4>
                          <ul className="space-y-2">
                            <li className="flex items-start">
                              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                              <span className="text-sm">
                                Proper data preprocessing and cleaning
                              </span>
                            </li>
                            <li className="flex items-start">
                              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                              <span className="text-sm">
                                Appropriate use of StandardScaler for
                                normalization
                              </span>
                            </li>
                            <li className="flex items-start">
                              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                              <span className="text-sm">
                                Clear documentation and comments
                              </span>
                            </li>
                            <li className="flex items-start">
                              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                              <span className="text-sm">
                                Modular code structure with separate functions
                              </span>
                            </li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium mb-3">
                            Areas for Improvement
                          </h4>
                          <ul className="space-y-2">
                            <li className="flex items-start">
                              <AlertCircle className="h-4 w-4 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" />
                              <span className="text-sm">
                                Could add validation for optimal number of
                                clusters
                              </span>
                            </li>
                            <li className="flex items-start">
                              <AlertCircle className="h-4 w-4 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" />
                              <span className="text-sm">
                                Missing error handling for file operations
                              </span>
                            </li>
                            <li className="flex items-start">
                              <AlertCircle className="h-4 w-4 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" />
                              <span className="text-sm">
                                Could include cross-validation for clustering
                                stability
                              </span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </CustomTabsContent>
              </div>
            </CustomTabs>
          </div>
        </div>
      </div>
    </div>
  );
}
