import React, { useState, useEffect, useMemo } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
  CardDescription,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import {
  ArrowLeft,
  Search,
  Plus,
  Pin,
  Reply,
  Send,
  Paperclip,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  Megaphone,
  MessageSquare,
  X,
} from "lucide-react";
 import { useAuth, useNavigation } from "../App";
//import useAuth from "../components/Context/AuthContext";
//import useNavigation from "../components/Context/NavigationContext";
import { Textarea } from "../components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import { Separator } from "../components/ui/separator";
import { cn } from "../lib/utils";

interface Announcement {
  id: string;
  title: string;
  content: string;
  authorId: string;
  authorName: string;
  authorRole: "instructor" | "admin";
  courseId: string;
  courseName: string;
  createdAt: string;
  isPinned: boolean;
  visibility: "course" | "section" | "group";
  targetGroup?: string;
  allowComments: boolean;
  comments: Comment[];
  readBy: string[];
}

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  senderRole: "student" | "instructor" | "admin";
  recipientIds: string[];
  recipientNames: string[];
  subject: string;
  content: string;
  createdAt: string;
  isGroupMessage: boolean;
  conversationId: string;
  attachments?: Attachment[];
  isRead: boolean;
  replies: Comment[];
}

interface Comment {
  id: string;
  authorId: string;
  authorName: string;
  authorRole: "student" | "instructor" | "admin";
  content: string;
  createdAt: string;
  parentId?: string;
  replies: Comment[];
}

interface Attachment {
  id: string;
  filename: string;
  size: number;
  type: string;
  url: string;
}

interface ContactOption {
  id: string;
  name: string;
  role: "student" | "instructor" | "admin";
  email: string;
  isGroup?: boolean;
  groupMembers?: string[];
}

interface CommunicationPageProps {
  defaultTab?: "announcements" | "messages";
}

// Mock data
const mockAnnouncements: Announcement[] = [
  {
    id: "1",
    title: "Midterm Project Guidelines Updated",
    content:
      "Please review the updated guidelines for the midterm project. The deadline has been extended to December 20th. Make sure to follow the new submission format.",
    authorId: "inst_1",
    authorName: "Dr. A. Sharma",
    authorRole: "instructor",
    courseId: "course_1",
    courseName: "Data Science 101",
    createdAt: "2025-01-07T10:00:00Z",
    isPinned: true,
    visibility: "course",
    allowComments: true,
    comments: [
      {
        id: "c1",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the extension! Could you clarify the format for data visualization requirements?",
        createdAt: "2025-01-07T10:30:00Z",
        replies: [
          {
            id: "c2",
            authorId: "inst_1",
            authorName: "Dr. A. Sharma",
            authorRole: "instructor",
            content:
              "Please use matplotlib or seaborn for visualizations. Include at least 3 different chart types.",
            createdAt: "2025-01-07T11:00:00Z",
            replies: [],
          },
        ],
      },
    ],
    readBy: ["stu_1", "stu_2"],
  },
  {
    id: "2",
    title: "AWS Sandbox Maintenance",
    content:
      "The AWS sandbox will be undergoing maintenance on January 10th from 2 PM to 4 PM IST. Please plan your work accordingly.",
    authorId: "admin_1",
    authorName: "Admin User",
    authorRole: "admin",
    courseId: "course_1",
    courseName: "Data Science 101",
    createdAt: "2025-01-06T15:00:00Z",
    isPinned: false,
    visibility: "course",
    allowComments: false,
    comments: [],
    readBy: ["stu_1"],
  },
];

const mockMessages: Message[] = [
  {
    id: "1",
    senderId: "ta_1",
    senderName: "TA Sarah",
    senderRole: "instructor",
    recipientIds: ["stu_1", "stu_2", "stu_3"],
    recipientNames: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
    subject: "Group Project Feedback - Team Alpha",
    content:
      "Great work on your data preprocessing! However, I noticed some issues with your feature engineering approach. Please review the attached feedback document and address the comments before the final submission.",
    createdAt: "2025-01-07T09:00:00Z",
    isGroupMessage: true,
    conversationId: "conv_1",
    attachments: [
      {
        id: "att_1",
        filename: "feedback_team_alpha.pdf",
        size: 245760,
        type: "application/pdf",
        url: "/attachments/feedback_team_alpha.pdf",
      },
    ],
    isRead: false,
    replies: [
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
    ],
  },
  {
    id: "2",
    senderId: "ta_2",
    senderName: "TA Michael",
    senderRole: "instructor",
    recipientIds: ["stu_4", "stu_5"],
    recipientNames: ["Neha Verma", "Karan Singh"],
    subject: "Lab Report Feedback - Team Beta",
    content:
      "Your lab report is well-written but lacks clarity in results interpretation. Please refer to my comments in the attached document.",
    createdAt: "2025-01-08T14:30:00Z",
    isGroupMessage: true,
    conversationId: "conv_2",
    attachments: [
      {
        id: "att_2",
        filename: "feedback_team_beta.pdf",
        size: 198653,
        type: "application/pdf",
        url: "/attachments/feedback_team_beta.pdf",
      },
    ],
    isRead: false,
    replies: [],
  },
  {
    id: "3",
    senderId: "ta_3",
    senderName: "TA Ananya",
    senderRole: "instructor",
    recipientIds: ["stu_6", "stu_7", "stu_8"],
    recipientNames: ["Deepak Mehta", "Sara Khan", "Ravi Joshi"],
    subject: "Presentation Feedback - Team Gamma",
    content:
      "Great presentation delivery! But the conclusion slide seemed incomplete. Please add summary points before final submission.",
    createdAt: "2025-01-09T11:45:00Z",
    isGroupMessage: true,
    conversationId: "conv_3",
    attachments: [
      {
        id: "att_3",
        filename: "feedback_team_gamma.pdf",
        size: 256784,
        type: "application/pdf",
        url: "/attachments/feedback_team_gamma.pdf",
      },
    ],
    isRead: false,
    replies: [],
  },
  {
    id: "11",
    senderId: "ta_1",
    senderName: "TA Sarah",
    senderRole: "instructor",
    recipientIds: ["stu_1", "stu_2", "stu_3"],
    recipientNames: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
    subject: "Group Project Feedback - Team Beta",
    content:
      "Great work on your data preprocessing! However, I noticed some issues with your feature engineering approach. Please review the attached feedback document and address the comments before the final submission.",
    createdAt: "2025-01-07T09:00:00Z",
    isGroupMessage: true,
    conversationId: "conv_1",
    attachments: [
      {
        id: "att_1",
        filename: "feedback_team_alpha.pdf",
        size: 245760,
        type: "application/pdf",
        url: "/attachments/feedback_team_alpha.pdf",
      },
    ],
    isRead: false,
    replies: [
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Priya Patel",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
      {
        id: "4",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
    ],
  },
  {
    id: "5",
    senderId: "ta_1",
    senderName: "TA Sarah",
    senderRole: "instructor",
    recipientIds: ["stu_1", "stu_2", "stu_3"],
    recipientNames: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
    subject: "Group Project Feedback - Team Alpha",
    content:
      "Great work on your data preprocessing! However, I noticed some issues with your feature engineering approach. Please review the attached feedback document and address the comments before the final submission.",
    createdAt: "2025-01-07T09:00:00Z",
    isGroupMessage: true,
    conversationId: "conv_1",
    attachments: [
      {
        id: "att_1",
        filename: "feedback_team_alpha.pdf",
        size: 245760,
        type: "application/pdf",
        url: "/attachments/feedback_team_alpha.pdf",
      },
    ],
    isRead: false,
    replies: [
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
    ],
  },
  {
    id: "6",
    senderId: "ta_1",
    senderName: "TA Sarah",
    senderRole: "instructor",
    recipientIds: ["stu_1", "stu_2", "stu_3"],
    recipientNames: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
    subject: "Group Project Feedback - Team Alpha",
    content:
      "Great work on your data preprocessing! However, I noticed some issues with your feature engineering approach. Please review the attached feedback document and address the comments before the final submission.",
    createdAt: "2025-01-07T09:00:00Z",
    isGroupMessage: true,
    conversationId: "conv_1",
    attachments: [
      {
        id: "att_1",
        filename: "feedback_team_alpha.pdf",
        size: 245760,
        type: "application/pdf",
        url: "/attachments/feedback_team_alpha.pdf",
      },
    ],
    isRead: false,
    replies: [
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
    ],
  },
  {
    id: "1",
    senderId: "ta_1",
    senderName: "TA Sarah",
    senderRole: "instructor",
    recipientIds: ["stu_1", "stu_2", "stu_3"],
    recipientNames: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
    subject: "Group Project Feedback - Team Alpha",
    content:
      "Great work on your data preprocessing! However, I noticed some issues with your feature engineering approach. Please review the attached feedback document and address the comments before the final submission.",
    createdAt: "2025-01-07T09:00:00Z",
    isGroupMessage: true,
    conversationId: "conv_1",
    attachments: [
      {
        id: "att_1",
        filename: "feedback_team_alpha.pdf",
        size: 245760,
        type: "application/pdf",
        url: "/attachments/feedback_team_alpha.pdf",
      },
    ],
    isRead: false,
    replies: [
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
    ],
  },
  {
    id: "1",
    senderId: "ta_1",
    senderName: "TA Sarah",
    senderRole: "instructor",
    recipientIds: ["stu_1", "stu_2", "stu_3"],
    recipientNames: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
    subject: "Group Project Feedback - Team Alpha",
    content:
      "Great work on your data preprocessing! However, I noticed some issues with your feature engineering approach. Please review the attached feedback document and address the comments before the final submission.",
    createdAt: "2025-01-07T09:00:00Z",
    isGroupMessage: true,
    conversationId: "conv_1",
    attachments: [
      {
        id: "att_1",
        filename: "feedback_team_alpha.pdf",
        size: 245760,
        type: "application/pdf",
        url: "/attachments/feedback_team_alpha.pdf",
      },
    ],
    isRead: false,
    replies: [
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
    ],
  },
  {
    id: "1",
    senderId: "ta_1",
    senderName: "TA Sarah",
    senderRole: "instructor",
    recipientIds: ["stu_1", "stu_2", "stu_3"],
    recipientNames: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
    subject: "Group Project Feedback - Team Alpha",
    content:
      "Great work on your data preprocessing! However, I noticed some issues with your feature engineering approach. Please review the attached feedback document and address the comments before the final submission.",
    createdAt: "2025-01-07T09:00:00Z",
    isGroupMessage: true,
    conversationId: "conv_1",
    attachments: [
      {
        id: "att_1",
        filename: "feedback_team_alpha.pdf",
        size: 245760,
        type: "application/pdf",
        url: "/attachments/feedback_team_alpha.pdf",
      },
    ],
    isRead: false,
    replies: [
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
    ],
  },
  {
    id: "1",
    senderId: "ta_1",
    senderName: "TA Sarah",
    senderRole: "instructor",
    recipientIds: ["stu_1", "stu_2", "stu_3"],
    recipientNames: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
    subject: "Group Project Feedback - Team Alpha",
    content:
      "Great work on your data preprocessing! However, I noticed some issues with your feature engineering approach. Please review the attached feedback document and address the comments before the final submission.",
    createdAt: "2025-01-07T09:00:00Z",
    isGroupMessage: true,
    conversationId: "conv_1",
    attachments: [
      {
        id: "att_1",
        filename: "feedback_team_alpha.pdf",
        size: 245760,
        type: "application/pdf",
        url: "/attachments/feedback_team_alpha.pdf",
      },
    ],
    isRead: false,
    replies: [
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
    ],
  },
  {
    id: "1",
    senderId: "ta_1",
    senderName: "TA Sarah",
    senderRole: "instructor",
    recipientIds: ["stu_1", "stu_2", "stu_3"],
    recipientNames: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
    subject: "Group Project Feedback - Team Alpha",
    content:
      "Great work on your data preprocessing! However, I noticed some issues with your feature engineering approach. Please review the attached feedback document and address the comments before the final submission.",
    createdAt: "2025-01-07T09:00:00Z",
    isGroupMessage: true,
    conversationId: "conv_1",
    attachments: [
      {
        id: "att_1",
        filename: "feedback_team_alpha.pdf",
        size: 245760,
        type: "application/pdf",
        url: "/attachments/feedback_team_alpha.pdf",
      },
    ],
    isRead: false,
    replies: [
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
    ],
  },
  {
    id: "1",
    senderId: "ta_1",
    senderName: "TA Sarah",
    senderRole: "instructor",
    recipientIds: ["stu_1", "stu_2", "stu_3"],
    recipientNames: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
    subject: "Group Project Feedback - Team Alpha",
    content:
      "Great work on your data preprocessing! However, I noticed some issues with your feature engineering approach. Please review the attached feedback document and address the comments before the final submission.",
    createdAt: "2025-01-07T09:00:00Z",
    isGroupMessage: true,
    conversationId: "conv_1",
    attachments: [
      {
        id: "att_1",
        filename: "feedback_team_alpha.pdf",
        size: 245760,
        type: "application/pdf",
        url: "/attachments/feedback_team_alpha.pdf",
      },
    ],
    isRead: false,
    replies: [
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
    ],
  },
  {
    id: "1",
    senderId: "ta_1",
    senderName: "TA Sarah",
    senderRole: "instructor",
    recipientIds: ["stu_1", "stu_2", "stu_3"],
    recipientNames: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
    subject: "Group Project Feedback - Team Alpha",
    content:
      "Great work on your data preprocessing! However, I noticed some issues with your feature engineering approach. Please review the attached feedback document and address the comments before the final submission.",
    createdAt: "2025-01-07T09:00:00Z",
    isGroupMessage: true,
    conversationId: "conv_1",
    attachments: [
      {
        id: "att_1",
        filename: "feedback_team_alpha.pdf",
        size: 245760,
        type: "application/pdf",
        url: "/attachments/feedback_team_alpha.pdf",
      },
    ],
    isRead: false,
    replies: [
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
    ],
  },
  {
    id: "1",
    senderId: "ta_1",
    senderName: "TA Sarah",
    senderRole: "instructor",
    recipientIds: ["stu_1", "stu_2", "stu_3"],
    recipientNames: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
    subject: "Group Project Feedback - Team Alpha",
    content:
      "Great work on your data preprocessing! However, I noticed some issues with your feature engineering approach. Please review the attached feedback document and address the comments before the final submission.",
    createdAt: "2025-01-07T09:00:00Z",
    isGroupMessage: true,
    conversationId: "conv_1",
    attachments: [
      {
        id: "att_1",
        filename: "feedback_team_alpha.pdf",
        size: 245760,
        type: "application/pdf",
        url: "/attachments/feedback_team_alpha.pdf",
      },
    ],
    isRead: false,
    replies: [
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
    ],
  },
  {
    id: "1",
    senderId: "ta_1",
    senderName: "TA Sarah",
    senderRole: "instructor",
    recipientIds: ["stu_1", "stu_2", "stu_3"],
    recipientNames: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
    subject: "Group Project Feedback - Team Alpha",
    content:
      "Great work on your data preprocessing! However, I noticed some issues with your feature engineering approach. Please review the attached feedback document and address the comments before the final submission.",
    createdAt: "2025-01-07T09:00:00Z",
    isGroupMessage: true,
    conversationId: "conv_1",
    attachments: [
      {
        id: "att_1",
        filename: "feedback_team_alpha.pdf",
        size: 245760,
        type: "application/pdf",
        url: "/attachments/feedback_team_alpha.pdf",
      },
    ],
    isRead: false,
    replies: [
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
    ],
  },
  {
    id: "1",
    senderId: "ta_1",
    senderName: "TA Sarah",
    senderRole: "instructor",
    recipientIds: ["stu_1", "stu_2", "stu_3"],
    recipientNames: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
    subject: "Group Project Feedback - Team Alpha",
    content:
      "Great work on your data preprocessing! However, I noticed some issues with your feature engineering approach. Please review the attached feedback document and address the comments before the final submission.",
    createdAt: "2025-01-07T09:00:00Z",
    isGroupMessage: true,
    conversationId: "conv_1",
    attachments: [
      {
        id: "att_1",
        filename: "feedback_team_alpha.pdf",
        size: 245760,
        type: "application/pdf",
        url: "/attachments/feedback_team_alpha.pdf",
      },
    ],
    isRead: false,
    replies: [
      {
        id: "2",
        authorId: "stu_1",
        authorName: "Rahul Sharma",
        authorRole: "student",
        content:
          "Thank you for the detailed feedback! We will address the feature engineering issues. Could you provide some guidance on the dimensionality reduction approach?",
        createdAt: "2025-01-07T10:15:00Z",
        replies: [],
      },
    ],
  },
];

const mockContacts: ContactOption[] = [
  {
    id: "inst_1",
    name: "Dr. A. Sharma",
    role: "instructor",
    email: "<EMAIL>",
  },
  {
    id: "ta_1",
    name: "TA Sarah",
    role: "instructor",
    email: "<EMAIL>",
  },
  {
    id: "stu_2",
    name: "Priya Patel",
    role: "student",
    email: "<EMAIL>",
  },
  { id: "stu_3", name: "Amit Kumar", role: "student", email: "<EMAIL>" },
  {
    id: "group_alpha",
    name: "Team Alpha",
    role: "student",
    email: "",
    isGroup: true,
    groupMembers: ["Rahul Sharma", "Priya Patel", "Amit Kumar"],
  },
  {
    id: "group_beta",
    name: "Team Beta",
    role: "student",
    email: "",
    isGroup: true,
    groupMembers: ["John Doe", "Jane Smith", "Mike Johnson"],
  },
];

function timeAgo(date: string) {
  const now = new Date();
  const past = new Date(date);
  const diffInHours = Math.floor(
    (now.getTime() - past.getTime()) / (1000 * 60 * 60)
  );
  if (diffInHours < 1) return "Less than an hour ago";
  if (diffInHours < 24) return `${diffInHours} hours ago`;
  return `${Math.floor(diffInHours / 24)} days ago`;
}

function NewAnnouncementDialog({
  onSave,
}: {
  onSave: (announcement: Partial<Announcement>) => void;
}) {
  const { user } = useAuth();
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    content: "",
    visibility: "course",
    isPinned: false,
    allowComments: true,
    targetGroup: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      ...formData,
      visibility: formData.visibility as "course" | "section" | "group",
      authorId: user?.id || "",
      authorName: user?.name || "",
      authorRole: user?.role as "instructor" | "admin",
      courseId: "course_1",
      courseName: "Data Science 101",
      createdAt: new Date().toISOString(),
      comments: [],
      readBy: [],
    });
    setOpen(false);
    setFormData({
      title: "",
      content: "",
      visibility: "course",
      isPinned: false,
      allowComments: true,
      targetGroup: "",
    });
  };

  if (user?.role === "student") return null;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-bits-blue hover:bg-bits-blue/90">
          <Plus className="h-4 w-4 mr-2" />
          New Announcement
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl bg-white border">
        <DialogHeader>
          <DialogTitle>Create New Announcement</DialogTitle>
          <DialogDescription>
            Post an announcement to students in your course.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) =>
                setFormData({ ...formData, title: e.target.value })
              }
              placeholder="Announcement title..."
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="content">Content</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) =>
                setFormData({ ...formData, content: e.target.value })
              }
              placeholder="Write your announcement..."
              rows={6}
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="visibility">Visibility</Label>
              <Select
                value={formData.visibility}
                onValueChange={(value) =>
                  setFormData({ ...formData, visibility: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="course">Entire Course</SelectItem>
                  <SelectItem value="section">Specific Section</SelectItem>
                  <SelectItem value="group">Specific Group</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {formData.visibility === "group" && (
              <div className="space-y-2">
                <Label htmlFor="targetGroup">Target Group</Label>
                <Select
                  value={formData.targetGroup}
                  onValueChange={(value) =>
                    setFormData({ ...formData, targetGroup: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select group" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="team_alpha">Team Alpha</SelectItem>
                    <SelectItem value="team_beta">Team Beta</SelectItem>
                    <SelectItem value="team_gamma">Team Gamma</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={formData.isPinned}
                onChange={(e) =>
                  setFormData({ ...formData, isPinned: e.target.checked })
                }
                className="rounded border-gray-300"
              />
              <span className="text-sm">Pin to top</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={formData.allowComments}
                onChange={(e) =>
                  setFormData({ ...formData, allowComments: e.target.checked })
                }
                className="rounded border-gray-300"
              />
              <span className="text-sm">Allow comments</span>
            </label>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-bits-blue hover:bg-bits-blue/90"
            >
              Post Announcement
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

function NewMessageDialog({
  onSave,
}: {
  onSave: (message: Partial<Message>) => void;
}) {
  const { user } = useAuth();
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({
    recipients: [] as string[],
    subject: "",
    content: "",
    isGroupMessage: false,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const selectedContacts = mockContacts.filter((contact) =>
      formData.recipients.includes(contact.id)
    );

    const newMessage: Partial<Message> = {
      senderId: user?.id || "",
      senderName: user?.name || "",
      senderRole: user?.role as "student" | "instructor" | "admin",
      recipientIds: formData.recipients,
      recipientNames: selectedContacts.map((c) => c.name),
      subject: formData.subject,
      content: formData.content,
      createdAt: new Date().toISOString(),
      isGroupMessage:
        formData.recipients.length > 1 ||
        selectedContacts.some((c) => c.isGroup),
      conversationId: `conv_${Date.now()}`,
      isRead: false,
      replies: [],
    };

    onSave(newMessage);
    setOpen(false);
    setFormData({
      recipients: [],
      subject: "",
      content: "",
      isGroupMessage: false,
    });
  };

  const handleRecipientToggle = (contactId: string) => {
    setFormData((prev) => ({
      ...prev,
      recipients: prev.recipients.includes(contactId)
        ? prev.recipients.filter((id) => id !== contactId)
        : [...prev.recipients, contactId],
    }));
  };

  const selectedContacts = mockContacts.filter((contact) =>
    formData.recipients.includes(contact.id)
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-bits-blue hover:bg-bits-blue/90">
          <Plus className="h-4 w-4 mr-2" />
          New Message
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl bg-white border">
        <DialogHeader>
          <DialogTitle>Compose New Message</DialogTitle>
          <DialogDescription>
            Send a message to instructors, students, or groups.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label>Recipients</Label>
            <div className="border rounded-md p-3 max-h-40 overflow-y-auto space-y-2 custom-scroll">
              {mockContacts.map((contact) => (
                <label
                  key={contact.id}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={formData.recipients.includes(contact.id)}
                    onChange={() => handleRecipientToggle(contact.id)}
                    className="rounded border-gray-300"
                  />
                  <div className="flex-1">
                    <span className="font-medium">{contact.name}</span>
                    {contact.isGroup ? (
                      <div className="text-xs text-muted-foreground">
                        Group: {contact.groupMembers?.join(", ")}
                      </div>
                    ) : (
                      <div className="text-xs text-muted-foreground capitalize">
                        {contact.role} • {contact.email}
                      </div>
                    )}
                  </div>
                </label>
              ))}
            </div>

            {selectedContacts.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {selectedContacts.map((contact) => (
                  <Badge
                    key={contact.id}
                    variant="secondary"
                    className="flex items-center space-x-1"
                  >
                    <span>{contact.name}</span>
                    <button
                      type="button"
                      onClick={() => handleRecipientToggle(contact.id)}
                      className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="subject">Subject</Label>
            <Input
              id="subject"
              value={formData.subject}
              onChange={(e) =>
                setFormData({ ...formData, subject: e.target.value })
              }
              placeholder="Message subject..."
              className="bg-gray-100 border-none"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="content">Message</Label>
            <Input
              id="content"
              value={formData.content}
              onChange={(e) =>
                setFormData({ ...formData, content: e.target.value })
              }
              placeholder="Type your message..."
              className="bg-gray-100 border-none"
              required
            />
          </div>

          <div className="flex-1 justify-between items-center">
            <div className="flex w-full space-x-3 mt-">
              <Button
                type="button"
                className="flex-1"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="flex-1 bg-bits-blue hover:bg-bits-blue/90"
                disabled={formData.recipients.length === 0}
              >
                <Send className="h-4 w-4 mr-2" />
                Send Message
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

function AnnouncementCard({
  announcement,
  onAddComment,
  isHighlighted,
}: {
  announcement: Announcement;
  onAddComment: (announcementId: string, comment: string) => void;
  isHighlighted?: boolean;
}) {
  const { user } = useAuth();
  const [showComments, setShowComments] = useState(false);
  const [newComment, setNewComment] = useState("");

  const handleAddComment = () => {
    if (newComment.trim()) {
      onAddComment(announcement.id, newComment);
      setNewComment("");
    }
  };

  const isUnread = !announcement.readBy.includes(user?.id || "");

  return (
    <Card
      className={`
      ${announcement.isPinned ? "border-bits-gold bg-yellow-50" : ""} 
      ${isUnread ? "border-l-bits-blue" : ""} 
      ${isHighlighted ? "ring-2 ring-bits-blue shadow-lg" : ""}
      transition-all duration-300 gap-2
    `}
    >
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              {announcement.isPinned && (
                <Pin className="h-4 w-4 text-bits-gold" />
              )}
              <CardTitle className="text-lg">{announcement.title}</CardTitle>
              {isUnread && (
                <Badge variant="destructive" className="text-xs">
                  New
                </Badge>
              )}
              {isHighlighted && (
                <Badge className="bg-bits-blue text-white text-xs">
                  Opened from notification
                </Badge>
              )}
            </div>
            <CardDescription className="text-muted-foreground">
              {announcement.authorName} • {announcement.courseName} •{" "}
              {timeAgo(announcement.createdAt)}
            </CardDescription>
          </div>
          {/* <Badge variant="outline" className="capitalize">
            {announcement.visibility}{" "}
            {announcement.visibility === "group" && announcement.targetGroup
              ? `(${announcement.targetGroup})`
              : ""}
          </Badge> */}
        </div>
      </CardHeader>

      <CardContent className="!pb-4">
        <div className="prose prose-sm max-w-none mb-1 ">
          <p>{announcement.content}</p>
        </div>

        {/* {announcement.allowComments && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowComments(!showComments)}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                {announcement.comments.length} Comments
              </Button>

              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Eye className="h-4 w-4" />
                <span>{announcement.readBy.length} read</span>
              </div>
            </div>

            {showComments && (
              <div className="space-y-4">
                <div className="space-y-3">
                  {announcement.comments.map((comment) => (
                    <div
                      key={comment.id}
                      className="border-l-2 border-gray-200 pl-4"
                    >
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-sm">
                          {comment.authorName}
                        </span>
                        <Badge variant="outline" className="text-xs capitalize">
                          {comment.authorRole}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {timeAgo(comment.createdAt)}
                        </span>
                      </div>
                      <p className="text-sm">{comment.content}</p>


                      {comment.replies.map((reply) => (
                        <div
                          key={reply.id}
                          className="ml-4 mt-2 border-l-2 border-gray-100 pl-3"
                        >
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium text-sm">
                              {reply.authorName}
                            </span>
                            <Badge
                              variant="outline"
                              className="text-xs capitalize"
                            >
                              {reply.authorRole}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {timeAgo(reply.createdAt)}
                            </span>
                          </div>
                          <p className="text-sm">{reply.content}</p>
                        </div>
                      ))}
                    </div>
                  ))}
                </div>


                <div className="flex space-x-2">
                  <Textarea
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    placeholder="Add a comment..."
                    rows={2}
                    className="flex-1"
                  />
                  <Button
                    size="sm"
                    onClick={handleAddComment}
                    disabled={!newComment.trim()}
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        )} */}
      </CardContent>
    </Card>
  );
}

function AnnouncementsTab({
  highlightedAnnouncementId,
}: {
  highlightedAnnouncementId?: string;
}) {
  const [announcements, setAnnouncements] = useState(mockAnnouncements);
  const [searchTerm, setSearchTerm] = useState("");
  const { user } = useAuth();
  const [tab, setTab] = useState<"announcements" | "messages">("announcements");

  const filteredAnnouncements = announcements
    .filter(
      (ann) =>
        ann.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ann.content.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

  const handleSaveAnnouncement = (newAnnouncement: Partial<Announcement>) => {
    const validVisibility = ["course", "section", "group"].includes(
      newAnnouncement.visibility as string
    )
      ? (newAnnouncement.visibility as "course" | "section" | "group")
      : "course";
    const announcement: Announcement = {
      id: Date.now().toString(),
      title: newAnnouncement.title || "",
      content: newAnnouncement.content || "",
      visibility: validVisibility,
      authorId: user?.id || "",
      authorName: user?.name || "",
      authorRole: (user?.role as "instructor" | "admin") || "instructor",
      courseId: "",
      courseName: "",
      createdAt: new Date().toISOString(),
      comments: [],
      readBy: [],
      isPinned: false,
      allowComments: true,
    };
    setAnnouncements((prev) => [announcement, ...prev]);
  };

  const handleAddComment = (announcementId: string, commentContent: string) => {
    // Implementation would add comment to announcement
    console.log("Adding comment:", announcementId, commentContent);
  };

  // Auto-scroll to highlighted announcement
  useEffect(() => {
    if (highlightedAnnouncementId) {
      const element = document.getElementById(
        `announcement-${highlightedAnnouncementId}`
      );
      if (element) {
        element.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    }
  }, [highlightedAnnouncementId]);

  return (
    <div>
      <Card className="gap-4">
        <CardTitle className="mt-4 ml-6 mb-0">
          <h3 className="text-xl font-semibold">
            Announcements ({announcements.length})
          </h3>
        </CardTitle>
        <CardContent>
          <div className="space-y-4">
            {mockAnnouncements.map((announcement) => (
              <div key={announcement.id} id={`announcement-${announcement.id}`}>
                <AnnouncementCard
                  announcement={announcement}
                  onAddComment={handleAddComment}
                  isHighlighted={announcement.id === highlightedAnnouncementId}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// const [searchTerm, setSearchTerm] = useState("");
// const filteredMessages = messages
//   .filter(
//     (msg) =>
//       msg.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
//       msg.content.toLowerCase().includes(searchTerm.toLowerCase())
//   )
//   .sort(
//     (a, b) =>
//       new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
//   );

function MessagesTab() {
  const { user } = useAuth();
  const [messages, setMessages] = useState(mockMessages);

  const [selectedMessageId, setSelectedMessageId] = useState(
    mockMessages?.[0]?.id ?? null
  );

  const selectedMessage = useMemo(
    () => messages.find((m) => m.id === selectedMessageId) ?? null,
    [messages, selectedMessageId]
  );
  const handleSaveMessage = (newMessage: Partial<Message>) => {
    const message: Message = {
      id: Date.now().toString(),
      senderId: user?.id || "",
      senderName: user?.name || "",
      senderRole:
        (user?.role as "student" | "instructor" | "admin") || "student",
      recipientIds: [],
      recipientNames: ["priya"],
      subject: newMessage.subject || "",
      content: newMessage.content || "",
      createdAt: new Date().toISOString(),
      isGroupMessage: false,
      conversationId: "",
      attachments: [],
      isRead: false,
      replies: [],
    };
    setMessages((prev) => [message, ...prev]);
  };

  const handleAddReply = (messageId: string, replyContent: string) => {
    setMessages((prev) =>
      prev.map((msg) =>
        msg.id === messageId
          ? {
              ...msg,
              replies: [
                ...msg.replies,
                {
                  id: Date.now().toString(),
                  authorId: user?.id || "",
                  authorName: user?.name || "",
                  authorRole:
                    (user?.role as "student" | "instructor" | "admin") ||
                    "student",
                  content: replyContent,
                  createdAt: new Date().toISOString(),
                  replies: [],
                },
              ],
            }
          : msg
      )
    );
    // console.log(messages);
  };

  const [replyText, setReplyText] = useState("");
  // const [selectedMessage, setSelectedMessage] = useState(messages[0] || null);
  return (
    <Card className="flex flex-col h-[620px] overflow-hidden">
      <div className="flex items-center justify-between mt-4 mx-6 shrink-0">
        <p className="text-xl font-semibold">Messages ({messages.length})</p>
        <NewMessageDialog onSave={handleSaveMessage} />
      </div>

      {messages.length > 0 ? (
        <div className="mx-6 mt-2 flex flex-row gap-6 flex-1 overflow-hidden">
          {/* LEFT PANE */}
          <div className="w-1/3 min-w-[260px] h-[520px] overflow-hidden">
            <div className="h-full overflow-y-auto pr-1 custom-scroll hover:scrollbar">
              {messages.map((message) => (
                <Card
                  key={message.id}
                  onClick={() => setSelectedMessageId(message.id)}
                  className={`p-4 rounded-lg cursor-pointer transition-colors duration-150 ${
                    selectedMessageId === message.id ? "" : "hover:bg-gray-50"
                  }`}
                >
                  <div>
                    <CardTitle className="!text-bits-blue-neutral-900 text-lg font-semibold line-clamp-1">
                      {message.subject}
                    </CardTitle>
                    <CardDescription className="text-sm text-muted-foreground truncate line-clamp-1 mt-1">
                      {message.isGroupMessage ? (
                        <>with {message.recipientNames.join(", ")}</>
                      ) : (
                        <>to {message.recipientNames}</>
                      )}
                    </CardDescription>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* RIGHT PANE */}
          <div className="w-2/3 flex-grow overflow-hidden">
            <Card className="flex flex-col h-[520px] overflow-hidden">
              {selectedMessage ? (
                <div className="flex flex-col w-full h-full">
                  <CardHeader className="shrink-0">
                    <div>
                      <CardTitle className="!text-bits-blue-neutral-900 text-xl font-semibold">
                        {selectedMessage.subject}
                      </CardTitle>
                      <CardDescription className="!text-base text-muted-foreground">
                        {selectedMessage.isGroupMessage && (
                          <>with {selectedMessage.recipientNames.join(", ")}</>
                        )}
                      </CardDescription>
                    </div>
                  </CardHeader>

                  <CardContent className="flex-1 min-h-0 overflow-y-auto px-4 custom-scroll hover:scrollbar">
                    <div className="mt-2 space-y-2">
                      <CardDescription className="flex items-center gap-2">
                        <p className="font-semibold text-base text-bits-grey">
                          {selectedMessage.senderName}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {`${new Date(
                            selectedMessage.createdAt
                          ).toLocaleTimeString("en-US", {
                            hour: "2-digit",
                            minute: "2-digit",
                            hour12: true,
                          })}, ${new Date(
                            selectedMessage.createdAt
                          ).toLocaleDateString("en-US", {
                            day: "2-digit",
                            month: "short",
                            year: "numeric",
                          })}`}
                        </p>
                      </CardDescription>

                      <Card className="rounded-md w-11/12 p-2 !bg-gray-50">
                        <p className="text-sm text-bits-grey">
                          {selectedMessage.content}
                        </p>
                      </Card>
                    </div>

                    {selectedMessage.replies &&
                      selectedMessage.replies.length > 0 && (
                        <div className="space-y-2 mt-3">
                          {selectedMessage.replies.map((reply) => (
                            <div key={reply.id} className="space-y-2">
                              <CardDescription className="flex items-center gap-2">
                                <p className="font-semibold text-base text-bits-grey">
                                  {reply.authorName}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  {`${new Date(
                                    reply.createdAt
                                  ).toLocaleTimeString("en-US", {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    hour12: true,
                                  })}, ${new Date(
                                    reply.createdAt
                                  ).toLocaleDateString("en-US", {
                                    day: "2-digit",
                                    month: "short",
                                    year: "numeric",
                                  })}`}
                                </p>
                              </CardDescription>
                              <Card className="rounded-md w-11/12 p-2 !bg-gray-50">
                                <p className="text-sm text-bits-grey">
                                  {reply.content}
                                </p>
                              </Card>
                            </div>
                          ))}
                        </div>
                      )}
                  </CardContent>

                  <CardFooter className="shrink-0 flex items-center justify-between gap-2 p-4 h-20">
                    <Input
                      type="text"
                      placeholder="Type your Reply"
                      className="h-full bg-gray-100"
                      value={replyText}
                      onChange={(e) => setReplyText(e.target.value)}
                    />
                    <Button
                      className="bg-bits-blue hover:bg-blue-900 text-white font-bold py-2 px-4 rounded-lg"
                      onClick={() => {
                        if (selectedMessage && replyText.trim()) {
                          handleAddReply(selectedMessage.id, replyText);
                          setReplyText("");
                        }
                      }}
                    >
                      <Send className="h-4 w-4" />
                      Send
                    </Button>
                  </CardFooter>
                </div>
              ) : null}
            </Card>
          </div>
        </div>
      ) : (
        // Empty state when no messages: hide left/right cards entirely
        <div className="flex-1 mx-6 mt-4 mb-6 flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <p className="text-lg font-medium">No messages yet</p>
            <p className="mt-1">Create a new message to get started.</p>
          </div>
        </div>
      )}
    </Card>
  );
}

function CommunicationPage({
  defaultTab = "announcements",
}: CommunicationPageProps) {
  const { user } = useAuth();
  const [tab, setTab] = useState<"announcements" | "messages">(defaultTab);
  const [highlightedAnnouncementId, setHighlightedAnnouncementId] =
    useState<string>("");
  const { goBack } = useNavigation();

  return (
    <div className="container mx-auto p-4">
      <div className="flex flex-row items-center space-x-2 mb-4">
        <button onClick={goBack} className="p-2 rounded-full hover:bg-gray-100">
          <ArrowLeft className="h-7 w-8 text-gray-700" />
        </button>
        <h1 className="text-2xl font-bold">Notifications</h1>
      </div>

      <div className="mx-2 space-x-4">
        <div className="p-0 mt-2">
          <Card className="mx-2 p-1 flex flex-row gap-1 rounded-full bg-gray-200">
            <button
              onClick={() => setTab("announcements")}
              className={cn(
                "flex-1 py-2 px-4 text-sm font-medium rounded-full transition-colors text-gray-600",
                tab === "announcements" ? "bg-white shadow" : ""
              )}
            >
              Announcements
            </button>
            <button
              onClick={() => setTab("messages")}
              className={cn(
                "flex-1 py-2 px-4 text-sm font-medium rounded-full transition-colors  text-gray-800",
                tab === "messages" ? "bg-white shadow" : ""
              )}
            >
              Messages
            </button>
          </Card>
        </div>

        <div className="p-0 mt-6">
          {tab === "announcements" && (
            <AnnouncementsTab
              highlightedAnnouncementId={highlightedAnnouncementId}
            />
          )}
          {tab === "messages" && <MessagesTab />}
        </div>
      </div>
    </div>
  );
}

export default CommunicationPage;
