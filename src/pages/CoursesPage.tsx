import React, { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Input } from "../components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import { Progress } from "../components/ui/progress";
import {
  Search,
  Eye,
  Users,
  BookOpen,
  Calendar,
  Settings,
  Clock,
  CheckCircle,
  AlertCircle,
  FileText,
  Play,
  Terminal,
  Award,
  Target,
  TrendingUp,
} from "lucide-react";
 import { useAuth, useNavigation } from "../App";
// import useAuth from "../components/Context/AuthContext";
// import useNavigation from "../components/Context/NavigationContext";
import { useNavigate } from "react-router-dom";
interface Course {
  id: string;
  name: string;
  code: string;
  description: string;
  instructor: string;
  semester: string;
  credits: number;
  startDate: string;
  endDate: string;
  enrolledStudents: number;
  maxCapacity: number;
  status: "active" | "inactive" | "draft";
  category: string;
}

interface StudentProject {
  id: string;
  title: string;
  courseId: string;
  courseName: string;
  courseCode: string;
  description: string;
  dueDate: string;
  status: "not-started" | "in-progress" | "submitted" | "graded";
  grade?: string;
  maxScore: number;
  earnedScore?: number;
  submissionDate?: string;
  instructor: string;
  difficulty: "easy" | "medium" | "hard";
  estimatedTime: string;
  requiresDatasets: boolean;
  tags: string[];
}

// Mock courses data
const mockCourses: Course[] = [
  {
    id: "1",
    name: "Data Science 101",
    code: "DS101",
    description:
      "Introduction to Data Science covering fundamentals of statistics, programming, and machine learning.",
    instructor: "Dr. A. Sharma",
    semester: "Fall 2024",
    credits: 4,
    startDate: "2024-08-15",
    endDate: "2024-12-15",
    enrolledStudents: 30,
    maxCapacity: 35,
    status: "active",
    category: "Data Science",
  },
  {
    id: "2",
    name: "Machine Learning Fundamentals",
    code: "ML201",
    description:
      "Advanced machine learning concepts including supervised and unsupervised learning algorithms.",
    instructor: "Dr. B. Patel",
    semester: "Fall 2024",
    credits: 4,
    startDate: "2024-08-15",
    endDate: "2024-12-15",
    enrolledStudents: 25,
    maxCapacity: 30,
    status: "active",
    category: "Machine Learning",
  },
  {
    id: "3",
    name: "Deep Learning Advanced",
    code: "DL301",
    description:
      "Advanced deep learning techniques including neural networks, CNNs, and RNNs.",
    instructor: "Dr. C. Singh",
    semester: "Spring 2025",
    credits: 4,
    startDate: "2025-01-15",
    endDate: "2025-05-15",
    enrolledStudents: 0,
    maxCapacity: 25,
    status: "draft",
    category: "Deep Learning",
  },
];

// Mock student enrolled courses (subset of all courses)
const studentEnrolledCourses = [
  mockCourses[0], // Data Science 101
  mockCourses[1], // Machine Learning Fundamentals
];

// Mock student projects/assignments
const studentProjects: StudentProject[] = [
  {
    id: "1",
    title: "Linear Regression Analysis",
    courseId: "1",
    courseName: "Data Science 101",
    courseCode: "DS101",
    description:
      "Build a comprehensive linear regression model to predict housing prices using multiple features.",
    dueDate: "2025-01-20T23:59:00Z",
    status: "graded",
    grade: "A-",
    maxScore: 100,
    earnedScore: 87,
    submissionDate: "2025-01-12T10:30:00Z",
    instructor: "Dr. A. Sharma",
    difficulty: "medium",
    estimatedTime: "2-3 hours",
    requiresDatasets: true,
    tags: ["regression", "statistics", "python"],
  },
  {
    id: "2",
    title: "Customer Segmentation Analysis",
    courseId: "1",
    courseName: "Data Science 101",
    courseCode: "DS101",
    description:
      "Use clustering algorithms to segment customers based on purchasing behavior.",
    dueDate: "2025-02-15T23:59:00Z",
    status: "in-progress",
    maxScore: 100,
    instructor: "Dr. A. Sharma",
    difficulty: "hard",
    estimatedTime: "4-5 hours",
    requiresDatasets: true,
    tags: ["clustering", "unsupervised learning", "python"],
  },
  {
    id: "3",
    title: "Classification Algorithms Comparison",
    courseId: "2",
    courseName: "Machine Learning Fundamentals",
    courseCode: "ML201",
    description:
      "Compare different classification algorithms on multiple datasets.",
    dueDate: "2025-01-30T23:59:00Z",
    status: "submitted",
    maxScore: 100,
    submissionDate: "2025-01-28T14:20:00Z",
    instructor: "Dr. B. Patel",
    difficulty: "medium",
    estimatedTime: "3-4 hours",
    requiresDatasets: true,
    tags: ["classification", "supervised learning", "comparison"],
  },
  {
    id: "4",
    title: "Neural Network Implementation",
    courseId: "2",
    courseName: "Machine Learning Fundamentals",
    courseCode: "ML201",
    description: "Implement a neural network from scratch using NumPy.",
    dueDate: "2025-02-28T23:59:00Z",
    status: "not-started",
    maxScore: 100,
    instructor: "Dr. B. Patel",
    difficulty: "hard",
    estimatedTime: "5-6 hours",
    requiresDatasets: false,
    tags: ["neural networks", "deep learning", "numpy"],
  },
];

function StudentCoursesView() {
  const { navigateTo } = useNavigation();
  const [searchTerm, setSearchTerm] = useState("");
  const [courseFilter, setCourseFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
const navigate = useNavigate()
  const filteredProjects = studentProjects.filter((project) => {
    const matchesSearch =
      project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.courseName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.courseCode.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCourse =
      courseFilter === "all" || project.courseId === courseFilter;
    const matchesStatus =
      statusFilter === "all" || project.status === statusFilter;
    return matchesSearch && matchesCourse && matchesStatus;
  });

  const handleViewProject = (projectId: string) => {
navigate(`/project-details/${projectId}`)
    //navigateTo("project-details", { projectId });
  };

  const handleLaunchSandbox = (projectId: string, projectTitle: string) => {
    navigateTo("sandbox", { projectId, projectTitle, context: "project" });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "graded":
        return "bg-green-100 text-green-800";
      case "submitted":
        return "bg-blue-100 text-blue-800";
      case "in-progress":
        return "bg-yellow-100 text-yellow-800";
      case "not-started":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "graded":
        return <Award className="h-4 w-4" />;
      case "submitted":
        return <CheckCircle className="h-4 w-4" />;
      case "in-progress":
        return <Clock className="h-4 w-4" />;
      case "not-started":
        return <Target className="h-4 w-4" />;
      default:
        return <Target className="h-4 w-4" />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "easy":
        return "bg-green-100 text-green-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "hard":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getDaysUntilDue = (dueDate: string) => {
    const due = new Date(dueDate);
    const now = new Date();
    const diffTime = due.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Calculate course statistics
  const completedProjects = studentProjects.filter(
    (p) => p.status === "graded"
  ).length;
  const averageGrade =
    (studentProjects
      .filter((p) => p.earnedScore && p.maxScore)
      .reduce((sum, p) => sum + p.earnedScore! / p.maxScore, 0) /
      studentProjects.filter((p) => p.earnedScore && p.maxScore).length) *
    100;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">My Courses</h1>
          <p className="text-gray-600 mt-1">
            Track your enrolled courses and assignments
          </p>
        </div>
      </div>

      {/* Student Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-bits-blue" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Enrolled Courses
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {studentEnrolledCourses.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Completed Projects
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {completedProjects}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Award className="h-8 w-8 text-bits-gold" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Average Grade
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {isNaN(averageGrade) ? "N/A" : `${Math.round(averageGrade)}%`}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Pending Projects
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {
                    studentProjects.filter(
                      (p) =>
                        p.status === "in-progress" || p.status === "not-started"
                    ).length
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enrolled Courses */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-bits-blue" />
            Enrolled Courses
          </CardTitle>
          <CardDescription>Your current course enrollments</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {studentEnrolledCourses.map((course) => {
              const courseProjects = studentProjects.filter(
                (p) => p.courseId === course.id
              );
              const completedCourseProjects = courseProjects.filter(
                (p) => p.status === "graded"
              ).length;
              const progressPercentage =
                courseProjects.length > 0
                  ? (completedCourseProjects / courseProjects.length) * 100
                  : 0;

              return (
                <Card key={course.id} className="border-l-4 border-l-bits-blue">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{course.name}</CardTitle>
                        <p className="text-sm text-gray-600">
                          {course.code} • {course.instructor}
                        </p>
                      </div>
                      <Badge className="bg-bits-blue text-white">
                        {course.credits} Credits
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <p className="text-sm text-gray-600">
                      {course.description}
                    </p>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Progress:</span>
                        <span className="font-medium">
                          {completedCourseProjects}/{courseProjects.length}{" "}
                          projects
                        </span>
                      </div>
                      <Progress value={progressPercentage} className="h-2" />
                    </div>

                    <div className="flex items-center justify-between pt-2">
                      <span className="text-sm text-gray-600">
                        {courseProjects.length} total assignments
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          navigateTo("view-course", { courseId: course.id })
                        }
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search projects and assignments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={courseFilter} onValueChange={setCourseFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Courses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Courses</SelectItem>
                {studentEnrolledCourses.map((course) => (
                  <SelectItem key={course.id} value={course.id}>
                    {course.code} - {course.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="not-started">Not Started</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="submitted">Submitted</SelectItem>
                <SelectItem value="graded">Graded</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Projects/Assignments */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-bits-blue" />
            Projects & Assignments
          </CardTitle>
          <CardDescription>
            All your course assignments and projects
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredProjects.map((project) => {
              const daysUntilDue = getDaysUntilDue(project.dueDate);
              const isOverdue =
                daysUntilDue < 0 &&
                project.status !== "submitted" &&
                project.status !== "graded";

              return (
                <Card
                  key={project.id}
                  className={`transition-shadow hover:shadow-md ${
                    isOverdue ? "border-red-200" : ""
                  }`}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg mb-1">
                          {project.title}
                        </CardTitle>
                        <p className="text-sm text-gray-600">
                          {project.courseCode} • {project.instructor}
                        </p>
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        <Badge className={getStatusColor(project.status)}>
                          {getStatusIcon(project.status)}
                          <span className="ml-1 capitalize">
                            {project.status.replace("-", " ")}
                          </span>
                        </Badge>
                        <Badge
                          variant="outline"
                          className={getDifficultyColor(project.difficulty)}
                        >
                          {project.difficulty}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {project.description}
                    </p>

                    {/* Project Details */}
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Due Date:</span>
                        <span
                          className={`font-medium ${
                            isOverdue
                              ? "text-red-600"
                              : daysUntilDue <= 3
                              ? "text-orange-600"
                              : "text-gray-900"
                          }`}
                        >
                          {new Date(project.dueDate).toLocaleDateString()}
                          {daysUntilDue >= 0 && project.status !== "graded" && (
                            <span className="ml-1">({daysUntilDue} days)</span>
                          )}
                          {isOverdue && <span className="ml-1">(Overdue)</span>}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Estimated Time:</span>
                        <span className="font-medium">
                          {project.estimatedTime}
                        </span>
                      </div>

                      {project.status === "graded" && project.grade && (
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Grade:</span>
                          <span className="font-medium text-green-600">
                            {project.grade} ({project.earnedScore}/
                            {project.maxScore})
                          </span>
                        </div>
                      )}

                      {project.submissionDate && (
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Submitted:</span>
                          <span className="font-medium">
                            {new Date(
                              project.submissionDate
                            ).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-1">
                      {project.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs"
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2 pt-2 border-t">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewProject(project.id)}
                        className="flex-1"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>

                      {(project.status === "not-started" ||
                        project.status === "in-progress") && (
                        <Button
                          size="sm"
                          onClick={() =>
                            handleLaunchSandbox(project.id, project.title)
                          }
                          className="flex-1 bg-bits-blue hover:bg-bits-blue/90"
                        >
                          <Terminal className="h-4 w-4 mr-2" />
                          Launch Sandbox
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Empty State */}
          {filteredProjects.length === 0 && (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No projects found
              </h3>
              <p className="text-gray-600">
                {searchTerm || courseFilter !== "all" || statusFilter !== "all"
                  ? "Try adjusting your search or filter criteria."
                  : "No projects are currently available."}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

function InstructorAdminCoursesView() {
  const { navigateTo } = useNavigation();
  const [courses, setCourses] = useState<Course[]>(mockCourses);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  const filteredCourses = courses.filter((course) => {
    const matchesSearch =
      course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.instructor.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" || course.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleViewCourse = (courseId: string) => {
    navigateTo("view-course", { courseId });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "inactive":
        return "bg-gray-100 text-gray-800";
      case "draft":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Courses</h1>
          {/* <p className="text-gray-600 mt-1">Manage and view all courses in the system</p> */}
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search courses..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Course Stats */}
      {/* <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-bits-blue" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Courses</p>
                <p className="text-2xl font-bold text-gray-900">{courses.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Courses</p>
                <p className="text-2xl font-bold text-gray-900">
                  {courses.filter(c => c.status === 'active').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-bits-gold" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">
                  {courses.reduce((sum, course) => sum + course.enrolledStudents, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Settings className="h-8 w-8 text-gray-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Draft Courses</p>
                <p className="text-2xl font-bold text-gray-900">
                  {courses.filter(c => c.status === 'draft').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div> */}

      {/* Courses Grid */}
      {/* <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCourses.map((course) => (
          <Card key={course.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg mb-1">{course.name}</CardTitle>
                  <p className="text-sm text-gray-600">{course.code} • {course.semester}</p>
                </div>
                <Badge className={getStatusColor(course.status)}>
                  {course.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600 line-clamp-2">{course.description}</p>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Instructor:</span>
                  <span className="font-medium">{course.instructor}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Students:</span>
                  <span className="font-medium">{course.enrolledStudents}/{course.maxCapacity}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Credits:</span>
                  <span className="font-medium">{course.credits}</span>
                </div>
              </div>

              <div className="pt-4 border-t">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => handleViewCourse(course.id)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Course
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div> */}

      {/* New course card */}
      <div className="space-y-4">
        {filteredCourses.map((course) => (
          <Card key={course.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg mb-1">{course.name}</CardTitle>
                  <p className="text-sm text-gray-600">
                    {course.instructor} • {course.enrolledStudents}{" "}
                    {course.enrolledStudents > 1 ? "students" : "student"}
                  </p>
                </div>
                <Badge className={getStatusColor(course.status)}>
                  {course.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="">
              {/* <p className="text-sm text-gray-600 line-clamp-2">{course.description}</p>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Instructor:</span>
                  <span className="font-medium">{course.instructor}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Students:</span>
                  <span className="font-medium">{course.enrolledStudents}/{course.maxCapacity}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Credits:</span>
                  <span className="font-medium">{course.credits}</span>
                </div>
              </div> */}

              <div className="">
                <Button
                  variant="outline"
                  className=""
                  onClick={() => handleViewCourse(course.id)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      {/* Empty State */}
      {filteredCourses.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No courses found
          </h3>
          <p className="text-gray-600">
            {searchTerm || statusFilter !== "all"
              ? "Try adjusting your search or filter criteria."
              : "No courses are currently available."}
          </p>
        </div>
      )}
    </div>
  );
}

export default function CoursesPage() {
  const { user } = useAuth();

  // Show student-specific view for students, instructor/admin view for others
  if (user?.role === "student") {
    return <StudentCoursesView />;
  } else {
    return <InstructorAdminCoursesView />;
  }
}
