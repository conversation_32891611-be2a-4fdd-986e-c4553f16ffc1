import React, { useState, useMemo, useEffect, useRef } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../components/ui/card";

import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { Textarea } from "../components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
} from "../components/ui/select";
import { RadioGroup, RadioGroupItem } from "../components/ui/radio-group";
import { Switch } from "../components/ui/switch";
import { Badge } from "../components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../components/ui/dialog";
import { toast } from "sonner";
import {
  ArrowLeft,
  Plus,
  Upload,
  FileText,
  Code,
  RotateCcw,
  Download,
  Trash2,
  GripVertical,
  Home,
  FolderOpen,
  Calendar,
  AlertTriangle,
  Save,
  Bookmark,
  Copy,
  Target,
  BookOpen,
  CheckCircle,
  Award,
  X,
  Clipboard,
  Terminal,
  Info,
  Settings,
} from "lucide-react";
import { useNavigation } from "../App";
//import useNavigation from "../components/Context/NavigationContext";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "../components/ui/breadcrumb";

import {
  checkpoints,
  CourseStaff,
  CreateProjectResponse,
  Difficulty,
  ProjectData,
  useCoursesQuery,
  useCreateCheckPointMutation,
  useCreateProjectMutation,
  useCreateRubricMutation,
  useDatasetMutation,
  useEditProjectMutation,
  useStaffMemberQuery,
} from "../api/createProjectApi";
import paths from "../routes";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { EnhancedProject, getProjectById } from "../api/projectManagementApi";

interface TemplateData {
  id: string;
  name: string;
  description: string;
  projectData: ProjectData;
  createdAt: string;
  createdBy: string;
  category: string;
  tags: string[];
  usageCount: number;
}

interface CreateProjectPageProps {
  mode?: "create" | "edit" | "template";
  projectId?: string;
  templateData?: TemplateData;
}

// Tag Input Component
const TagInput = React.memo(
  ({
    tags,
    setTags,
    placeholder,
  }: {
    tags: string[];
    setTags: (tags: string[]) => void;
    placeholder?: string;
  }) => {
    const [inputValue, setInputValue] = useState("");

    // Safety check: ensure tags is always an array
    const safeTags = tags || [];

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === "Enter" || e.key === ",") {
        e.preventDefault();
        addTag();
      } else if (
        e.key === "Backspace" &&
        inputValue === "" &&
        safeTags.length > 0
      ) {
        removeTag(safeTags.length - 1);
      }
    };

    const addTag = () => {
      const trimmedValue = inputValue.trim();
      if (trimmedValue && !safeTags.includes(trimmedValue)) {
        setTags([...safeTags, trimmedValue]);
        setInputValue("");
      }
    };

    const removeTag = (index: number) => {
      setTags(safeTags.filter((_, i) => i !== index));
    };

    return (
      <div className="space-y-2">
        <div className="flex flex-wrap gap-2 p-3 border rounded-lg min-h-[42px] focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
          {safeTags.map((tag, index) => (
            <Badge
              key={`tag-${tag}-${index}`}
              variant="secondary"
              className="flex items-center gap-1"
            >
              {tag}
              <X
                className="h-3 w-3 cursor-pointer hover:text-red-500"
                onClick={() => removeTag(index)}
              />
            </Badge>
          ))}
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={addTag}
            placeholder={safeTags.length === 0 ? placeholder : ""}
            className="flex-1 outline-none bg-transparent min-w-[120px]"
          />
        </div>
        <p className="text-xs text-gray-500">
          Press Enter or comma to add tags. Click the X to remove tags.
        </p>
      </div>
    );
  }
);

// Step Components moved outside to prevent recreation on every render
interface StepProps {
  projectData: ProjectData;
  setProjectData: React.Dispatch<React.SetStateAction<ProjectData>>;
}

type Course = {
  id: string;
  name: string;
  code?: string;
  // add other fields if needed
};

type Staff = {
  id: string;
  name: string;
  role: string;
  // add other fields if needed
};

interface ProjectDetailsStepProps extends StepProps {
  courses?: Course[] | undefined;
  coursesLoading?: boolean;
  coursesError?: boolean;
  coursesErrorObj?: any;
  // staff for selected course — may be undefined while loading / before selection
  courseStaff?: CourseStaff | undefined;
  // | {
  //     instructors: Staff[]; // or a proper type for instructor
  //     tas: Staff[]; // or a proper type for ta
  //   }
  // | undefined;

  staffLoading?: boolean;
  staffError?: boolean;
  staffErrorObj?: any;
}

const ProjectDetailsStep = React.memo(
  ({
    projectData,
    setProjectData,
    courses,
    coursesLoading,
    courseStaff,
    staffLoading,
    staffError,
  }: ProjectDetailsStepProps) => {
    const [formatted, setFormatted] = useState("");
    const [searchParams] = useSearchParams();
    const mode = searchParams.get("mode");
    { console.log(projectData) }
    return (
      <div className="space-y-6">
        {/* Basic Information Card */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-bits-blue" />
              <CardTitle>Basic Information</CardTitle>
            </div>
            <p className="text-sm text-gray-600">
              Configure the basic project information and requirements
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="title">Project Name *</Label>
                <Input
                  id="title"
                  placeholder="Enter project name"
                  value={projectData.title}
                  className="bg-bits-grey-100"
                  onChange={(e) => {
                    const newValue = e.target.value;
                    console.log("after title is set: ", newValue);
                    setProjectData((prev) => ({ ...prev, title: newValue }));
                  }}
                />

              </div>

              <div className="space-y-2">
                <Label htmlFor="courseId">Select Course *</Label>
                <Select
                  value={String(projectData.courseId ?? "")} // always string
                  onValueChange={(value) => {
                    setProjectData(prev => ({
                      ...prev,
                      courseId: value === "__none" ? "" : value,
                    }));
                  }}
                >
                  <SelectTrigger className="bg-bits-grey-100">
                    <SelectValue placeholder="-- Select Course --" />
                  </SelectTrigger>

                  <SelectContent>
                    {coursesLoading ? (
                      <SelectItem value="loading" key="loading" disabled>
                        Loading courses...
                      </SelectItem>
                    ) : (
                      courses?.map(course => (
                        <SelectItem key={course.id} value={String(course.id)}>
                          {course.name} {course.code ? `(${course.code})` : ""}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>

              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Project Description *</Label>
              <Textarea
                id="description"
                placeholder="Brief description of the project"
                className="min-h-[80px] bg-bits-grey-100"
                value={projectData.description}
                onChange={(e) =>
                  setProjectData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="instructor">Instructor *</Label>

                <GenericMultiSelect
                  projectData={projectData}
                  setProjectData={setProjectData}
                  valueToCheck="instructors"
                  items={[
                    ...(projectData.instructors ?? []),
                    ...(courseStaff?.instructors ?? []),
                  ].filter((v, i, a) => a.findIndex((x) => x.id === v.id) === i)}
                  selectedIds={projectData.instructorIds?.map(String) ?? []}
                  onChange={(ids) =>
                    setProjectData((prev) => ({ ...prev, instructorIds: ids }))
                  }
                  label="Instructors"
                />


              </div>
              <div className="space-y-2">
                <Label htmlFor="teachingAssId">Teaching Assistant</Label>

                <GenericMultiSelect
                  projectData={projectData}
                  setProjectData={setProjectData}
                  //items={courseStaff?.tas ?? []}
                  valueToCheck="teachingAssistants"
                  items={[
                    ...(projectData.teachingAssistants ?? []),          // already selected
                    ...(courseStaff?.tas ?? []),         // full list from backend
                  ].filter((v, i, a) => a.findIndex((x) => x.id === v.id) === i)} // remove duplicates

                  selectedIds={projectData.teachingAssId}
                  onChange={(ids) =>
                    setProjectData((prev) => ({ ...prev, teachingAssId: ids }))
                  }
                  label="TAs"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label>Project Type *</Label>
                <RadioGroup
                  value={projectData.projectType}
                  onValueChange={(value: "group" | "individual") =>
                    setProjectData((prev) => ({ ...prev, projectType: value }))
                  }
                  className="flex space-x-6"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="group" id="group" />
                    <Label htmlFor="group">Group Project</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="individual" id="individual" />
                    <Label htmlFor="individual">Individual Project</Label>
                  </div>
                </RadioGroup>
              </div>
              <div className="space-y-2">
                <Label htmlFor="difficulty">Difficulty Level *</Label>
                <Select
                  value={projectData.difficulty_level || ""} // "" or one of Difficulty
                  onValueChange={(value: Difficulty) =>
                    setProjectData((prev) => ({
                      ...prev,
                      difficulty_level: value,
                    }))
                  }
                >
                  <SelectTrigger className="text-bits-grey-500 bg-bits-grey-100 capitalize">
                    {/* Let the SelectValue component handle placeholder/selected display */}
                    <SelectValue placeholder="-- Select Difficulty Level --" />
                  </SelectTrigger>

                  <SelectContent className="capitalize">
                    <SelectItem value="beginner" >beginner</SelectItem>
                    <SelectItem value="intermediate">intermediate</SelectItem>
                    <SelectItem value="advanced">advanced</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Project Metadata Card */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Award className="h-5 w-5 text-bits-blue" />
              <CardTitle>Project Metadata</CardTitle>
            </div>
            <p className="text-sm text-gray-600">
              Additional project information for tracking and organization
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="totalPoints">Total Points *</Label>
                <Input
                  id="totalPoints"
                  type="number"
                  min="0"
                  max="1000"
                  placeholder="100"
                  value={projectData.totalPoints || ""}
                  onChange={(e) =>
                    setProjectData((prev) => ({
                      ...prev,
                      totalPoints: Number(e.target.value) || 0,
                    }))
                  }
                  className="text-bits-grey-500 bg-bits-grey-100"
                />
                <p className="text-xs text-gray-500">
                  Maximum points students can earn for this project
                </p>

              </div>
              <div className="space-y-2">
                <Label htmlFor="estimatedHours">Estimated Time(hours) *</Label>

                <Select
                  value={projectData.estimatedHours}
                  onValueChange={(value) =>
                    setProjectData((prev) => ({
                      ...prev,
                      estimatedHours: value,
                    }))
                  }
                >
                  <SelectTrigger className="bg-bits-grey-100">
                    <SelectValue placeholder="Select estimated time" />
                  </SelectTrigger>
                  <SelectContent>
                    {[1, 2, 3, 4, 5, 6, 7, 8].map((hour) => (
                      <SelectItem key={hour} value={hour}>
                        {hour}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500">
                  Expected time for students to complete this project
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="sandBoxDuration">
                  Sandbox available duration (HH:MM) *
                </Label>
                <TimeDurationInputs
                  onChange={(f) => {
                    setFormatted(f);
                    console.log(f)

                    setProjectData((prev) => ({
                      ...prev,
                      sandbox_time_duration: f,

                    }));

                  }}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="dueDate">Project End Date *</Label>
              <Input
                className="text-bits-grey-500 bg-bits-grey-100"
                type="date"
                id="dueDate"
                placeholder="Enter hours"
                value={projectData.dueDate ? projectData.dueDate.split('T')[0] : ''}
                onChange={(e) =>
                  setProjectData((prev) => ({
                    ...prev,
                    dueDate: e.target.value ? new Date(e.target.value).toISOString() : '',
                  }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="tags">Project Tags</Label>
              <TagInput
                tags={projectData.tags}
                setTags={(tags) =>
                  setProjectData((prev) => ({ ...prev, tags }))
                }
                placeholder="Add tags to categorize this project (e.g., machine-learning, python, beginner)"
              />
              <p className="text-xs text-gray-500">
                Tags help students find relevant projects and improve
                searchability
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Project Overview Card */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-bits-blue" />
              <CardTitle>Project Overview</CardTitle>
            </div>
            <p className="text-sm text-gray-600">
              Provide a comprehensive overview of the project scope and context
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="project_overview">Project Overview *</Label>
              <Textarea
                id="project_overview"
                placeholder="Provide a detailed overview of the project including its purpose, scope, methodology, and expected outcomes. This should give students a comprehensive understanding of what they will be working on and why it's important."
                className="min-h-[120px] bg-bits-grey-100"
                value={projectData.project_overview}
                onChange={(e) =>
                  setProjectData((prev) => ({
                    ...prev,
                    project_overview: e.target.value,
                  }))
                }
              />
              <p className="text-xs text-gray-500">
                This overview will help students understand the broader context
                and significance of the project.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Learning Objectives Card */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-bits-blue" />
              <CardTitle>Learning Objectives</CardTitle>
            </div>
            <p className="text-sm text-gray-600">
              Define what students will learn and achieve through this project
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="learning_objectives">Learning Objectives *</Label>
              <Textarea
                id="learning_objectives"
                placeholder="List the specific learning objectives for this project. What skills, knowledge, or competencies will students gain? 

Example:
• Understand fundamental concepts of machine learning algorithms
• Apply data preprocessing techniques to real-world datasets  
• Develop proficiency in Python libraries (pandas, scikit-learn, matplotlib)
• Create and evaluate predictive models
• Communicate findings through data visualization and reporting"
                className="min-h-[140px] bg-bits-grey-100"
                value={projectData.learning_objectives}
                onChange={(e) =>
                  setProjectData((prev) => ({
                    ...prev,
                    learning_objectives: e.target.value,
                  }))
                }
              />
              <p className="text-xs text-gray-500">
                Use bullet points or numbered lists to clearly define what
                students will learn.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Prerequisites Card */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-bits-blue" />
              <CardTitle>Prerequisites & Requirements</CardTitle>
            </div>
            <p className="text-sm text-gray-600">
              Specify the knowledge, skills, and tools students need before
              starting
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="prerequisites">
                Prerequisites & Requirements *
              </Label>
              <Textarea
                id="prerequisites"
                placeholder="List the prerequisites and requirements for this project:

Technical Prerequisites:
• Basic Python programming knowledge
• Familiarity with Jupyter notebooks
• Understanding of basic statistics

Required Tools/Software:
• Python 3.8+
• Jupyter Notebook or Google Colab
• Required libraries will be provided

Recommended Background:
• Introduction to Data Science course
• Basic mathematics (algebra, statistics)"
                className="min-h-[140px] bg-bits-grey-100"
                value={projectData.prerequisites}
                onChange={(e) =>
                  setProjectData((prev) => ({
                    ...prev,
                    prerequisites: e.target.value,
                  }))
                }
              />
              <p className="text-xs text-gray-500">
                Include technical skills, software requirements, and any
                recommended background knowledge.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
);

const makeId = () =>
  // fallback if crypto.randomUUID is not available
  typeof crypto !== "undefined" &&
    typeof (crypto as any).randomUUID === "function"
    ? (crypto as any).randomUUID()
    : `${Date.now()}-${Math.random().toString(36).slice(2, 9)}`;

const ProjectAssetsStep = React.memo(
  ({ projectData, setProjectData }: StepProps) => {
    const handleUpload = () => {
      console.log("Upload clicked");
      const input = document.createElement("input");
      input.type = "file";
      input.accept = ".csv,.json,.xlsx,.parquet";
      input.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];

        if (file) {
          const kb = Math.round(file.size / 1024);
          const sizeStr =
            kb < 1024 ? `${kb} KB` : `${(kb / 1024).toFixed(2)} MB`;

          const newDataset = {
            id: makeId(),
            name: file.name,
            file: file,
            size: sizeStr,
          };

          console.log("newDatasets (will be set):", newDataset);

          setProjectData((prev) => ({
            ...prev,
            datasets: [...(prev.datasets ?? []), newDataset],
          }));

          console.log("After upload - datasets:", projectData.datasets);
          toast.success(`Uploaded ${file.name}`);
        }
      };
      input.click();
    };

    const handleRemoveNewDataset = (datasetId: string, datasetName: string) => {
      console.log("Removing new dataset:", datasetId, datasetName);
      setProjectData((prev) => ({
        ...prev,
        datasets: prev.datasets?.filter(dataset => dataset.id !== datasetId) ?? []
      }));

      toast.success(`Removed ${datasetName}`);
    };

    const handleRemoveExistingDataset = (datasetId: string, datasetName: string) => {
      console.log("Removing existing dataset:", datasetId, datasetName);
      setProjectData((prev) => ({
        ...prev,
        dataset_s3_url: prev.dataset_s3_url?.filter(dataset => dataset.id !== datasetId) ?? []
      }));

      toast.success(`Removed ${datasetName}`);
    };

    // Debug logs
    console.log("=== RENDER DEBUG ===");
    console.log("projectData:", projectData);
    console.log("datasets count:", projectData.datasets?.length || 0);
    console.log("dataset_s3_url count:", projectData.dataset_s3_url?.length || 0);
    console.log("datasets:", projectData.datasets);
    console.log("dataset_s3_url:", projectData.dataset_s3_url);
    console.log("===================");

    useEffect(() => {
      if (!projectData.projectId) return;
      setProjectData((prev) => {
        const checkpoints = (prev.checkpoints ?? [])?.map((cp) => ({
          ...cp,
          project_id: projectData.id,
        }));
        return { ...prev, checkpoints };
      });
    }, [projectData.projectId]);

    return (
      <div className="space-y-6">
        {/* Dataset Upload Card */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2 ">
              <Upload className="h-5 w-5 text-bits-blue" />
              <CardTitle>Dataset Upload</CardTitle>
            </div>
            <p className="text-sm text-gray-600">
              Upload the datasets that students will work with
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center bg-bits-grey-100">
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-sm text-gray-600 mb-2">
                <span
                  className="font-medium text-bits-blue cursor-pointer"
                  onClick={handleUpload}
                >
                  Click to upload
                </span>{" "}
                or drag and drop
              </p>
              <p className="text-xs text-gray-500">CSV or JSON file</p>
            </div>


            <div className="space-y-2">
              {/* Display newly uploaded files (from datasets) */}
              {projectData?.datasets?.length > 0 && (
                <div className="text-sm font-medium text-gray-700 mb-2">
                  Newly Uploaded Files ({projectData.datasets.length})
                </div>
              )}
              {projectData?.datasets?.map((dataset, index) => (
                <div
                  key={`dataset-new-${dataset.id || index}`}
                  className="flex items-center justify-between p-3 bg-green-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <FileText className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium">{dataset.name}</p>
                      <p className="text-xs text-gray-500">{dataset.size}</p>
                      <p className="text-xs text-blue-500">NEW UPLOAD</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveNewDataset(dataset.id, dataset.name)}
                    className="hover:bg-red-50 hover:text-red-600"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}


              {projectData?.dataset_s3_url?.map((dataset, index) => (
                <div
                  key={`dataset-existing-${dataset.id || index}`}
                  className="flex items-center justify-between p-3 bg-blue-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <FileText className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium">{dataset.metadata?.originalname || 'Unknown file'}</p>
                      <p className="text-xs text-gray-500">
                        {dataset.fileSize ? `${(dataset.fileSize / 1024).toFixed(2)} KB` : 'Unknown size'}
                      </p>
                      <p className="text-xs text-blue-500">FROM S3</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveExistingDataset(dataset.id, dataset.metadata?.originalname || 'Unknown')}
                    className="hover:bg-red-50 hover:text-red-600"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}

              {/* Show message if no files */}
              {(!projectData?.datasets?.length && !projectData?.dataset_s3_url?.length) && (
                <div className="text-center text-gray-500 py-4">
                  No files uploaded yet
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Notebook Content Card - keeping original */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Code className="h-5 w-5 text-bits-blue" />
              <CardTitle>Project Instructions & Notebook</CardTitle>
            </div>
            <p className="text-sm text-gray-600">
              Create the initial notebook content and instructions for students
            </p>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg overflow-hidden">
              <div className="bg-gray-50 px-4 py-2 border-b flex items-center justify-between">
                <h4 className="font-medium">Untitled Notebook-1</h4>
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" size="sm">
                    <Code className="h-4 w-4 mr-1" />
                    Code
                  </Button>
                  <Button variant="ghost" size="sm">
                    <FileText className="h-4 w-4 mr-1" />
                    Markdown
                  </Button>
                  <Button variant="ghost" size="sm">
                    <RotateCcw className="h-4 w-4 mr-1" />
                    Undo
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Download className="h-4 w-4 mr-1" />
                    Redo
                  </Button>
                  <Button variant="ghost" size="sm">
                    <RotateCcw className="h-4 w-4 mr-1" />
                    Reset All
                  </Button>
                </div>
              </div>
              <div className="p-4">
                <Textarea
                  className="min-h-[300px] font-mono text-sm border-0 resize-none focus:ring-0 bg-bits-grey-100"
                  placeholder="# Project Instructions

Write your project instructions and starter code here...

## Objectives
- 

## Getting Started
1. 

## Expected Deliverables
- "
                  value={projectData.instructions}
                  onChange={(e) =>
                    setProjectData((prev) => ({
                      ...prev,
                      instructions: e.target.value,
                    }))
                  }
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
);
interface CheckpointsStepProps extends StepProps {
  selectedCheckpointId: number;
  setSelectedCheckpointId: React.Dispatch<React.SetStateAction<number>>;
  addCheckpoint: () => void;
  updateCheckpoint: (
    id: number,
    field: string,
    value: string | boolean
  ) => void;
}

const ReviewCheckpointsStep = React.memo(
  ({
    projectData,
    selectedCheckpointId,
    setSelectedCheckpointId,
    addCheckpoint,
    updateCheckpoint,
  }: CheckpointsStepProps) => {
    const selectedCheckpoint =
      projectData.checkpoints.find(
        (cp) => cp.checkpoint_number === selectedCheckpointId
      ) || projectData.checkpoints[0];

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
          {/* Checkpoints Sidebar */}
          <Card className="h-fit">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-2">
                <FileText className="h-4 w-4 text-bits-blue" />
                <CardTitle className="text-base">Checkpoints</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-2">
              {projectData?.checkpoints?.map((checkpoint, index) => (
                <div
                  key={`checkpoint-${checkpoint.checkpoint_number || index}`}
                  onClick={() =>
                    setSelectedCheckpointId(checkpoint.checkpoint_number)
                  }
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${checkpoint.checkpoint_number === selectedCheckpointId
                    ? "bg-blue-50 border-blue-200"
                    : "bg-gray-50 border-gray-200 hover:bg-gray-100"
                    }`}
                >
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">
                      {checkpoint.title}
                    </span>
                  </div>
                </div>
              ))}
              <Button
                variant="ghost"
                size="sm"
                onClick={addCheckpoint}
                className="justify-start text-bits-blue hover:text-blue-700 mt-3"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add
              </Button>
            </CardContent>
          </Card>

          {/* Checkpoint Details - Main Content */}
          <div className="lg:col-span-4">
            <Card>
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5 text-bits-blue" />
                  <CardTitle className="text-lg">Checkpoint Details</CardTitle>
                </div>
                <p className="text-sm text-gray-600">
                  Configure the timeline and requirements for each project
                  milestone
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Checkpoint Name</Label>
                  <Input
                    placeholder="Enter Checkpoint name"
                    value={selectedCheckpoint?.title || ""}
                    onChange={(e) =>
                      updateCheckpoint(
                        selectedCheckpointId,
                        "name",
                        e.target.value
                      )
                    }
                    className="bg-bits-grey-100"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Checkpoint Description</Label>
                  <Textarea
                    placeholder="Describe what students need to complete for this checkpoint..."
                    className="min-h-[120px] bg-bits-grey-100"
                    value={selectedCheckpoint?.description || ""}
                    onChange={(e) =>
                      updateCheckpoint(
                        selectedCheckpointId,
                        "description",
                        e.target.value
                      )
                    }
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Start Date</Label>
                    <Input
                      className="bg-bits-grey-100"
                      type="date"
                      value={selectedCheckpoint?.start_date || ""}
                      onChange={(e) =>
                        updateCheckpoint(
                          selectedCheckpointId,
                          "start_date",
                          e.target.value
                        )
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Due Date</Label>
                    <Input
                      className="bg-bits-grey-100"
                      type="date"
                      value={selectedCheckpoint?.due_date || ""}
                      onChange={(e) =>
                        updateCheckpoint(
                          selectedCheckpointId,
                          "due_date",
                          e.target.value
                        )
                      }
                    />
                  </div>
                </div>
                <div>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={selectedCheckpoint?.is_required ?? false}
                      onChange={(e) =>
                        updateCheckpoint(
                          selectedCheckpointId,
                          "is_required",
                          e.target.checked
                        )
                      }
                      className="rounded border-gray-300"
                    />

                    <span className="text-sm">Mandatory to proceed</span>
                  </label>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }
);

interface SettingsStepProps extends StepProps {
  updateRubricItem: (
    index: number,
    field: string,
    value: string | number
  ) => void;
  totalMarks: number;
  isValidMarks: boolean;
  marksError: string;
}

const ProjectSettingsStep = React.memo(
  ({
    projectData,
    setProjectData,
    updateRubricItem,
    totalMarks,
    isValidMarks,
    marksError,
  }: SettingsStepProps) => {
    const [rubricVariation, setRubricVariation] = useState<
      "compact" | "standard" | "detailed"
    >("detailed");

    useEffect(() => {
      if (!projectData.id) return;

      setProjectData((prev) => {
        const updated = (prev.gradingRubric ?? [])?.map((ru) => ({
          ...ru,
          project_id: prev.id,
        }));
        return { ...prev, gradingRubric: updated };
      });
    }, [projectData.id]);

    return (
      <div className="space-y-6">
        {/* Sandbox Resource Configuration */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Terminal className="h-5 w-5 text-blue-600" />
              <CardTitle>Sandbox Resource Configuration</CardTitle>
            </div>
            <p className="text-sm text-gray-600">
              Configure the computing resources and environment for this project
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="sandboxResourceProfile">Resource Profile *</Label>
              <Select
                value={projectData.sandboxResourceProfile}
                onValueChange={(value) =>
                  setProjectData((prev) => ({
                    ...prev,
                    sandboxResourceProfile: value,
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select resource profile" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="basic-python">
                    Basic Python Environment
                  </SelectItem>
                  <SelectItem value="data-science-standard">
                    Data Science Standard
                  </SelectItem>
                  <SelectItem value="machine-learning-gpu">
                    Machine Learning with GPU
                  </SelectItem>
                  <SelectItem value="deep-learning-advanced">
                    Deep Learning Advanced
                  </SelectItem>
                  <SelectItem value="custom-profile">Custom Profile</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">
                Choose the appropriate computing resources based on project
                requirements
              </p>
            </div>

            {/* Resource Profile Details */}
            {projectData.sandboxResourceProfile && (
              <div className="p-4 bg-gray-50 rounded-lg space-y-3">
                <div className="flex items-center space-x-2">
                  <Info className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-gray-900">
                    {projectData.sandboxResourceProfile === "basic-python" &&
                      "Basic Python Environment"}
                    {projectData.sandboxResourceProfile ===
                      "data-science-standard" && "Data Science Standard"}
                    {projectData.sandboxResourceProfile ===
                      "machine-learning-gpu" && "Machine Learning with GPU"}
                    {projectData.sandboxResourceProfile ===
                      "deep-learning-advanced" && "Deep Learning Advanced"}
                    {projectData.sandboxResourceProfile === "custom-profile" &&
                      "Custom Profile"}
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">CPU: </span>
                    <span className="text-gray-600">
                      {projectData.sandboxResourceProfile === "basic-python" &&
                        "2 vCPUs"}
                      {projectData.sandboxResourceProfile ===
                        "data-science-standard" && "4 vCPUs"}
                      {projectData.sandboxResourceProfile ===
                        "machine-learning-gpu" && "8 vCPUs"}
                      {projectData.sandboxResourceProfile ===
                        "deep-learning-advanced" && "16 vCPUs"}
                      {projectData.sandboxResourceProfile ===
                        "custom-profile" && "Configurable"}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Memory: </span>
                    <span className="text-gray-600">
                      {projectData.sandboxResourceProfile === "basic-python" &&
                        "4 GB"}
                      {projectData.sandboxResourceProfile ===
                        "data-science-standard" && "16 GB"}
                      {projectData.sandboxResourceProfile ===
                        "machine-learning-gpu" && "32 GB"}
                      {projectData.sandboxResourceProfile ===
                        "deep-learning-advanced" && "64 GB"}
                      {projectData.sandboxResourceProfile ===
                        "custom-profile" && "Configurable"}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Storage: </span>
                    <span className="text-gray-600">
                      {projectData.sandboxResourceProfile === "basic-python" &&
                        "20 GB"}
                      {projectData.sandboxResourceProfile ===
                        "data-science-standard" && "50 GB"}
                      {projectData.sandboxResourceProfile ===
                        "machine-learning-gpu" && "100 GB"}
                      {projectData.sandboxResourceProfile ===
                        "deep-learning-advanced" && "200 GB"}
                      {projectData.sandboxResourceProfile ===
                        "custom-profile" && "Configurable"}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">GPU: </span>
                    <span className="text-gray-600">
                      {projectData.sandboxResourceProfile === "basic-python" &&
                        "None"}
                      {projectData.sandboxResourceProfile ===
                        "data-science-standard" && "None"}
                      {projectData.sandboxResourceProfile ===
                        "machine-learning-gpu" && "1x NVIDIA T4"}
                      {projectData.sandboxResourceProfile ===
                        "deep-learning-advanced" && "2x NVIDIA V100"}
                      {projectData.sandboxResourceProfile ===
                        "custom-profile" && "Configurable"}
                    </span>
                  </div>
                </div>

                <div className="pt-2 border-t border-gray-200">
                  <div className="text-sm">
                    <span className="font-medium text-gray-700">
                      Pre-installed Libraries:{" "}
                    </span>
                    <span className="text-gray-600">
                      {projectData.sandboxResourceProfile === "basic-python" &&
                        "Python 3.9, Jupyter, numpy, pandas, matplotlib"}
                      {projectData.sandboxResourceProfile ===
                        "data-science-standard" &&
                        "Python 3.9, Jupyter, pandas, numpy, matplotlib, seaborn, plotly, scikit-learn"}
                      {projectData.sandboxResourceProfile ===
                        "machine-learning-gpu" &&
                        "Python 3.9, Jupyter, pandas, numpy, matplotlib, seaborn, plotly, scikit-learn, tensorflow, pytorch"}
                      {projectData.sandboxResourceProfile ===
                        "deep-learning-advanced" &&
                        "Python 3.9, Jupyter, Full ML/DL stack, CUDA libraries, Distributed training tools"}
                      {projectData.sandboxResourceProfile ===
                        "custom-profile" &&
                        "Customizable environment based on requirements"}
                    </span>
                  </div>
                </div>

                {projectData.sandboxResourceProfile === "custom-profile" && (
                  <div className="pt-3 border-t border-gray-200">
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-blue-600 border-blue-200 hover:bg-blue-50"
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      Configure Custom Profile
                    </Button>
                  </div>
                )}
              </div>
            )}

            <div className="flex items-start space-x-2 p-3 bg-blue-50 rounded-lg">
              <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-700">
                <p className="font-medium mb-1">Resource Profile Guidelines</p>
                <ul className="text-xs space-y-1">
                  <li>
                    • <strong>Basic Python:</strong> Simple data manipulation
                    and basic analysis
                  </li>
                  <li>
                    • <strong>Data Science Standard:</strong> Statistical
                    analysis, visualization, basic ML
                  </li>
                  <li>
                    • <strong>ML with GPU:</strong> Machine learning with
                    moderate computational needs
                  </li>
                  <li>
                    • <strong>Deep Learning Advanced:</strong> Complex neural
                    networks and large datasets
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submission Settings */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Clipboard className="h-5 w-5 text-bits-blue" />
              <CardTitle>Submission Settings</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-row space-x-6 mb-4">
              <div className="flex flex-col mb-4 gap-2">
                <Label>Allow Late Submissions</Label>
                <Switch
                  checked={projectData.lateSubmissionsAllowed}
                  onCheckedChange={(checked) =>
                    setProjectData((prev) => ({
                      ...prev,
                      lateSubmissionsAllowed: checked,
                    }))
                  }
                />
              </div>
              <div className="flex flex-col mb-4 gap-2">
                <Label>Days allowed after due date</Label>
                <Select
                  value={String(projectData.acceptForDays)}
                  onValueChange={(value) =>
                    setProjectData((prev) => ({
                      ...prev,
                      acceptForDays: Number(value),
                    }))
                  }
                >
                  <SelectTrigger className="bg-bits-grey-100">
                    <SelectValue placeholder="Select numbers" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1</SelectItem>
                    <SelectItem value="2">2</SelectItem>
                    <SelectItem value="3">3</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label>Maximum Submission</Label>
                <Select
                  value={String(projectData.maxSubmissions)}
                  onValueChange={(value) =>
                    setProjectData((prev) => ({
                      ...prev,
                      maxSubmissions: Number(value),
                    }))
                  }
                >
                  <SelectTrigger className="bg-bits-grey-100">
                    <SelectValue placeholder="Select numbers" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1</SelectItem>
                    <SelectItem value="2">2</SelectItem>
                    <SelectItem value="3">3</SelectItem>
                    <SelectItem value="unlimited">Unlimited</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Grading Rubric */}
        <Card>
          <CardHeader className="border-b border-gray-200 bg-white p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <FileText className="h-4 w-4 text-blue-600" />
                <div>
                  <CardTitle className="text-base">Grading Rubric</CardTitle>
                  <p className="text-xs text-gray-500">
                    Define grading criteria for checkpoints
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                {/* Design Variation Selector */}
                <Select
                  value={rubricVariation}
                  onValueChange={(value: "compact" | "standard" | "detailed") =>
                    setRubricVariation(value)
                  }
                >
                  <SelectTrigger className="w-24 h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="compact">Compact</SelectItem>
                    <SelectItem value="standard">Standard</SelectItem>
                    <SelectItem value="detailed">Detailed</SelectItem>
                  </SelectContent>
                </Select>
                <div className="text-right">
                  <div className="text-xs text-gray-500">Total Points</div>
                  <div
                    className={`text-lg font-semibold ${isValidMarks
                      ? "text-green-600"
                      : totalMarks > 100
                        ? "text-red-600"
                        : "text-yellow-600"
                      }`}
                  >
                    {totalMarks}/100
                  </div>
                </div>
                <div
                  className={`w-2 h-8 rounded-full ${isValidMarks
                    ? "bg-green-500"
                    : totalMarks > 100
                      ? "bg-red-500"
                      : "bg-yellow-500"
                    }`}
                ></div>
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            {/* Compact Variation */}
            {rubricVariation === "compact" && (
              <div className="p-6 space-y-4">
                {projectData?.gradingRubric?.map((item, checkpointIndex) => (
                  <div
                    key={`compact-rubric-checkpoint-${checkpointIndex}`}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-medium text-gray-900">
                        {item.checkpoint}
                      </h3>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newCriteria = {
                            name: `criteria-${Date.now()}`,
                            description: "",
                            points: 0,
                          };
                          const updatedRubric = [...projectData.gradingRubric];
                          if (!updatedRubric[checkpointIndex].criteria) {
                            updatedRubric[checkpointIndex].criteria = [];
                          }
                          updatedRubric[checkpointIndex].criteria.push(
                            newCriteria
                          );
                          setProjectData((prev) => ({
                            ...prev,
                            gradingRubric: updatedRubric,
                          }));
                        }}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add
                      </Button>
                    </div>

                    <div className="space-y-2">
                      {Array.isArray(item.criteria)
                        ? item?.criteria?.map((criteria, criteriaIndex) => (
                          <div
                            key={`compact-criteria-${criteria.name || criteriaIndex
                              }`}
                            className="flex items-center gap-3 p-2 bg-gray-50 rounded"
                          >
                            <span className="text-sm text-gray-500 w-6">
                              {criteriaIndex + 1}.
                            </span>
                            <Input
                              placeholder="Criteria description"
                              value={criteria.description || ""}
                              onChange={(e) => {
                                const updatedRubric = [
                                  ...projectData.gradingRubric,
                                ];
                                updatedRubric[checkpointIndex].criteria[
                                  criteriaIndex
                                ]?.description = e.target.value;
                                setProjectData((prev) => ({
                                  ...prev,
                                  gradingRubric: updatedRubric,
                                }));
                              }}
                              className="flex-1 text-sm"
                            />
                            <Select
                              value={criteria.points?.toString() || "0"}
                              onValueChange={(value) => {
                                const updatedRubric = [
                                  ...projectData.gradingRubric,
                                ];
                                updatedRubric[checkpointIndex].criteria[
                                  criteriaIndex
                                ]?.points = parseInt(value);
                                setProjectData((prev) => ({
                                  ...prev,
                                  gradingRubric: updatedRubric,
                                }));
                              }}
                            >
                              <SelectTrigger className="w-16 text-sm">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {[
                                  0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50,
                                ]?.map((points) => (
                                  <SelectItem
                                    key={points}
                                    value={points.toString()}
                                  >
                                    {points}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                const updatedRubric = [
                                  ...projectData.gradingRubric,
                                ];
                                updatedRubric[checkpointIndex].criteria =
                                  updatedRubric[
                                    checkpointIndex
                                  ].criteria.filter(
                                    (_, i) => i !== criteriaIndex
                                  );
                                setProjectData((prev) => ({
                                  ...prev,
                                  gradingRubric: updatedRubric,
                                }));
                              }}
                              className="h-8 w-8 p-0 text-red-500 hover:bg-red-50"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))
                        : []}

                      {(!Array.isArray(item.criteria) ||
                        item.criteria.length === 0) && (
                          <div className="text-center py-4 text-gray-500 text-sm">
                            No criteria defined yet
                          </div>
                        )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Standard Variation */}
            {rubricVariation === "standard" && (
              <div className="space-y-6 p-6">
                {projectData.gradingRubric.map((item, checkpointIndex) => (
                  <div
                    key={`standard-rubric-checkpoint-${checkpointIndex}`}
                    className="border border-gray-200 rounded-lg overflow-hidden"
                  >
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold text-gray-900">
                            {item.checkpoint}
                          </h3>
                          <p className="text-sm text-gray-600 mt-1">
                            {Array.isArray(item.criteria)
                              ? item.criteria.length
                              : 0}{" "}
                            criteria •{" "}
                            {Array.isArray(item.criteria)
                              ? item.criteria.reduce(
                                (sum, criteria) =>
                                  sum + (criteria.points || 0),
                                0
                              )
                              : item.total_points || 0}{" "}
                            points
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          onClick={() => {
                            const newCriteria = {
                              name: `criteria-${Date.now()}`,
                              description: "",
                              points: 0,
                            };
                            const updatedRubric = [
                              ...projectData.gradingRubric,
                            ];
                            if (!updatedRubric[checkpointIndex].criteria) {
                              updatedRubric[checkpointIndex].criteria = [];
                            }
                            updatedRubric[checkpointIndex].criteria.push(
                              newCriteria
                            );
                            setProjectData((prev) => ({
                              ...prev,
                              gradingRubric: updatedRubric,
                            }));
                          }}
                          className="text-blue-600 border-blue-200 hover:bg-blue-50"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Criteria
                        </Button>
                      </div>
                    </div>

                    <div className="p-4">
                      <div className="space-y-4">
                        {Array.isArray(item.criteria)
                          ? item.criteria.map((criteria, criteriaIndex) => (
                            <div
                              key={`standard-criteria-${criteria.name || criteriaIndex
                                }`}
                              className="flex items-start gap-4 p-4 border border-gray-200 rounded-lg bg-white hover:shadow-sm transition-shadow"
                            >
                              <div className="w-8 h-8 flex items-center justify-center bg-blue-100 text-blue-700 rounded-lg font-medium text-sm">
                                {criteriaIndex + 1}
                              </div>
                              <div className="flex-1">
                                <Input
                                  placeholder="Describe the grading criteria..."
                                  value={criteria.description || ""}
                                  onChange={(e) => {
                                    const updatedRubric = [
                                      ...projectData.gradingRubric,
                                    ];
                                    updatedRubric[checkpointIndex].criteria[
                                      criteriaIndex
                                    ].description = e.target.value;
                                    setProjectData((prev) => ({
                                      ...prev,
                                      gradingRubric: updatedRubric,
                                    }));
                                  }}
                                  className="border-gray-200 focus:border-blue-500"
                                />
                              </div>
                              <div className="flex items-center gap-2">
                                <Select
                                  value={criteria.points?.toString() || "0"}
                                  onValueChange={(value) => {
                                    const updatedRubric = [
                                      ...projectData.gradingRubric,
                                    ];
                                    updatedRubric[checkpointIndex].criteria[
                                      criteriaIndex
                                    ]?.points = parseInt(value);
                                    setProjectData((prev) => ({
                                      ...prev,
                                      gradingRubric: updatedRubric,
                                    }));
                                  }}
                                >
                                  <SelectTrigger className="w-20">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {[
                                      0, 5, 10, 15, 20, 25, 30, 35, 40, 45,
                                      50,
                                    ].map((points) => (
                                      <SelectItem
                                        key={points}
                                        value={points.toString()}
                                      >
                                        {points}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    const updatedRubric = [
                                      ...projectData.gradingRubric,
                                    ];
                                    updatedRubric[checkpointIndex].criteria =
                                      updatedRubric[
                                        checkpointIndex
                                      ].criteria.filter(
                                        (_, i) => i !== criteriaIndex
                                      );
                                    setProjectData((prev) => ({
                                      ...prev,
                                      gradingRubric: updatedRubric,
                                    }));
                                  }}
                                  className="h-9 w-9 p-0 text-red-500 hover:bg-red-50"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          ))
                          : []}

                        {(!Array.isArray(item.criteria) ||
                          item.criteria.length === 0) && (
                            <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
                              <FileText className="h-8 w-8 text-gray-400 mx-auto mb-3" />
                              <p className="text-gray-600 mb-3">
                                No criteria defined for this checkpoint
                              </p>
                              <Button
                                variant="outline"
                                onClick={() => {
                                  const newCriteria = {
                                    name: `criteria-${Date.now()}`,
                                    description: "",
                                    points: 0,
                                  };
                                  const updatedRubric = [
                                    ...projectData.gradingRubric,
                                  ];
                                  if (!updatedRubric[checkpointIndex].criteria) {
                                    updatedRubric[checkpointIndex].criteria = [];
                                  }
                                  updatedRubric[checkpointIndex].criteria.push(
                                    newCriteria
                                  );
                                  setProjectData((prev) => ({
                                    ...prev,
                                    gradingRubric: updatedRubric,
                                  }));
                                }}
                                className="text-blue-600 border-blue-200 hover:bg-blue-50"
                              >
                                <Plus className="h-4 w-4 mr-2" />
                                Add First Criteria
                              </Button>
                            </div>
                          )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Detailed Variation (Current Implementation) */}
            {rubricVariation === "detailed" && (
              <div className="space-y-0">
                {projectData.gradingRubric.map((item, checkpointIndex) => (
                  <div
                    key={`rubric-checkpoint-${checkpointIndex}`}
                    className="border-b border-gray-100 last:border-b-0"
                  >
                    {/* Checkpoint Header */}
                    <div className="bg-gradient-to-r from-blue-50 to-gray-50 p-6 border-l-4 border-blue-500">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-3">
                            <GripVertical className="h-5 w-5 text-gray-400" />
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900">
                                {item.checkpoint}
                              </h3>
                              <div className="flex items-center space-x-4 mt-1">
                                <span className="text-sm text-gray-500">
                                  {Array.isArray(item.criteria)
                                    ? item.criteria.length
                                    : 0}{" "}
                                  criteria •{" "}
                                  {Array.isArray(item.criteria)
                                    ? item.criteria.reduce(
                                      (sum, criteria) =>
                                        sum + (criteria.points || 0),
                                      0
                                    )
                                    : item.total_points || 0}{" "}
                                  points
                                </span>
                                <div className="flex items-center space-x-1">
                                  {[
                                    ...Array(
                                      Math.ceil(
                                        (Array.isArray(item.criteria)
                                          ? item.criteria.reduce(
                                            (sum, criteria) =>
                                              sum + (criteria.points || 0),
                                            0
                                          )
                                          : item.total_points || 0) / 10
                                      )
                                    ),
                                  ].map((_, i) => (
                                    <div
                                      key={i}
                                      className="w-2 h-2 bg-blue-300 rounded-full"
                                    ></div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          onClick={() => {
                            const newCriteria = {
                              name: `criteria-${Date.now()}`,
                              description: "",
                              points: 0,
                            };
                            const updatedRubric = [
                              ...projectData.gradingRubric,
                            ];
                            if (!updatedRubric[checkpointIndex].criteria) {
                              updatedRubric[checkpointIndex].criteria = [];
                            }
                            updatedRubric[checkpointIndex].criteria.push(
                              newCriteria
                            );
                            setProjectData((prev) => ({
                              ...prev,
                              gradingRubric: updatedRubric,
                            }));
                          }}
                          className="bg-white border-blue-200 text-blue-600 hover:bg-blue-50 shadow-sm"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Criteria
                        </Button>
                      </div>
                    </div>

                    {/* Criteria List */}
                    <div className="p-6 bg-white">
                      <div className="space-y-4">
                        {Array.isArray(item.criteria)
                          ? item.criteria.map((criteria, criteriaIndex) => (
                            <div
                              key={`criteria-${criteria.name || criteriaIndex
                                }`}
                              className="group"
                            >
                              <div className="flex items-start gap-6 p-5 rounded-xl border-2 border-gray-100 hover:border-blue-200 hover:shadow-md transition-all duration-300 bg-gradient-to-r from-white to-gray-50">
                                {/* Criteria Number & Drag Handle */}
                                <div className="flex flex-col items-center space-y-2">
                                  <div className="w-10 h-10 flex items-center justify-center bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-xl shadow-sm font-semibold">
                                    {criteriaIndex + 1}
                                  </div>
                                  <GripVertical className="h-4 w-4 text-gray-300 opacity-0 group-hover:opacity-100 transition-opacity" />
                                </div>

                                {/* Criteria Content */}
                                <div className="flex-1 space-y-3">
                                  <div className="flex items-center space-x-2">
                                    <label className="text-sm font-medium text-gray-700">
                                      Description
                                    </label>
                                    <div className="flex-1 h-px bg-gray-200"></div>
                                  </div>
                                  <Input
                                    placeholder="Describe the grading criteria in detail (e.g., Data preprocessing, cleaning, and validation techniques)"
                                    value={criteria.description || ""}
                                    onChange={(e) => {
                                      const updatedRubric = [
                                        ...projectData.gradingRubric,
                                      ];
                                      updatedRubric[checkpointIndex].criteria[
                                        criteriaIndex
                                      ].description = e.target.value;
                                      setProjectData((prev) => ({
                                        ...prev,
                                        gradingRubric: updatedRubric,
                                      }));
                                    }}
                                    className="border-gray-200 focus:border-blue-500 focus:ring-blue-500 bg-white shadow-sm"
                                  />
                                </div>

                                {/* Points & Actions */}
                                <div className="flex flex-col items-center space-y-3">
                                  <div className="text-center">
                                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      Points
                                    </label>
                                    <div className="mt-1">
                                      <Select
                                        value={
                                          criteria.points?.toString() || "0"
                                        }
                                        onValueChange={(value) => {
                                          const updatedRubric = [
                                            ...projectData.gradingRubric,
                                          ];
                                          updatedRubric[
                                            checkpointIndex
                                          ].criteria[criteriaIndex]?.points =
                                            parseInt(value);
                                          setProjectData((prev) => ({
                                            ...prev,
                                            gradingRubric: updatedRubric,
                                          }));
                                        }}
                                      >
                                        <SelectTrigger className="w-20 text-center font-semibold">
                                          <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                          {[
                                            0, 5, 10, 15, 20, 25, 30, 35, 40,
                                            45, 50,
                                          ].map((points) => (
                                            <SelectItem
                                              key={points}
                                              value={points.toString()}
                                            >
                                              {points}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>
                                  </div>

                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      const updatedRubric = [
                                        ...projectData.gradingRubric,
                                      ];
                                      updatedRubric[
                                        checkpointIndex
                                      ].criteria = updatedRubric[
                                        checkpointIndex
                                      ].criteria.filter(
                                        (_, i) => i !== criteriaIndex
                                      );
                                      setProjectData((prev) => ({
                                        ...prev,
                                        gradingRubric: updatedRubric,
                                      }));
                                    }}
                                    className="opacity-0 group-hover:opacity-100 h-9 w-9 p-0 text-gray-400 hover:text-red-500 hover:bg-red-50 transition-all"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ))
                          : []}

                        {/* Enhanced Empty State */}
                        {(!Array.isArray(item.criteria) ||
                          item.criteria.length === 0) && (
                            <div className="text-center py-12 bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl border-2 border-dashed border-gray-200">
                              <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                                <FileText className="h-8 w-8 text-blue-500" />
                              </div>
                              <h4 className="font-semibold text-gray-900 mb-2">
                                No criteria defined yet
                              </h4>
                              <p className="text-gray-600 mb-4 max-w-md mx-auto">
                                Add grading criteria to define how students will
                                be evaluated for this checkpoint
                              </p>
                              <Button
                                variant="outline"
                                onClick={() => {
                                  const newCriteria = {
                                    name: `criteria-${Date.now()}`,
                                    description: "",
                                    points: 0,
                                  };
                                  const updatedRubric = [
                                    ...projectData.gradingRubric,
                                  ];
                                  if (!updatedRubric[checkpointIndex].criteria) {
                                    updatedRubric[checkpointIndex].criteria = [];
                                  }
                                  updatedRubric[checkpointIndex].criteria.push(
                                    newCriteria
                                  );
                                  setProjectData((prev) => ({
                                    ...prev,
                                    gradingRubric: updatedRubric,
                                  }));
                                }}
                                className="border-blue-200 text-blue-600 hover:bg-blue-50"
                              >
                                <Plus className="h-4 w-4 mr-2" />
                                Add First Criteria
                              </Button>
                            </div>
                          )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Enhanced Status & Validation */}
            <div className="p-6 bg-gradient-to-r from-gray-50 to-blue-50 border-t border-gray-100">
              {!isValidMarks && (
                <div
                  className={`p-5 rounded-xl border-2 mb-4 ${totalMarks > 100
                    ? "bg-gradient-to-r from-red-50 to-red-100 border-red-200"
                    : "bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200"
                    }`}
                >
                  <div className="flex items-start gap-4">
                    <div
                      className={`p-2 rounded-lg ${totalMarks > 100 ? "bg-red-200" : "bg-yellow-200"
                        }`}
                    >
                      <AlertTriangle
                        className={`h-6 w-6 ${totalMarks > 100 ? "text-red-600" : "text-yellow-600"
                          }`}
                      />
                    </div>
                    <div className="flex-1">
                      <h4
                        className={`font-semibold mb-1 ${totalMarks > 100 ? "text-red-800" : "text-yellow-800"
                          }`}
                      >
                        {totalMarks > 100
                          ? "Points Exceed Maximum"
                          : "Rubric Incomplete"}
                      </h4>
                      <p
                        className={`text-sm ${totalMarks > 100 ? "text-red-700" : "text-yellow-700"
                          }`}
                      >
                        {marksError}
                      </p>
                      <div className="mt-3 flex items-center space-x-4">
                        <div className="text-sm">
                          <span className="font-medium">Current total:</span>{" "}
                          {totalMarks} points
                        </div>
                        <div className="text-sm">
                          <span className="font-medium">Target:</span> 100
                          points
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {isValidMarks && (
                <div className="p-5 rounded-xl bg-gradient-to-r from-green-50 to-emerald-100 border-2 border-green-200 mb-4">
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-green-200 rounded-lg">
                      <CheckCircle className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-green-800 mb-1">
                        Perfect Rubric!
                      </h4>
                      <p className="text-sm text-green-700">
                        Your grading rubric is complete and correctly totals 100
                        points across all checkpoints.
                      </p>
                      <div className="mt-3 flex items-center space-x-4">
                        <div className="text-sm text-green-600">
                          <span className="font-medium">Total criteria:</span>{" "}
                          {projectData.gradingRubric.reduce(
                            (total, item) =>
                              total +
                              (Array.isArray(item.criteria)
                                ? item.criteria.length
                                : 0),
                            0
                          )}
                        </div>
                        <div className="text-sm text-green-600">
                          <span className="font-medium">Checkpoints:</span>{" "}
                          {projectData.gradingRubric.length}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Rubric Summary */}
              <div className="grid grid-cols-3 gap-4">
                {projectData.gradingRubric.map((item, index) => (
                  <div
                    key={index}
                    className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm"
                  >
                    <h5
                      className="font-medium text-gray-900 text-sm mb-2 truncate"
                      title={item.checkpoint}
                    >
                      {item.checkpoint}
                    </h5>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">
                        {Array.isArray(item.criteria)
                          ? item.criteria.length
                          : 0}{" "}
                        criteria
                      </span>
                      <span className="font-semibold text-blue-600">
                        {Array.isArray(item.criteria)
                          ? item.criteria.reduce(
                            (sum, criteria) => sum + (criteria.points || 0),
                            0
                          )
                          : item.total_points || 0}{" "}
                        pts
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
);

export default function CreateProjectPage({
  templateData,
}: CreateProjectPageProps) {
  const { goBack, navigateTo } = useNavigation();
  const [searchParams] = useSearchParams();
  const mode = searchParams.get("mode");
  const { projectId } = useParams()
  const [projectDetails, setProjectDetails] = useState<EnhancedProject | null>(null);

  const { data, isLoading, error } = useQuery({
    queryKey: ["project", projectId],
    queryFn: () => getProjectById(projectId || ""),
    enabled: !!projectId && mode === "edit",  // condition moved here
  });

  if (projectId && mode === "edit") {
    console.log(data, isLoading, error);
  }
  useEffect(() => {
    if (!data) return;

    if (mode === "edit") {
      setProjectData(prev => {
        const updated: ProjectData = { ...prev, ...data };

        if (!prev.courseId && data.course?.code) {
          const matchedCourse = courses?.find(
            c =>
              c.code?.trim().toLowerCase() ===
              data.course.code?.trim().toLowerCase()
          );
          if (matchedCourse) {
            updated.courseId = matchedCourse.id;
          }
        }

        updated.estimatedHours = prev.estimated_hours;
        updated.projectType = prev.projectType ?? data.type ?? "";
        updated.dueDate = prev.due_date
        if (data.instructors && !prev.instructorIds?.length) {
          updated.instructorIds = data.instructors.map(i => String(i.id));
        }
        if (data.teachingAssistants && !prev.teachingAssId?.length) {
          updated.teachingAssId = data.teachingAssistants.map(i => String(i.id));
        }

        return updated;
      });
    }
  }, [data, mode]);

  const [currentStep, setCurrentStep] = useState(1);
  const [selectedCheckpointId, setSelectedCheckpointId] = useState(1);
  const [showTemplateDialog, setShowTemplateDialog] = useState(false);
  const [templateName, setTemplateName] = useState("");
  const [templateDescription, setTemplateDescription] = useState("");
  const [savingTemplate, setSavingTemplate] = useState(false);
  console.log(templateData, mode)

  const createProjectMutation = useCreateProjectMutation();
  const uploadDataSetMutation = useDatasetMutation();
  const editProjectMutation = useEditProjectMutation();
  const createCheckpointMutation = useCreateCheckPointMutation();
  const createRubricMutation = useCreateRubricMutation();

  // fetch courses with React Query (inside component body — valid)
  const {
    data: courses,
    isLoading: coursesLoading,
    isError: coursesError,
    error: coursesErrorObj,
  } = useCoursesQuery(true);

  const getDefaultProjectData = (): ProjectData => ({
    // Step 1: Project Details
    id: "",
    title: "",
    // categoryId: "category abc",
    courseId: "",
    description: "",
    instructorIds: [],
    teachingAssId: [],
    projectType: "individual",
    difficulty_level: "advanced",
    totalPoints: 100,
    estimatedHours: "",
    tags: [],
    project_overview: "",
    learning_objectives: "",
    prerequisites: "",
    isScreen: 1,
    sandBoxDuration: "",
    dueDate: "",

    // Step 2: Assets & Instructions
    projectId: "",
    instructions: "",
    datasets: [],

    // Step 3: Review Checkpoints
    checkpoints: [
      {
        project_id: "",
        isScreen: 3,
        title: "Checkpoint 1",
        description: "",
        checkpoint_number: 1,

        start_date: "",
        due_date: "",
        weight_percentage: 0,
        is_required: false,
        status: "draft",
        metadata: {},
      },
    ],

    // Step 4: Project Settings
    sandboxResourceProfile: "",
    maxSubmissions: 0,
    lateSubmissionsAllowed: false,

    acceptForDays: 0,
    gradingRubric: [
      {
        checkpoint: `Checkpoint 1`,
        criteria: [
          { name: `criteria-${Date.now()}`, description: "", points: 0 },
        ],
        total_points: 0,
        project_id: "",
        title: `Checkpoint 1`,
        description: "temp desc",
        checkpoint_mapping: {},
        grading_scale: {},
        is_template: false,
        template_name: "",
        metadata: {},
      },
    ],
  });

  const [projectData, setProjectData] = useState<ProjectData>(
    getDefaultProjectData()
  );

  const {
    data: courseStaff,
    isLoading: staffLoading,
    isError: staffError,
    error: staffErrorObj,
  } = useStaffMemberQuery(projectData.courseId, true);

  useEffect(() => {
    if (mode === "template" && templateData) {
      // Pre-populate with template data
      const templateProjectData = {
        ...templateData.projectData,
        // Ensure arrays are always initialized
        tags: templateData.projectData.tags || [],
        datasets: templateData.projectData.datasets || [],
        // checkpoints: templateData.projectData.checkpoints || [],
        gradingRubric: templateData.projectData.gradingRubric || [],
        // Keep empty for instance-specific fields unless user wants to pre-fill them
        projectName: "",
        instructor: "",
        teachingAssistant: "",
        // Clear dates for checkpoints
        checkpoints: (templateData.projectData.checkpoints || []).map((cp) => ({
          ...cp,
          start_date: "",
          end_date: "",
        })),
      };

      setProjectData(templateProjectData);

      // Set the first checkpoint as selected
      if (templateProjectData.checkpoints.length > 0) {
        // setSelectedCheckpointId(templateProjectData.checkpoints[0].id);
      }

      // Show success message
      toast.success(`Template "${templateData.name}" loaded successfully!`);

      // Update template usage count
      updateTemplateUsageCount(templateData.id);
    }
  }, [mode, templateData]);

  const updateTemplateUsageCount = (templateId: string) => {
    try {
      const existingTemplates = JSON.parse(
        localStorage.getItem("projectTemplates") || "[]"
      );
      const updatedTemplates = existingTemplates.map((template: TemplateData) =>
        template.id === templateId
          ? { ...template, usageCount: template.usageCount + 1 }
          : template
      );
      localStorage.setItem(
        "projectTemplates",
        JSON.stringify(updatedTemplates)
      );
    } catch (error) {
      console.error("Failed to update template usage count:", error);
    }
  };

  const steps = [
    {
      number: 1,
      title: "Project Details",
      subtitle: "Basic information and requirements",
    },
    {
      number: 2,
      title: "Create Project Assets & Instructions",
      subtitle: "Upload datasets and notebook content",
    },
    {
      number: 3,
      title: "Add Review Checkpoints",
      subtitle: "Set up project milestones and timeline",
    },
    {
      number: 4,
      title: "Project Settings",
      subtitle: "Configure grading and submission settings",
    },
  ];

  const totalMarks = useMemo(() => {
    return projectData?.gradingRubric?.reduce(
      (sum, item) => sum + (item.total_points || 0),
      0
    );
  }, [projectData.gradingRubric]);

  useEffect(() => {
    const current = projectData.gradingRubric || [];

    const updated = current.map((item) => {
      const sum = Array.isArray(item.criteria)
        ? item.criteria.reduce((s, c) => s + (Number(c?.points) || 0), 0)
        : Number(item.total_points) || 0;
      return { ...item, total_points: sum };
    });

    const changed = updated.some(
      (it, idx) => (current[idx]?.total_points || 0) !== (it.total_points || 0)
    );

    if (changed) {
      setProjectData((prev) => ({ ...prev, gradingRubric: updated }));
    }
  }, [projectData.gradingRubric]);

  useEffect(() => {
  }, [projectData?.gradingRubric]);

  const isValidMarks = totalMarks === 100;
  const marksError =
    totalMarks > 100
      ? `Total marks exceed 100 by ${totalMarks - 100} marks`
      : totalMarks < 100
        ? `Total marks are ${100 - totalMarks} marks short of 100`
        : "";

  const canProceed = currentStep < 5 || (currentStep === 5 && isValidMarks);

  const getPageTitle = () => {
    switch (mode) {
      case "edit":
        return "Edit Project";
      case "template":
        return `Create Project from Template`;
      default:
        return "Create New Project";
    }
  };

  const getButtonText = () => {
    if (currentStep === 4) {
      switch (mode) {
        case "edit":
          return "Update Project";
        case "template":
          return "Create Project";
        default:
          return "Create Project";
      }
    }
    return "Next";
  };


  function extractProjectId(resp: CreateProjectResponse) {
    if (resp?.isSuccess && resp.data) {
      setProjectData((prev) => ({
        ...prev,
        projectId: resp.data.projectId,
        id: resp.data.id,
      }));
      console.log("project id = ", projectData.projectId);
      console.log("id = ", projectData.id);
    } else {
      console.error("Invalid response", resp);
    }
  }


  const handleAddProject = async () => {
    console.log("Submitting project data:", projectData);
    console.log(mode, projectData);

    let response;

    if (mode === "edit" && projectId) {
      response = await editProjectMutation.mutateAsync({
        projectId,
        projectData,
      });
    } else {
      response = await createProjectMutation.mutateAsync(projectData);
    }

    console.log("Project response", response);

    if (response.isSuccess) {
      extractProjectId(response);

      console.log("Project created/updated successfully");

      const successMessage =
        mode === "edit"
          ? response.message
          : mode === "template"
            ? "Project created from template successfully!"
            : "Project created successfully!";

      toast.success(successMessage);
    }
  };
  const prepareCombinedUpload = (): FormData | null => {
    const datasets = projectData.datasets;
    if (!datasets || datasets.length === 0) {
      console.log("No datasets found");
      return null;
    }

    const files: File[] = [];

    // Handle FileList
    if (typeof FileList !== "undefined" && datasets instanceof FileList) {
      files.push(...Array.from(datasets));
    } else if (Array.isArray(datasets)) {
      datasets.forEach((item: any, i: number) => {
        if (item instanceof File) {
          files.push(item);
        } else if (item && item.file instanceof File) {
          // your wrapper shape: { id, name, file, size }
          files.push(item.file);
        } else if (item && item.originFileObj instanceof File) {
          // other libs (antd) pattern
          files.push(item.originFileObj);
        } else {
          console.log(`datasets[${i}] ignored — not a File:`, item);
        }
      });
    } else {
      console.log("datasets is not an array or FileList:", datasets);
    }

    if (files.length === 0) {
      console.log("No File objects to upload after normalization");
      return null;
    }

    const fd = new FormData();
    files.forEach((f) => fd.append("dataset", f)); // or "files" depending on backend
    console.log("project id : ", projectData.projectId);
    console.log("id : ", projectData.id);
    fd.append("projectId", projectData.id || "");
    fd.append("courseId", projectData.courseId || "");
    fd.append("isPublic", "false");

    // Debug FormData contents
    for (const [key, value] of fd.entries()) {
      console.log("FormData entry:", key, value);
    }

    return fd;
  };

  const handleNext = async () => {
    console.warn("Here checking", mode)
    console.log("Current step", currentStep);
    console.log("canProceed ", canProceed);
    if (currentStep <= 4) {
      switch (currentStep) {
        case 1:
          handleAddProject();
          break;
        case 2:
          const fd = prepareCombinedUpload();
          console.log("Prepared FormData for upload:", fd);
          if (fd) {
            // dataSetMutation.mutate(fd);
            // // Call backend API to create project
            for (const [name, value] of fd.entries()) {
              console.log(
                "after prepareCombinedUpload - FormData entry:",
                name,
                value
              );
            }
            const uploadDatasetResponse =
              await uploadDataSetMutation.mutateAsync(fd);
            if (uploadDatasetResponse.isSuccess) {
              console.log("Dataset uploaded successfully");
            }
          }

          break;
        case 3:
          console.log("Creating checkpoints");
          // checkpointsMutation.mutate();
          const createCheckPointResponse =
            await createCheckpointMutation.mutateAsync(projectData.checkpoints);
          if (createCheckPointResponse.isSuccess) {
            console.log("Checkpoints created successfully");
          }
          break;
        case 4:
          console.log("Creating rubric");
          // rubricMutation.mutate();
          const createRubricResponse = await createRubricMutation.mutateAsync(
            projectData.gradingRubric
          );
          if (createRubricResponse.isSuccess) {
            console.log("canProcees", canProceed);
            if (canProceed) {
              // Save project and navigate back
              console.log("Saving project:", projectData);

              projectData.isScreen = currentStep;

              // handleAddProject();

              navigateTo("manage-projects");
            }
            console.log("Grading Rubric created/updated successfully");
          }
          break;
      }

      setCurrentStep(currentStep + 1);

      projectData.isScreen = currentStep;
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleCancel = () => {
    navigate(paths.manageProjects)
    //    navigateTo("manage-projects");
  };

  const handleSaveAsTemplate = async () => {
    if (!templateName.trim()) {
      toast.error("Please enter a template name");
      return;
    }

    setSavingTemplate(true);

    try {
      // Simulate API call to save template
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Create template object
      const template = {
        id: Date.now().toString(),
        name: templateName.trim(),
        description: templateDescription.trim() || "No description provided",
        projectData: {
          ...projectData,
          // Clear instance-specific data for template
          projectName: "",
          instructor: "",
          teachingAssistant: "",
          checkpoints: projectData.checkpoints.map((cp) => ({
            ...cp,
            start_date: "",
            due_date: "",
          })),
        },
        createdAt: new Date().toISOString(),
        createdBy: "Current User", // In real app, get from auth context
        category: projectData.difficulty_level || "General",
        tags: [
          projectData.projectType,
          projectData.difficulty_level,
          ...projectData.tags,
        ].filter(Boolean),
        usageCount: 0,
      };

      // In a real application, this would be an API call
      console.log("Saving template:", template);

      // Store in localStorage for demo purposes
      const existingTemplates = JSON.parse(
        localStorage.getItem("projectTemplates") || "[]"
      );
      existingTemplates.push(template);
      localStorage.setItem(
        "projectTemplates",
        JSON.stringify(existingTemplates)
      );

      toast.success("Template saved successfully!");
      setShowTemplateDialog(false);
      setTemplateName("");
      setTemplateDescription("");
    } catch (error) {
      toast.error("Failed to save template. Please try again.");
    } finally {
      setSavingTemplate(false);
    }
  };

  const addCheckpoint = () => {
    // compute new checkpoint number from current state
    const newCheckpointNumber = projectData.checkpoints.length
      ? Math.max(
        ...projectData.checkpoints.map((c) => Number(c.checkpoint_number))
      ) + 1
      : 1;

    const newCheckpoint: checkpoints = {
      project_id: projectData.id || "",
      isScreen: projectData.isScreen ?? 0,
      title: `Checkpoint ${newCheckpointNumber}`,
      description: "",
      checkpoint_number: newCheckpointNumber,
      start_date: "",
      due_date: "",
      weight_percentage: 0,
      is_required: false,
      status: "draft",
      metadata: {},
    };

    const newRubricItem = {
      checkpoint: `Checkpoint ${newCheckpointNumber}`,
      checkpoint_mapping: {},
      criteria: [
        { name: `criteria-${Date.now()}`, description: "", points: 0 },
      ],
      total_points: 0,
      project_id: projectData.id || "",
      title: `Checkpoint ${newCheckpointNumber}`,
      description: "temp desc",
      grading_scale: {},
      is_template: false,
      template_name: "",
      metadata: {},
    };

    setProjectData((prev) => ({
      ...prev,
      checkpoints: [...prev.checkpoints, newCheckpoint],
      gradingRubric: [...prev.gradingRubric, newRubricItem],
    }));

    // select the newly created checkpoint (selectedCheckpointId is a number)
    setSelectedCheckpointId(newCheckpointNumber);
  };

  const updateCheckpoint = (
    id: number,
    field: string,
    value: string | boolean
  ) => {
    setProjectData((prev) => ({
      ...prev,
      checkpoints: prev.checkpoints.map((cp) =>
        cp.checkpoint_number === id ? { ...cp, [field]: value } : cp
      ),
    }));
  };

  const updateRubricItem = (
    index: number,
    field: string,
    value: string | number
  ) => {
    setProjectData((prev) => ({
      ...prev,
      gradingRubric: prev.gradingRubric.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  const Breadcrumb_Component = () => (
    <div className="flex items-start">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink
              onClick={() => navigateTo("dashboard")}
              className="cursor-pointer flex items-center"
            >
              <Home className="h-4 w-4" />
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink
              onClick={() => navigateTo("manage-projects")}
              className="cursor-pointer flex items-center"
            >
              <FolderOpen className="h-4 w-4 mr-1" />
              Project Management
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{getPageTitle()}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );

  const StepIndicator = () => (
    <div className="flex items-center justify-center mb-12">
      {steps.map((step, index) => (
        <div key={`step-${step.number}`} className="flex items-center">
          <div className="flex flex-col items-center">
            {/* Circle */}
            <div
              className={`w-6 h-6 rounded-full border-2 flex items-center justify-center
              ${step.number <= currentStep
                  ? "bg-bits-blue border-bits-blue"
                  : "bg-gray-300 border-gray-300"
                }`}
            >
              {/* Inner white dot only if active */}
              {step.number === currentStep && (
                <div className="w-2 h-2 rounded-full bg-white" />
              )}
            </div>

            {/* Title + Subtitle */}
            <div className="mt-2 text-center">
              <div className="text-sm font-medium text-gray-900">
                {step.title}
              </div>
              <div className="text-xs text-gray-500">{step.subtitle}</div>
            </div>
          </div>

          {/* Divider */}
          {index < steps.length - 1 && (
            <div
              key={`step-divider-${step.number}`}
              className={`w-24 h-0.5 mx-4 mt-[-20px] ${step.number < currentStep ? "bg-bits-blue" : "bg-gray-200"
                }`}
            />
          )}
        </div>
      ))}
    </div>
  );

  const TemplateInfoBanner = () => {
    if (mode !== "template" || !templateData) return null;

    return (
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start space-x-3">
          <Copy className="h-5 w-5 text-bits-blue mt-0.5" />
          <div className="flex-1">
            <h3 className="text-sm font-medium text-blue-900">
              Creating project from template: "{templateData.name}"
            </h3>
            <p className="text-sm text-blue-700 mt-1">
              {templateData.description}
            </p>
            <div className="flex items-center space-x-4 mt-2 text-xs text-bits-blue">
              <span>Category: {templateData.category}</span>
              <span>•</span>
              <span>Used {templateData.usageCount} times</span>
              <span>•</span>
              <span>Created by {templateData.createdBy}</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  useEffect(() => {
    console.log("courses (from query):", courses);
    console.log("projectData.courseId:", projectData.courseId);
    console.log("projectData.instructorIds:", projectData.instructorIds);
    console.log("projectData.teachingAssId:", projectData.teachingAssId);
  }, [
    courses,
    projectData.courseId,
    projectData.instructorIds,
    projectData.teachingAssId,
  ]);
  const navigate = useNavigate()

  const navigateBack = () => {
    navigate(paths.manageProjects)
  }
  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="p-6">
        {/* Breadcrumb - Fixed alignment */}
        <div className="mb-6">
          <Breadcrumb_Component />
        </div>

        {/* Template Info Banner */}
        <TemplateInfoBanner />

        {/* Header - Increased spacing to stepper by 1.5x */}
        <div className="flex items-center space-x-4 mb-12">
          <Button variant="ghost" size="sm" onClick={navigateBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-semibold text-gray-900">
            {getPageTitle()}
          </h1>
          {mode === "template" && templateData && (
            <div className="ml-auto text-sm text-gray-500">
              Template: {templateData.name}
            </div>
          )}
        </div>

        {/* Step Indicator - Increased spacing to content by 1.5x */}
        <StepIndicator />

        {/* Step Content - Full Width */}
        <div className="mb-8">
          {currentStep === 1 && (
            <ProjectDetailsStep
              projectData={projectData}
              setProjectData={setProjectData}
              courses={courses}
              coursesLoading={coursesLoading}
              coursesError={coursesError}
              coursesErrorObj={coursesErrorObj}
              courseStaff={courseStaff}
              staffLoading={staffLoading}
              staffError={staffError}
            />
          )}
          {currentStep === 2 && (
            <ProjectAssetsStep
              projectData={projectData}
              setProjectData={setProjectData}
            />
          )}
          {currentStep === 3 && (
            <ReviewCheckpointsStep
              projectData={projectData}
              setProjectData={setProjectData}
              selectedCheckpointId={selectedCheckpointId}
              setSelectedCheckpointId={setSelectedCheckpointId}
              addCheckpoint={addCheckpoint}
              updateCheckpoint={updateCheckpoint}
            />
          )}
          {currentStep === 4 && (
            <ProjectSettingsStep
              projectData={projectData}
              setProjectData={setProjectData}
              updateRubricItem={updateRubricItem}
              totalMarks={totalMarks}
              isValidMarks={isValidMarks}
              marksError={marksError}
            />
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <div>
            {currentStep > 1 && (
              <Button variant="outline" onClick={handleBack}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            )}
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>

            {/* Save as Template Button - Don't show in template mode to avoid confusion */}
            {mode !== "template" && (
              <Dialog
                open={showTemplateDialog}
                onOpenChange={setShowTemplateDialog}
              >
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    className="text-bits-blue border-blue-600 hover:bg-blue-50"
                  >
                    <Bookmark className="h-4 w-4 mr-2" />
                    Save as Template
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px] bg-white border">
                  <DialogHeader>
                    <DialogTitle>Save as Template</DialogTitle>
                    <DialogDescription>
                      Save this project configuration as a reusable template for
                      future projects.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="templateName">Template Name *</Label>
                      <Input
                        id="templateName"
                        placeholder="Enter template name (e.g., 'Data Analysis Project')"
                        value={templateName}
                        onChange={(e) => setTemplateName(e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="templateDescription">Description</Label>
                      <Textarea
                        id="templateDescription"
                        placeholder="Brief description of when to use this template..."
                        value={templateDescription}
                        onChange={(e) => setTemplateDescription(e.target.value)}
                        className="min-h-[80px]"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setShowTemplateDialog(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSaveAsTemplate}
                      disabled={!templateName.trim() || savingTemplate}
                      className="bg-bits-blue hover:bg-bits-blue"
                    >
                      {savingTemplate ? (
                        <>
                          <Save className="h-4 w-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          Save Template
                        </>
                      )}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            )}

            <Button
              onClick={handleNext}
              className="bg-bits-blue hover:bg-bits-blue"
              disabled={!canProceed}
            >
              {getButtonText()}
              {currentStep < 4 && <span className="ml-2">→</span>}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}


interface Props {
  onChange?: (formattedTime: string) => void;
  value?: string; // Optional prop to set initial value
}

function TimeDurationInputs({ onChange, value }: Props) {
  // Initialize from value prop if provided (format: "HHH:MM")
  const parseInitialValue = (val?: string) => {
    if (!val || !val?.includes(':')) return { hours: '', minutes: '' };
    const [h, m] = val.split(':');
    return {
      hours: parseInt(h) || 0,
      minutes: parseInt(m) || 0
    };
  };

  const initialValue = parseInitialValue(value);
  const [hours, setHours] = useState(initialValue.hours.toString());
  const [minutes, setMinutes] = useState(initialValue.minutes.toString());

  const formatTime = (h: string, m: string) => {
    const hoursNum = parseInt(h) || 0;
    const minutesNum = parseInt(m) || 0;
    const formattedHours = hoursNum.toString().padStart(3, "0");
    const formattedMinutes = minutesNum.toString().padStart(2, "0");
    return `${formattedHours}:${formattedMinutes}`;
  };

  const handleHoursChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Allow only digits and limit to 3 characters
    if (/^\d{0,3}$/.test(inputValue)) {
      const numValue = parseInt(inputValue) || 0;
      // Limit to maximum 999 hours
      if (numValue <= 999) {
        setHours(inputValue);
        onChange?.(formatTime(inputValue, minutes));
      }
    }
  };

  const handleMinutesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Allow only digits and limit to 2 characters
    if (/^\d{0,2}$/.test(inputValue)) {
      const numValue = parseInt(inputValue) || 0;
      // Limit to maximum 59 minutes
      if (numValue <= 59) {
        setMinutes(inputValue);
        onChange?.(formatTime(hours, inputValue));
      }
    }
  };

  const handleHoursBlur = () => {
    // Ensure minimum 1 digit display on blur
    if (hours === '') {
      setHours('0');
      onChange?.(formatTime('0', minutes));
    }
  };

  const handleMinutesBlur = () => {
    // Ensure minimum 1 digit display on blur
    if (minutes === '') {
      setMinutes('0');
      onChange?.(formatTime(hours, '0'));
    }
  };

  const handleHoursKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow: backspace, delete, tab, escape, enter, arrows, home, end
    if ([8, 9, 27, 13, 46, 35, 36, 37, 38, 39, 40].includes(e.keyCode) ||
      // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
      (e.keyCode === 65 && e.ctrlKey) ||
      (e.keyCode === 67 && e.ctrlKey) ||
      (e.keyCode === 86 && e.ctrlKey) ||
      (e.keyCode === 88 && e.ctrlKey)) {
      return;
    }
    // Ensure that it's a number and stop the keypress
    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
      e.preventDefault();
    }
  };

  const handleMinutesKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Same validation as hours
    if ([8, 9, 27, 13, 46, 35, 36, 37, 38, 39, 40].includes(e.keyCode) ||
      (e.keyCode === 65 && e.ctrlKey) ||
      (e.keyCode === 67 && e.ctrlKey) ||
      (e.keyCode === 86 && e.ctrlKey) ||
      (e.keyCode === 88 && e.ctrlKey)) {
      return;
    }
    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
      e.preventDefault();
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Hours Input */}
      <div className="flex flex-col">
        <input
          id="hours"
          type="text"
          value={hours}
          onChange={handleHoursChange}
          onBlur={handleHoursBlur}
          onKeyDown={handleHoursKeyDown}
          placeholder="000"
          maxLength={3}
          className="w-16 px-2 py-2 text-sm text-center border border-gray-300 rounded bg-bits-grey-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <label className="text-xs text-gray-500 text-center mt-1">Hours</label>
      </div>

      {/* Separator */}
      <div className="text-lg font-semibold text-gray-400 mb-4">:</div>

      {/* Minutes Input */}
      <div className="flex flex-col">
        <input
          id="minutes"
          type="text"
          value={minutes}
          onChange={handleMinutesChange}
          onBlur={handleMinutesBlur}
          onKeyDown={handleMinutesKeyDown}
          placeholder="00"
          maxLength={2}
          className="w-14 px-2 py-2 text-sm text-center border border-gray-300 rounded bg-bits-grey-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <label className="text-xs text-gray-500 text-center mt-1">Minutes</label>
      </div>

      {/* Display formatted time */}
      <div className="ml-2 text-sm text-gray-600 font-mono">
        {formatTime(hours, minutes)}
      </div>
    </div>
  );
}

type Item = {
  id: string;
  name: string;
  // allow arbitrary extra fields
  [k: string]: any;
};

type CourseStaffProps = {
  items: Item[]; // list of selectable items
  selectedIds: string[]; // controlled selected ids
  onChange: (ids: string[]) => void; // controlled change handler
  label?: string; // optional label rendered inside the trigger
  placeholder?: string; // placeholder when nothing selected
  width?: string; // tailwind width class, e.g. "w-72"
  search?: boolean; // enable a simple search box in dropdown
  showSelectAll?: boolean; // show 'Select all' control
  className?: string; // extra classes for trigger
  projectData: any;
  setProjectData: React.Dispatch<React.SetStateAction<ProjectData>>;
  teachinAssistants: any[],
  valueToCheck: string
};

export function GenericMultiSelect({
  items,
  label,
  placeholder = "-- Select --",
  width = "w-72",
  search = true,
  showSelectAll = true,
  className = "",
  projectData,
  setProjectData,
  valueToCheck
}: CourseStaffProps) {
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useState("");
  const rootRef = useRef<HTMLDivElement | null>(null);
  const [searchParams] = useSearchParams();
  const mode = searchParams.get("mode");

  // Initialize selectedIds from projectData[valueToCheck] in edit mode
  useEffect(() => {
    if (
      mode === "edit" &&
      projectData[valueToCheck] &&
      !(projectData[`${valueToCheck}Ids`]?.length)
    ) {
      setProjectData((prev: any) => ({
        ...prev,
        [`${valueToCheck}Ids`]: projectData[valueToCheck].map((i: any) =>
          String(i.id)
        ),
      }));
    }
  }, [mode, projectData[valueToCheck]]);

  // Close dropdown on outside click
  useEffect(() => {
    function handleClickOutside(e: MouseEvent) {
      if (!rootRef.current) return;
      if (!rootRef.current.contains(e.target as Node)) setOpen(false);
    }
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  // Filter items based on search query
  const filtered = query
    ? items.filter(
      (it) =>
        String(it.name)?.toLowerCase()?.includes(query.toLowerCase()) ||
        String(it.id)?.toLowerCase()?.includes(query.toLowerCase())
    )
    : items;

  // Compute dynamic selected IDs
  const dynamicSelectedIds: string[] =
    projectData[`${valueToCheck}Ids`] ??
    (mode === "edit" && projectData[valueToCheck]
      ? projectData[valueToCheck].map((i: any) => String(i.id))
      : []);

  // Toggle selection
  const toggleId = (id: string) => {
    const exists = dynamicSelectedIds.includes(id);
    const updatedIds = exists
      ? dynamicSelectedIds.filter((x) => x !== id)
      : [...dynamicSelectedIds, id];

    setProjectData((prev: any) => ({
      ...prev,
      [`${valueToCheck}Ids`]: updatedIds,
    }));
  };

  // Select all / clear all
  const selectAll = () =>
    setProjectData((prev: any) => ({
      ...prev,
      [`${valueToCheck}Ids`]: items.map((it) => String(it.id)),
    }));
  const clearAll = () =>
    setProjectData((prev: any) => ({
      ...prev,
      [`${valueToCheck}Ids`]: [],
    }));

  // Display selected names in button
  const labelStr =
    dynamicSelectedIds.length > 0
      ? dynamicSelectedIds
        .map((id) => items.find((it) => String(it.id) === id)?.name ?? id)
        .join(", ")
      : label ?? placeholder;

  return (
    <div className="relative inline-block text-left" ref={rootRef}>
      {/* Button showing selected names */}
      <button
        type="button"
        onClick={() => setOpen((v) => !v)}
        className={`${width} text-left text-sm font-normal text-bits-grey-500 border bg-bits-grey-100 px-3 py-2 rounded flex items-center justify-between ${className}`}
        aria-haspopup="menu"
        aria-expanded={open}
      >
        <span className="truncate">{labelStr}</span>
        <span className="ml-2">▾</span>
      </button>

      {/* Dropdown menu */}
      {open && (
        <div
          className="absolute mt-2 bg-white shadow-lg border rounded z-50 max-h-72 overflow-auto p-2"
          style={{ minWidth: 280 }}
          role="menu"
        >
          <div className="flex flex-col gap-2">
            {search && (
              <input
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Search..."
                className="px-2 py-1 border rounded text-sm"
              />
            )}

            <div className="flex items-center justify-between gap-2">
              {showSelectAll && (
                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={selectAll}
                    className="text-sm px-2 py-1 rounded hover:bg-gray-100"
                  >
                    Select all
                  </button>
                  <button
                    type="button"
                    onClick={clearAll}
                    className="text-sm px-2 py-1 rounded hover:bg-gray-100"
                  >
                    Clear
                  </button>
                </div>
              )}

              <button
                type="button"
                onClick={() => setOpen(false)}
                className="text-sm px-3 py-1 rounded bg-bits-grey-100"
              >
                Done
              </button>
            </div>

            <div className="divide-y overflow-auto">
              {filtered.length === 0 ? (
                <div className="p-2 text-sm text-gray-500">No items</div>
              ) : (
                filtered.map((it) => {
                  const idStr = String(it.id);
                  const checked = dynamicSelectedIds.includes(idStr);
                  return (
                    <button
                      key={idStr}
                      type="button"
                      onClick={() => toggleId(idStr)}
                      className={`w-full flex items-center justify-between gap-2 p-2 rounded hover:bg-gray-50 text-left ${checked ? "bg-bits-grey-50" : ""
                        }`}
                    >
                      <div className="text-sm truncate">
                        {it.name}{" "}
                        {it.id && (
                          <span className="text-xs text-gray-400">({it.id})</span>
                        )}
                      </div>
                      {checked ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-7.414 7.414a1 1 0 01-1.414 0L3.293 9.414a1 1 0 011.414-1.414L8 11.293l6.293-6.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      ) : (
                        <span className="w-4 h-4" />
                      )}
                    </button>
                  );
                })
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
