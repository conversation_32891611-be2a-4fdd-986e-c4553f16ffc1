import React, { use<PERSON>em<PERSON>, useState } from "react";
import { <PERSON><PERSON> } from "../components/ui/button";
import {
  Search,
  Upload,
  Eye,
  ArrowLeft,
  Plus,
} from "lucide-react";
import useNavigation from "../components/Context/NavigationContext";
import { Input } from "../components/ui/input";
import { Badge } from "../components/ui/badge";
import {
  useUpdateContentMutation,
  getContents,
} from "../api/jupyterManagementApi";
import { toast } from "sonner";
import { useJupyterWorkspace } from "../contexts/JupyterWorkspaceContext";
import { useLocation } from "react-router-dom";

type DatasetType = "Project File" | "Template" | "User Created";

interface Dataset {
  id: string;
  name: string;
  description?: string;
  author?: string;
  lastModified?: string;
  size?: string;
  version?: string;
  type?: DatasetType;
  tags?: string[];
  format?: string;
  // optional path that some APIs might return
  path?: string;
}

function getTypeColor(type?: DatasetType) {
  switch (type) {
    case "Project File":
      return "bg-bits-blue/10 text-bits-blue border-bits-blue";
    case "Template":
      return "bg-bits-gold/10 text-bits-gold border-bits-gold";
    case "User Created":
    default:
      return "bg-muted-foreground/10 text-muted-foreground border-muted-foreground";
  }
}

// allowed file extensions for listing
const ALLOWED_EXTS = new Set(["csv", "txt", "md"]);

function getExt(name: string): string {
  const i = name.lastIndexOf(".");
  if (i === -1) return "";
  return name.slice(i + 1).toLowerCase();
}

function isAllowedName(name?: string) {
  if (!name) return false;
  return ALLOWED_EXTS.has(getExt(name));
}

function normalizeModelToDataset(res: any, fallback: { id: string; name: string; path: string }, base: Partial<Dataset> = {}): Dataset {
  return {
    id: res?.id ?? res?.path ?? fallback.id,
    name: res?.name ?? fallback.name,
    description: res?.description ?? base.description ?? "",
    author: res?.author ?? base.author ?? "",
    lastModified: res?.last_modified ?? res?.lastModified ?? new Date().toISOString(),
    size:
      typeof res?.size === "number"
        ? `${res.size}`
        : res?.size ?? base.size ?? "",
    version: res?.version ?? base.version ?? "",
    type: (base.type as DatasetType) ?? "User Created",
    tags: res?.tags ?? base.tags ?? [],
    format: res?.format ?? base.format ?? inferMimeFromName(fallback.name),
    path: res?.path ?? fallback.path,
  };
}

function inferMimeFromName(name: string) {
  const ext = getExt(name);
  switch (ext) {
    case "csv":
      return "text/csv";
    case "txt":
      return "text/plain";
    case "md":
      return "text/markdown";
    default:
      return "text/plain";
  }
}

export default function DatasetsPage() {
  const { goBack, pageParams, navigateTo } = useNavigation();
  const initialFromParams: Dataset[] = (pageParams?.files as Dataset[]) ?? [];
  const [datasets, setDatasets] = useState<Dataset[]>(
    // ensure initial list is filtered
    
    initialFromParams.filter(d => isAllowedName(d?.name))
  );
  const { workspace } = useJupyterWorkspace();
    
  const updateContentMutation = useUpdateContentMutation();

  const [searchTerm, setSearchTerm] = useState("");

  const filteredDatasets = useMemo(() => {
    const q = searchTerm.trim().toLowerCase();
    // filter by allowed extensions first, then by query
    const base = datasets.filter(d => isAllowedName(d?.name));
    if (!q) return base;
    return base.filter(
      (d) =>
        d.name.toLowerCase().includes(q) ||
        (d.description ?? "").toLowerCase().includes(q) ||
        (d.tags ?? []).some((t) => t.toLowerCase().includes(q))
    );
  }, [datasets, searchTerm]);

  const buildTargetPath = (filename: string) => {
    const basePath = (workspace?.folderPath ?? "").replace(/\/+$/g, "");
    return `${basePath}/${filename}`;
  };

  const handleUpload = () => {
    const input = document.createElement("input");
    input.type = "file";
    // keep CSV upload accept rule; listing can still show txt/md from incoming state
    input.accept = ".csv";
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        const filePath = buildTargetPath(file.name);
        const content = await file.text();
        const type = "file";
        const format = "text";

        const res: any = await updateContentMutation.mutateAsync({
          path: filePath,
          content,
          type,
          format,
        });

        const added = normalizeModelToDataset(
          res,
          { id: filePath, name: file.name, path: filePath },
          {
            type: "User Created",
            size: `${file.size}`,
            format: "text/csv",
          }
        );

        // only add if allowed by extension
        if (!isAllowedName(added.name)) {
          toast.info("Uploaded, but hidden due to unsupported extension filter.");
        }

        setDatasets((prev) => {
          const exists = prev.some(
            (d) => d.id === added.id || d.name.toLowerCase() === added.name.toLowerCase()
          );
          return exists ? prev : [added, ...prev];
        });

        toast.success(`Uploaded ${added.name}`);
      } catch (err) {
        console.error(err);
        toast.error("Upload failed");
      }
    };
    input.click();
  };

  const handleDownload = async (dataset: Dataset) => {
    try {
      const filePath = dataset.path ?? buildTargetPath(dataset.name);
      const fileModel = await getContents(filePath);

      const contentText =
        typeof fileModel?.content === "string" ? fileModel.content : "";

      if (!contentText) {
        toast.error("No content available to download.");
        return;
      }
      const blob = new Blob([contentText], {
        type: `${inferMimeFromName(dataset.name)};charset=utf-8;`,
      });
      const url = URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", dataset.name);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);

      toast.success(`Downloading ${dataset.name}`);
    } catch (err) {
      console.error("Download failed:", err);
      toast.error("Download failed");
    }
  };

  const handleDuplicate = async (dataset: Dataset) => {
    try {
      const resolvePath = (name: string) => {
        const basePath = (workspace?.folderPath ?? "").replace(/\/+$/g, "");
        return `${basePath}/${name}`;
      };
      const sourcePath = dataset.path ?? resolvePath(dataset.name);
      const sourceModel = await getContents(sourcePath);
      const sourceText =
        typeof sourceModel?.content === "string" ? sourceModel.content : "";

      if (!sourceText) {
        toast.error("No content available to duplicate.");
        return;
      }
      const existingNames = new Set(datasets.map((d) => d.name.toLowerCase()));
      const nameParts = dataset.name.split(".");
      const ext = nameParts.length > 1 ? `.${nameParts.pop()}` : "";
      const base = nameParts.join(".");
      const mkName = (suffixIdx: number | null) =>
        suffixIdx === null ? `${base}-copy${ext}` : `${base}-copy-${suffixIdx}${ext}`;

      let candidate = mkName(null);
      let idx = 1;
      while (existingNames.has(candidate.toLowerCase())) {
        candidate = mkName(idx++);
      }
      const targetPath = resolvePath(candidate);
      const res: any = await updateContentMutation.mutateAsync({
        path: targetPath,
        content: sourceText,
        type: "file",
        format: "text",
      });

      const added = normalizeModelToDataset(
        res,
        { id: targetPath, name: candidate, path: targetPath },
        {
          type: dataset.type ?? "User Created",
          tags: dataset.tags ?? [],
          format: dataset.format ?? inferMimeFromName(candidate),
          size: dataset.size ?? "",
        }
      );

      setDatasets((prev) => {
        const exists = prev.some((d) => d.id === added.id || d.name === added.name);
        return exists ? prev : [added, ...prev];
      });

      toast.success(`Duplicated ${dataset.name} → ${added.name}`);
    } catch (err: any) {
      console.error("Duplicate failed:", err);
      toast.error(err?.response?.data?.message || "Duplicate failed");
    }
  };

  const handleView = (dataset: Dataset) => {
    navigateTo("notebook", { file: dataset });
  };

  const handleCreateNew = async () => {
    try {
      const basePath = (workspace?.folderPath ?? "").replace(/\/+$/g, "");
      const existingNames = new Set(datasets.map((d) => d.name.toLowerCase()));
      const base = "Untitled";
      const ext = ".csv";

      const mkName = (i: number) => (i === 0 ? `${base}${ext}` : `${base}-${i}${ext}`);

      let i = 0;
      let filename = mkName(i);
      while (existingNames.has(filename.toLowerCase())) {
        i += 1;
        filename = mkName(i);
      }

      const filePath = `${basePath}/${filename}`;

      // Add initial CSV columns and a sample row
      // Adjust as needed for the app’s expectations
      const headers = ["id", "name", "value", "timestamp"];
      const sample = [
        "1",
        "Sample",
        "0",
        new Date().toISOString(),
      ];
      const csvContent = `${headers.join(",")}\n${sample.join(",")}\n`;

      const res: any = await updateContentMutation.mutateAsync({
        path: filePath,
        content: csvContent,
        type: "file",
        format: "text",
      });

      const added = normalizeModelToDataset(
        res,
        { id: filePath, name: filename, path: filePath },
        {
          type: "User Created",
          size: `${csvContent.length}`,
          format: "text/csv",
        }
      );

      setDatasets((prev) => {
        const exists = prev.some((d) => d.id === added.id || d.name === added.name);
        return exists ? prev : [added, ...prev];
      });

      toast.success(`Created ${added.name}`);
    } catch (err: any) {
      console.error("Create new CSV failed:", err);
      toast.error(err?.response?.data?.message || "Failed to create CSV");
    }
  };

  return (
    <div className="p-4 mt-4 space-y-2 bg-white">
      <div className="flex items-center">
        <Button variant="destructive" className="p-4" onClick={goBack}>
          <ArrowLeft className="!h-6 !w-8 text-gray-700" />
        </Button>
        <h2 className="text-2xl font-semibold flex items-center gap-2">
          Dataset Management
        </h2>
        <div className="ml-auto flex items-center gap-2">
          <Button onClick={handleUpload} variant="secondary" className="mt-4">
            <Upload className="h-4 w-4" />
            Upload dataset
          </Button>
          <Button onClick={handleCreateNew} variant="secondary" className="mt-4">
            <Plus className="h-4 w-4" />
            New dataset
          </Button>
        </div>
      </div>

      <p className="text-muted-foreground">
        Manage datasets, upload new data files, or create new datasets for data science projects
      </p>

      <div className="relative">
        <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search datasets..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-9"
        />
      </div>

      <div className="grid gap-3">
        {filteredDatasets.map((dataset) => (
          <div key={dataset.id} className="border rounded-lg p-3 bg-background">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {/* <span
                  className={`border px-2 py-0.5 rounded text-xs ${getTypeColor(
                    dataset.type
                  )}`}
                >
                  {dataset.type ?? "User Created"}
                </span> */}
                <span className="font-medium">{dataset.name}</span>
                {/* <span className="text-muted-foreground">
                  · {dataset.version ?? "v1"}
                </span> */}
              </div>
              <div className="flex items-center gap-2">
                <Button size="sm" variant="outline" onClick={() => handleView(dataset)}>
                 <Eye className="h-4 w-4" /> Open
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDownload(dataset)}
                >
                  Download
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDuplicate(dataset)}
                >
                  Duplicate
                </Button>
              </div>
            </div>

            <div className="mt-1 text-sm text-muted-foreground">
              {dataset.description ?? ""}
            </div>

            <div className="mt-2 flex items-center gap-3 text-xs text-muted-foreground">
              <span> {dataset.author ?? "👤"}</span>
              <span>⏰ {dataset.lastModified ?? ""}</span>
              <span>📁 {dataset.size ?? ""}</span>
              <span>🧾 {dataset.format ?? ""}</span>
            </div>

            <div className="mt-2 flex gap-2">
              {(dataset.tags ?? []).map((tag) => (
                <Badge key={tag} variant="outline">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        ))}

        {filteredDatasets.length === 0 && (
          <div className="text-sm text-muted-foreground">
            No datasets match the current search.
          </div>
        )}
      </div>
    </div>
  );
}
