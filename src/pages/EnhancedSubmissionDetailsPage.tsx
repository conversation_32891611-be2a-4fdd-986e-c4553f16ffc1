import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import {
  CustomTabs,
  CustomTabsList,
  CustomTabsTrigger,
  CustomTabsContent,
} from "../components/ui/custom-tabs";
import { Textarea } from "../components/ui/textarea";
import { Separator } from "../components/ui/separator";
import { Alert, AlertDescription } from "../components/ui/alert";
import {
  ArrowLeft,
  Download,
  FileText,
  Calendar,
  Clock,
  User,
  GraduationCap,
  Eye,
  Play,
  Code,
  BarChart3,
  MessageSquare,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  RefreshCw,
  Save,
  Edit3,
  Send,
  Upload,
  GitBranch,
} from "lucide-react";
import { useSubmissionNavigation } from "../contexts/SubmissionNavigationContext";
import { useSandboxSession } from "../contexts/SandboxSessionContext";
import { SubmissionNavigationControls } from "../components/SubmissionNavigationControls";
import { SandboxStatusPanel } from "../components/SandboxStatusPanel";
import FeedbackDialog from "../components/FeedbackDialog";
//import { useNavigation, useUtilities } from "../App";
import useNavigation from "../components/Context/NavigationContext";
import useUtilities from "../components/Context/UtilityContext";
import { formatDistanceToNow } from "../helper/date-fns";
import { toast } from "sonner";

export default function EnhancedSubmissionDetailsPage() {
  const { goBack, navigateTo, pageParams } = useNavigation();
  const { downloadSubmission } = useUtilities();
  const [activeTab, setActiveTab] = useState("overview");
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Feedback dialog state
  const [isFeedbackDialogOpen, setIsFeedbackDialogOpen] = useState(false);
  const [feedbackText, setFeedbackText] = useState("");
  const [isEditingFeedback, setIsEditingFeedback] = useState(false);
  const [isSavingFeedback, setIsSavingFeedback] = useState(false);

  const {
    currentSubmission,
    assignmentTitle,
    isLoading,
    refreshSubmissions,
    setSubmissions,
  } = useSubmissionNavigation();

  // Initialize submissions from pageParams
  React.useEffect(() => {
    if (pageParams?.submissions && pageParams?.assignmentTitle) {
      setSubmissions(pageParams.submissions, pageParams.assignmentTitle);

      // Find the current submission index if specified
      if (pageParams.currentSubmissionId) {
        const currentIndex = pageParams.submissions.findIndex(
          (s) => s.id === pageParams.currentSubmissionId
        );
        if (currentIndex >= 0) {
          // The context will automatically set the current submission
        }
      }
    }
  }, [pageParams, setSubmissions]);

  // Initialize feedback text when current submission changes
  useEffect(() => {
    if (currentSubmission) {
      setFeedbackText(currentSubmission.feedback || "");
    }
  }, [currentSubmission]);

  // Generate mock submission history data
  const getSubmissionHistory = () => {
    if (!currentSubmission) return [];

    const submissionDate = new Date(currentSubmission.submissionDate);
    const history = [
      {
        id: 1,
        type: "submission",
        title: "Initial Submission",
        description: "Student submitted their work",
        timestamp: submissionDate,
        status: "completed",
        icon: Upload,
        details: {
          files: currentSubmission.files?.length || 0,
          grade: null,
        },
      },
    ];

    // Add grading event if grade exists
    if (currentSubmission.grade) {
      history.push({
        id: 2,
        type: "grade",
        title: "Graded",
        description: `Instructor assigned grade: ${currentSubmission.grade}/100`,
        timestamp: new Date(submissionDate.getTime() + 24 * 60 * 60 * 1000), // 1 day later
        status: "completed",
        icon: GraduationCap,
        details: {
          grade: currentSubmission.grade,
          gradedBy: "Dr. A. Sharma",
        },
      });
    }

    // Add feedback event if feedback exists
    if (currentSubmission.feedback) {
      history.push({
        id: 3,
        type: "feedback",
        title: "Feedback Provided",
        description: "Instructor provided detailed feedback",
        timestamp: new Date(submissionDate.getTime() + 25 * 60 * 60 * 1000), // 1 day + 1 hour later
        status: "completed",
        icon: MessageSquare,
        details: {
          feedback: currentSubmission.feedback,
          providedBy: "Dr. A. Sharma",
        },
      });
    }

    // Add current activity if in progress
    if (currentSubmission.status === "draft") {
      history.unshift({
        id: 0,
        type: "draft",
        title: "Work in Progress",
        description: "Student is currently working on this assignment",
        timestamp: new Date(),
        status: "in-progress",
        icon: Edit3,
        details: {},
      });
    }

    return history.reverse(); // Latest first
  };

  const { switchToSession, currentSession, getSessionStatus, updateActivity } =
    useSandboxSession();

  useEffect(() => {
    // Update activity when viewing submission details
    if (currentSession) {
      updateActivity(currentSession.id);
    }
  }, [currentSession, updateActivity]);

  const handleOpenSandbox = async () => {
    if (!currentSubmission) return;

    try {
      const session = await switchToSession({
        studentId: currentSubmission.studentId,
        studentName: currentSubmission.studentName,
        assignmentId: currentSubmission.assignmentId,
        assignmentTitle: assignmentTitle,
        submissionId: currentSubmission.id,
      });

      // Navigate to sandbox environment
      navigateTo("student-notebook", {
        studentId: currentSubmission.studentId,
        studentName: currentSubmission.studentName,
        assignmentId: currentSubmission.assignmentId,
        assignmentTitle: assignmentTitle,
        notebookUrl: session.notebookUrl,
        submissionId: currentSubmission.id,
      });
    } catch (error) {
      console.error("Failed to open sandbox:", error);
    }
  };

  const handleModelTesting = () => {
    if (!currentSubmission) return;

    navigateTo("model-testing", {
      studentId: currentSubmission.studentId,
      studentName: currentSubmission.studentName,
      assignmentId: currentSubmission.assignmentId,
      assignmentTitle: assignmentTitle,
      submissionId: currentSubmission.id,
    });
  };

  const handleViewCode = () => {
    if (!currentSubmission) return;

    navigateTo("view-student-code", {
      studentId: currentSubmission.studentId,
      studentName: currentSubmission.studentName,
      assignmentId: currentSubmission.assignmentId,
      assignmentTitle: assignmentTitle,
      submissionId: currentSubmission.id,
    });
  };

  const handleDownload = () => {
    if (!currentSubmission) return;
    downloadSubmission(currentSubmission.id, currentSubmission);
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshSubmissions();
      toast.success("Submission data refreshed");
    } catch (error) {
      toast.error("Failed to refresh submission data");
    } finally {
      setIsRefreshing(false);
    }
  };

  if (isLoading || !currentSubmission) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
          <p className="text-gray-500">Loading submission details...</p>
        </div>
      </div>
    );
  }

  const sessionStatus = getSessionStatus(
    currentSubmission.studentId,
    currentSubmission.assignmentId
  );

  const getSubmissionStatusInfo = () => {
    switch (currentSubmission.status) {
      case "submitted":
        return {
          icon: CheckCircle,
          color: "text-green-600",
          bg: "bg-green-50",
          border: "border-green-200",
        };
      case "late":
        return {
          icon: AlertCircle,
          color: "text-orange-600",
          bg: "bg-orange-50",
          border: "border-orange-200",
        };
      case "draft":
        return {
          icon: Clock,
          color: "text-blue-600",
          bg: "bg-blue-50",
          border: "border-blue-200",
        };
      default:
        return {
          icon: AlertCircle,
          color: "text-gray-600",
          bg: "bg-gray-50",
          border: "border-gray-200",
        };
    }
  };

  const statusInfo = getSubmissionStatusInfo();
  const StatusIcon = statusInfo.icon;

  const handleSaveFeedback = async () => {
    if (!currentSubmission) return;

    setIsSavingFeedback(true);
    try {
      // Simulate API call to save feedback
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Update the submission with new feedback
      // In a real app, this would update the backend and refresh the data
      toast.success("Feedback saved successfully");
      setIsEditingFeedback(false);
    } catch (error) {
      toast.error("Failed to save feedback");
    } finally {
      setIsSavingFeedback(false);
    }
  };

  const handleEditFeedback = () => {
    setIsEditingFeedback(true);
  };

  const handleCancelFeedback = () => {
    setFeedbackText(currentSubmission?.feedback || "");
    setIsEditingFeedback(false);
  };

  const handleOpenFeedbackDialog = () => {
    setIsFeedbackDialogOpen(true);
  };

  const handleFeedbackSubmit = async (feedbackData: any) => {
    // In a real app, this would submit to the backend
    console.log("Feedback submitted:", feedbackData);

    // Update the current submission with the feedback
    // This would normally be handled by refreshing from the backend
    toast.success("Feedback submitted successfully!");
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Navigation Controls */}
      <SubmissionNavigationControls
        onOpenSandbox={handleOpenSandbox}
        showSandboxControls={true}
      />

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="max-w-7xl mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="outline" onClick={goBack}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Submissions
              </Button>

              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Submission Review
                </h1>
                <p className="text-gray-500">
                  {currentSubmission.studentName} • {assignmentTitle}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                <RefreshCw
                  className={`h-4 w-4 mr-2 ${
                    isRefreshing ? "animate-spin" : ""
                  }`}
                />
                Refresh
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Submission Overview */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center space-x-2">
                      <User className="h-5 w-5" />
                      <span>{currentSubmission.studentName}</span>
                    </CardTitle>
                    <div
                      className={`flex items-center space-x-2 px-3 py-1 rounded-full border ${statusInfo.bg} ${statusInfo.border}`}
                    >
                      <StatusIcon className={`h-4 w-4 ${statusInfo.color}`} />
                      <span
                        className={`text-sm font-medium ${statusInfo.color} capitalize`}
                      >
                        {currentSubmission.status}
                      </span>
                    </div>
                  </div>
                  <CardDescription>
                    Student ID: {currentSubmission.studentId} • Submitted{" "}
                    {formatDistanceToNow(
                      new Date(currentSubmission.submissionDate),
                      { addSuffix: true }
                    )}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-600">Submission Date</p>
                        <p className="font-medium">
                          {new Date(
                            currentSubmission.submissionDate
                          ).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <GraduationCap className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-600">Current Grade</p>
                        <p className="font-medium">
                          {currentSubmission.grade
                            ? `${currentSubmission.grade}/100`
                            : "Not graded"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="flex flex-wrap gap-2">
                    <Button
                      onClick={handleOpenSandbox}
                      className="bg-bits-blue hover:bg-bits-blue/90"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      {sessionStatus?.status === "active"
                        ? "Open Sandbox"
                        : "Launch Sandbox"}
                    </Button>
                    <Button variant="outline" onClick={handleModelTesting}>
                      <BarChart3 className="h-4 w-4 mr-2" />
                      Test Model
                    </Button>
                    <Button variant="outline" onClick={handleViewCode}>
                      <Eye className="h-4 w-4 mr-2" />
                      View Code
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Detailed Tabs */}
              <Card>
                <CardHeader>
                  <CardTitle>Submission Details</CardTitle>
                </CardHeader>
                <CardContent className="flex-1 flex flex-col min-h-0">
                  <CustomTabs
                    value={activeTab}
                    onValueChange={setActiveTab}
                    className="flex-1 flex flex-col min-h-0"
                  >
                    <div className="flex-shrink-0">
                      <CustomTabsList>
                        <CustomTabsTrigger value="overview">
                          Overview
                        </CustomTabsTrigger>
                        <CustomTabsTrigger value="files">
                          Files
                        </CustomTabsTrigger>
                        <CustomTabsTrigger value="history">
                          History
                        </CustomTabsTrigger>
                      </CustomTabsList>
                    </div>

                    <div className="flex-1 overflow-auto min-h-0">
                      <CustomTabsContent value="overview" className="space-y-4">
                        <div>
                          <h4 className="font-medium mb-2">
                            Submission Summary
                          </h4>
                          <p className="text-gray-600">
                            {currentSubmission.summary ||
                              "No summary provided for this submission."}
                          </p>
                        </div>

                        <Separator />

                        <div>
                          <h4 className="font-medium mb-2">
                            Performance Metrics
                          </h4>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="p-3 border rounded-lg">
                              <p className="text-sm text-gray-600">Accuracy</p>
                              <p className="text-lg font-semibold">
                                {currentSubmission.metrics?.accuracy || "N/A"}
                              </p>
                            </div>
                            <div className="p-3 border rounded-lg">
                              <p className="text-sm text-gray-600">
                                Execution Time
                              </p>
                              <p className="text-lg font-semibold">
                                {currentSubmission.metrics?.executionTime ||
                                  "N/A"}
                              </p>
                            </div>
                          </div>
                        </div>
                      </CustomTabsContent>

                      <CustomTabsContent value="files" className="space-y-4">
                        <div>
                          <h4 className="font-medium mb-3">Submitted Files</h4>
                          <div className="space-y-2">
                            {currentSubmission.files?.map((file, index) => (
                              <div
                                key={index}
                                className="flex items-center justify-between p-3 border rounded-lg"
                              >
                                <div className="flex items-center space-x-2">
                                  <FileText className="h-4 w-4 text-gray-500" />
                                  <span className="font-medium">
                                    {file.name}
                                  </span>
                                  <Badge variant="outline">{file.type}</Badge>
                                </div>
                                <Button variant="ghost" size="sm">
                                  <ExternalLink className="h-3 w-3" />
                                </Button>
                              </div>
                            )) || (
                              <p className="text-gray-500 text-center py-4">
                                No files submitted
                              </p>
                            )}
                          </div>
                        </div>
                      </CustomTabsContent>

                      <CustomTabsContent value="history" className="space-y-4">
                        <div>
                          <h4 className="font-medium mb-4">
                            Submission Timeline
                          </h4>
                          <div className="relative">
                            {/* Timeline line */}
                            <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                            <div className="space-y-6">
                              {getSubmissionHistory().map((event, index) => {
                                const Icon = event.icon;
                                const isLatest = index === 0;

                                return (
                                  <div
                                    key={event.id}
                                    className="relative flex items-start space-x-4"
                                  >
                                    {/* Timeline dot */}
                                    <div
                                      className={`
                                      relative z-10 flex items-center justify-center w-12 h-12 rounded-full border-2 
                                      ${
                                        event.status === "completed"
                                          ? "bg-green-50 border-green-200"
                                          : event.status === "in-progress"
                                          ? "bg-blue-50 border-blue-200 animate-pulse"
                                          : "bg-gray-50 border-gray-200"
                                      }
                                    `}
                                    >
                                      <Icon
                                        className={`
                                        w-5 h-5 
                                        ${
                                          event.status === "completed"
                                            ? "text-green-600"
                                            : event.status === "in-progress"
                                            ? "text-blue-600"
                                            : "text-gray-600"
                                        }
                                      `}
                                      />
                                    </div>

                                    {/* Event content */}
                                    <div className="flex-1 min-w-0">
                                      <div
                                        className={`
                                        p-4 rounded-lg border
                                        ${
                                          isLatest
                                            ? "bg-blue-50 border-blue-200"
                                            : "bg-white border-gray-200"
                                        }
                                      `}
                                      >
                                        <div className="flex items-start justify-between">
                                          <div className="flex-1">
                                            <h5 className="font-medium text-gray-900">
                                              {event.title}
                                            </h5>
                                            <p className="text-sm text-gray-600 mt-1">
                                              {event.description}
                                            </p>

                                            {/* Event details */}
                                            {event.details && (
                                              <div className="mt-2 space-y-1">
                                                {event.details.files && (
                                                  <p className="text-xs text-gray-500">
                                                    Files: {event.details.files}
                                                  </p>
                                                )}
                                                {event.details.grade && (
                                                  <p className="text-xs text-gray-500">
                                                    Grade: {event.details.grade}
                                                    /100
                                                  </p>
                                                )}
                                                {event.details.gradedBy && (
                                                  <p className="text-xs text-gray-500">
                                                    Graded by:{" "}
                                                    {event.details.gradedBy}
                                                  </p>
                                                )}
                                                {event.details.feedback && (
                                                  <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                                                    {event.details.feedback
                                                      .length > 100
                                                      ? `${event.details.feedback.substring(
                                                          0,
                                                          100
                                                        )}...`
                                                      : event.details.feedback}
                                                  </div>
                                                )}
                                                {event.details.providedBy && (
                                                  <p className="text-xs text-gray-500">
                                                    Provided by:{" "}
                                                    {event.details.providedBy}
                                                  </p>
                                                )}
                                              </div>
                                            )}
                                          </div>

                                          <div className="ml-4 text-right flex-shrink-0">
                                            <p className="text-sm font-medium text-gray-900">
                                              {event.timestamp.toLocaleDateString()}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                              {event.timestamp.toLocaleTimeString(
                                                [],
                                                {
                                                  hour: "2-digit",
                                                  minute: "2-digit",
                                                }
                                              )}
                                            </p>
                                            <Badge
                                              variant={
                                                event.status === "completed"
                                                  ? "default"
                                                  : "secondary"
                                              }
                                              className="mt-1"
                                            >
                                              {event.status.replace("-", " ")}
                                            </Badge>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>

                            {/* If no history */}
                            {getSubmissionHistory().length === 0 && (
                              <div className="text-center py-8">
                                <GitBranch className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                                <p className="text-gray-500">
                                  No submission history available.
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      </CustomTabsContent>
                    </div>
                  </CustomTabs>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Sandbox Status */}
              <SandboxStatusPanel showCompactView />

              {/* Quick Stats */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Stats</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Grade</span>
                    <span className="font-medium">
                      {currentSubmission.grade
                        ? `${currentSubmission.grade}/100`
                        : "Pending"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Attempts</span>
                    <span className="font-medium">1</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Time Spent</span>
                    <span className="font-medium">2h 45m</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Files</span>
                    <span className="font-medium">
                      {currentSubmission.files?.length || 0}
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={handleOpenFeedbackDialog}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Add Feedback
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={handleDownload}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download Files
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Feedback Dialog */}
      <FeedbackDialog
        isOpen={isFeedbackDialogOpen}
        onClose={() => setIsFeedbackDialogOpen(false)}
        studentName={currentSubmission.studentName}
        assignmentTitle={assignmentTitle}
        currentFeedback={currentSubmission.feedback}
        currentGrade={currentSubmission.grade}
        onSubmit={handleFeedbackSubmit}
      />
    </div>
  );
}
