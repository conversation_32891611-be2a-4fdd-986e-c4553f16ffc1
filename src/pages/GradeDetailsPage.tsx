import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Progress } from "../components/ui/progress";
import {
  Tabs,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "../components/ui/tabs";
import {
  ArrowLeft,
  Award,
  Calendar,
  User,
  FileText,
  MessageSquare,
  BookOpen,
  Target,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Download,
  Eye,
  Flag,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
// import { useAuth, useNavigation } from "../App";
import useAuth from "../components/Context/AuthContext";
import useNavigation from "../components/Context/NavigationContext";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../components/ui/dialog";
import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionI<PERSON>,
  AccordionTrigger,
} from "../components/ui/accordion";

// Define the component's props interface
interface GradeDetailsPageProps {
  project: Project; // Use the Project interface defined in the parent
}

interface Project {
  id: string;
  trimester: number;
  title: string;
  description: string;
  subject: string;
  instructor: string;
  dueDate: string;
  createdDate: string;
  status: "not_started" | "in_progress" | "submitted" | "graded";
  progress: number;
  grade?: number;
  totalPoints: number;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
  objectives: string[];
  requirements: string[];
  datasets: Dataset[];
  submissions: Submission[];
  rubric: RubricItem[];
  estimatedHours: number;
  tags: string[];
  checkpoints: Checkpoint[];
}

interface Checkpoint {
  id: string;
  title: string;
  description: string;
  status: "not_started" | "in_progress" | "submitted" | "graded" | "overdue";
  dueDate: string;
  submittedDate?: string;
  grade?: string;
  instructorFeedback?: string[];
}

interface Dataset {
  id: string;
  name: string;
  description: string;
  format: string;
  size: string;
  downloadUrl: string;
}

interface Submission {
  id: string;
  submittedAt: string;
  files: string[];
  grade?: number;
  feedback?: string;
  status: "submitted" | "graded" | "revision_needed";
}

interface RubricItem {
  id: string;
  criteria: string;
  description: string;
  points: number;
  earnedPoints?: number;
}

interface InstructorFeedbackProps {
  feedback: string[];
}

const InstructorFeedback: React.FC<InstructorFeedbackProps> = ({
  feedback,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrev = () => {
    setCurrentIndex((prev) => Math.max(0, prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => Math.min(feedback.length - 1, prev + 1));
  };

  if (!feedback || feedback.length === 0) {
    return null;
  }

  return (
    <div className="bg-gray-100 rounded-lg p-4 border">
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center text-base font-semibold ">
          <MessageSquare className="h-5 w-5 mr-3 text-bits-orange" />
          Instructor Feedback
        </div>
        {feedback.length > 1 && (
          <div className="flex items-center">
            <Button
              variant="destructive"
              size="icon"
              onClick={handlePrev}
              disabled={currentIndex === 0}
              className="h-7 w-7 text-gray-500 hover:bg-gray-200"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <Button
              variant="destructive"
              size="icon"
              onClick={handleNext}
              disabled={currentIndex === feedback.length - 1}
              className="h-7 w-7 text-gray-500 hover:bg-gray-200"
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
        )}
      </div>
      <p className="text-sm text-muted-foreground leading-relaxed">
        {feedback[currentIndex]}
      </p>
    </div>
  );
};

// Helper to get status badge styles
const getStatusBadge = (status: Checkpoint["status"]) => {
  switch (status) {
    case "graded":
      return <Badge className="bg-green-100 text-bits-green">Completed</Badge>;
    case "submitted":
      return <Badge className="bg-blue-100 text-bits-blue">Submitted</Badge>;
    case "overdue":
      return <Badge variant="destructive">Overdue</Badge>;
    default:
      return <Badge variant="secondary">In Progress</Badge>;
  }
};

// Helper to format dates
const formatDateRange = (dateString: string) => {
  // This is a placeholder. Use a robust date library like date-fns or moment.js
  // For "12 jun - 16 Jul 2025", this logic would need to be more complex.
  // For now, we just return the string.
  return dateString;
};

export default function GradeDetailsPage({ project }: GradeDetailsPageProps) {
  // Calculate overall score from the rubric
  const overallScore = project.rubric.reduce(
    (sum, item) => sum + (item.earnedPoints || 0),
    0
  );
  const totalPossibleScore = project.rubric.reduce(
    (sum, item) => sum + item.points,
    0
  );

  // Function to convert score to letter grade (example logic)
  const getLetterGrade = (score: number, total: number) => {
    const percentage = total > 0 ? (score / total) * 100 : 0;
    if (percentage >= 90) return "A";
    if (percentage >= 80) return "B";
    if (percentage >= 70) return "C";
    if (percentage >= 60) return "D";
    return "F";
  };

  const overallGrade = getLetterGrade(overallScore, totalPossibleScore);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Eye className="h-4 w-4 mr-2" />
          View Details
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto bg-white border custom-scroll">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-start">
            <div>
              <h2 className="text-2xl font-bold">{project.title}</h2>
              <p className="text-base text-muted-foreground">
                {project.subject}
              </p>
            </div>
            <Badge
              className={
                project.status === "graded"
                  ? "bg-green-100 text-bits-green mr-8"
                  : "bg-yellow-100 text-bits-warning mr-8"
              }
            >
              {project.status === "in_progress" ? "In Progress" : "Completed"}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-2 md:grid-cols-2 gap-6 pb-6 pt-2 border-b">
          <div>
            <p className="font-semibold">Instructor</p>
            <p className=" text-sm font-medium text-muted-foreground">
              {project.instructor}
            </p>
          </div>
          <div>
            <p className="font-semibold ">Timeline</p>
            <p className="text-sm font-medium text-muted-foreground">
              {formatDateRange(project.dueDate)}
            </p>
          </div>
          <div>
            <p className="font-semibold">Overall Score</p>
            <p className="text-sm font-medium text-muted-foreground">{`${overallScore}/${totalPossibleScore}`}</p>
          </div>
          <div>
            <p className="font-semibold">Grade</p>
            <p className="text-sm font-medium text-muted-foreground">
              {overallGrade}
            </p>
          </div>
        </div>

        <div className="space-y-6 mt-2">
          <h3 className="flex items-center text-xl font-semibold">
            <Flag className="h-5 w-5 mr-3 text-purple-600" />
            Checkpoint Grades
          </h3>
          <div className="space-y-4">
            {project.checkpoints.map((checkpoint) => (
              <div key={checkpoint.id} className="p-4 border rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <div>
                    <h4 className="font-semibold text-lg">
                      {checkpoint.title}
                    </h4>
                    <p className="text-base text-muted-foreground">
                      {project.subject} • {project.instructor}
                    </p>
                  </div>
                  {getStatusBadge(checkpoint.status)}
                </div>

                <div className="flex items-center text-lg font-bold mb-4 mt-2">
                  {checkpoint.grade && (
                    <p className="text-base text-gray-600 font-medium">
                      <span className="text-3xl text-gray-800 bg font-bold">
                        {checkpoint.grade}
                      </span>{" "}
                      Grade
                    </p>
                  )}
                </div>

                {checkpoint.instructorFeedback &&
                  checkpoint.instructorFeedback.length > 0 && (
                    <InstructorFeedback
                      feedback={checkpoint.instructorFeedback}
                    />
                  )}

                <div className="flex items-center text-sm text-muted-foreground mt-4">
                  <Calendar className="h-4 w-4 mr-2" />
                  <span>{formatDateRange(checkpoint.dueDate)}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
