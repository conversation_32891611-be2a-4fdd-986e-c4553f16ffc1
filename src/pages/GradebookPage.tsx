import React, { use<PERSON>em<PERSON>, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { But<PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import {
  ArrowLeft,
  Search,
  Filter,
  Download,
  Edit,
  Eye,
  Award,
  TrendingUp,
  BarChart3,
  Users,
  BookOpen,
  Calculator,
  Calendar,
  FileText,
  User,
  CheckCircle,
  Save,
  X,
  BarChart,
} from "lucide-react";
 import { useAuth, useNavigation } from "../App";
// import useAuth from "../components/Context/AuthContext";
// import useNavigation from "../components/Context/NavigationContext";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import { Progress } from "../components/ui/progress";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../components/ui/dialog";
import { Textarea } from "../components/ui/textarea";
import { UserProfileModal } from "../components/UserProfileModal";
import { toast } from "sonner";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "../components/ui/pagination";
import GradeDetailsPage from "./GradeDetailsPage";

// Rubric Interface
interface RubricItem {
  id: string;
  criteria: string;
  description: string;
  points: number;
  earnedPoints?: number;
}

// Mock student data based on current user
const currentStudent = {
  id: "1",
  name: "Rahul Sharma",
  email: "<EMAIL>",
  overallGPA: 3.8,
  completedProjects: 12,
  inProgressProjects: 3,
  totalPoints: 850,
  maxPoints: 1000,
  projects: [
    {
      id: "1",
      name: "Linear Regression Analysis",
      status: "Submitted",
      grade: "A-",
      submissionDate: "2025-01-12",
      course: "Data Science 101",
      instructor: "Dr. A. Sharma",
    },
    {
      id: "2",
      name: "Classification Algorithms",
      status: "In Progress",
      grade: "Pending",
      submissionDate: "2025-01-20",
      course: "Machine Learning",
      instructor: "Dr. B. Patel",
    },
    {
      id: "3",
      name: "Data Visualization Project",
      status: "Submitted",
      grade: "B+",
      submissionDate: "2025-01-05",
      course: "Data Visualization",
      instructor: "Dr. C. Kumar",
    },
    {
      id: "4",
      name: "Statistical Analysis Report",
      status: "Submitted",
      grade: "A",
      submissionDate: "2024-12-15",
      course: "Statistics",
      instructor: "Dr. D. Singh",
    },
  ],
  recentGrades: [
    { project: "Linear Regression Analysis", grade: "A-", date: "2025-01-15" },
    { project: "Data Visualization Project", grade: "B+", date: "2025-01-08" },
    { project: "Statistical Analysis Report", grade: "A", date: "2024-12-18" },
  ],
};

// Mock data for other views with rubric information
const mockStudents = [
  {
    id: "1",
    name: "Rahul Sharma",
    email: "<EMAIL>",
    overallGrade: "A-",
    completedAssignments: 8,
    totalAssignments: 10,
    lastSubmission: "2025-01-12",
    role: "student",
    avatar:
      "https://ui-avatars.com/api/?name=Rahul+Sharma&background=3B82F6&color=fff",
    joinDate: "2024-08-15",
    department: "Computer Science & Engineering",
    year: "Third Year",
    studentId: "F20230001",
    phone: "+91 98765 43210",
    address: "Pilani, Rajasthan, India",
    status: "Active",
    projects: [
      {
        id: "1",
        name: "Linear Regression Analysis",
        grade: "A-",
        status: "Submitted",
        score: 87,
        maxScore: 100,
        feedback:
          "Excellent work on data preprocessing and model implementation.",
        rubric: [
          {
            id: "r1",
            criteria: "Data Exploration & Visualization",
            description:
              "Comprehensive exploration of the dataset with meaningful visualizations",
            points: 20,
            earnedPoints: 18,
          },
          {
            id: "r2",
            criteria: "Data Preprocessing",
            description:
              "Proper handling of missing values, outliers, and data transformation",
            points: 20,
            earnedPoints: 15,
          },
          {
            id: "r3",
            criteria: "Feature Engineering",
            description:
              "Creation of meaningful features and feature selection",
            points: 25,
            earnedPoints: 24,
          },
          {
            id: "r4",
            criteria: "Model Implementation",
            description:
              "Implementation of multiple regression models with proper validation",
            points: 25,
            earnedPoints: 22,
          },
          {
            id: "r5",
            criteria: "Analysis & Interpretation",
            description:
              "Clear interpretation of results and model performance",
            points: 10,
            earnedPoints: 8,
          },
        ],
      },
      {
        id: "2",
        name: "Classification Project",
        grade: "B+",
        status: "Submitted",
        score: 83,
        maxScore: 100,
        feedback: "Good analysis but could improve feature selection.",
        rubric: [
          {
            id: "r1",
            criteria: "Algorithm Implementation",
            description: "Complete implementation of classification algorithms",
            points: 30,
            earnedPoints: 26,
          },
          {
            id: "r2",
            criteria: "Model Evaluation",
            description:
              "Proper evaluation with multiple metrics and validation",
            points: 25,
            earnedPoints: 20,
          },
          {
            id: "r3",
            criteria: "Code Quality",
            description: "Well-documented and structured code",
            points: 20,
            earnedPoints: 17,
          },
          {
            id: "r4",
            criteria: "Results Analysis",
            description: "Clear analysis and interpretation of results",
            points: 25,
            earnedPoints: 20,
          },
        ],
      },
      {
        id: "3",
        name: "Data Visualization",
        grade: "A",
        status: "Submitted",
        score: 92,
        maxScore: 100,
        feedback: "Outstanding visualization techniques and insights.",
        rubric: [
          {
            id: "r1",
            criteria: "Visualization Design",
            description: "Creative and effective visualization design",
            points: 40,
            earnedPoints: 38,
          },
          {
            id: "r2",
            criteria: "Data Storytelling",
            description: "Clear narrative and insights from visualizations",
            points: 35,
            earnedPoints: 32,
          },
          {
            id: "r3",
            criteria: "Technical Implementation",
            description: "Proper use of visualization libraries and techniques",
            points: 25,
            earnedPoints: 22,
          },
        ],
      },
    ],
  },
  {
    id: "2",
    name: "Priya Patel",
    email: "<EMAIL>",
    overallGrade: "B+",
    completedAssignments: 7,
    totalAssignments: 10,
    lastSubmission: "2025-01-10",
    role: "student",
    avatar:
      "https://ui-avatars.com/api/?name=Priya+Patel&background=3B82F6&color=fff",
    joinDate: "2024-08-15",
    department: "Computer Science & Engineering",
    year: "Third Year",
    studentId: "F20230002",
    phone: "+91 98765 43211",
    address: "Mumbai, Maharashtra, India",
    status: "Active",
    projects: [
      {
        id: "1",
        name: "Linear Regression Analysis",
        grade: "B+",
        status: "Submitted",
        score: 82,
        maxScore: 100,
        feedback: "Solid understanding of concepts, minor improvements needed.",
        rubric: [
          {
            id: "r1",
            criteria: "Data Exploration & Visualization",
            description:
              "Comprehensive exploration of the dataset with meaningful visualizations",
            points: 20,
            earnedPoints: 16,
          },
          {
            id: "r2",
            criteria: "Data Preprocessing",
            description:
              "Proper handling of missing values, outliers, and data transformation",
            points: 20,
            earnedPoints: 17,
          },
          {
            id: "r3",
            criteria: "Feature Engineering",
            description:
              "Creation of meaningful features and feature selection",
            points: 25,
            earnedPoints: 20,
          },
          {
            id: "r4",
            criteria: "Model Implementation",
            description:
              "Implementation of multiple regression models with proper validation",
            points: 25,
            earnedPoints: 21,
          },
          {
            id: "r5",
            criteria: "Analysis & Interpretation",
            description:
              "Clear interpretation of results and model performance",
            points: 10,
            earnedPoints: 8,
          },
        ],
      },
      {
        id: "2",
        name: "Classification Project",
        grade: "B",
        status: "Submitted",
        score: 78,
        maxScore: 100,
        feedback: "Good effort, but accuracy could be improved.",
        rubric: [
          {
            id: "r1",
            criteria: "Algorithm Implementation",
            description: "Complete implementation of classification algorithms",
            points: 30,
            earnedPoints: 24,
          },
          {
            id: "r2",
            criteria: "Model Evaluation",
            description:
              "Proper evaluation with multiple metrics and validation",
            points: 25,
            earnedPoints: 18,
          },
          {
            id: "r3",
            criteria: "Code Quality",
            description: "Well-documented and structured code",
            points: 20,
            earnedPoints: 16,
          },
          {
            id: "r4",
            criteria: "Results Analysis",
            description: "Clear analysis and interpretation of results",
            points: 25,
            earnedPoints: 20,
          },
        ],
      },
      {
        id: "3",
        name: "Data Visualization",
        grade: "A-",
        status: "Submitted",
        score: 88,
        maxScore: 100,
        feedback: "Creative visualizations with good insights.",
        rubric: [
          {
            id: "r1",
            criteria: "Visualization Design",
            description: "Creative and effective visualization design",
            points: 40,
            earnedPoints: 35,
          },
          {
            id: "r2",
            criteria: "Data Storytelling",
            description: "Clear narrative and insights from visualizations",
            points: 35,
            earnedPoints: 30,
          },
          {
            id: "r3",
            criteria: "Technical Implementation",
            description: "Proper use of visualization libraries and techniques",
            points: 25,
            earnedPoints: 23,
          },
        ],
      },
    ],
  },
  {
    id: "3",
    name: "Arjun Singh",
    email: "<EMAIL>",
    overallGrade: "A",
    completedAssignments: 9,
    totalAssignments: 10,
    lastSubmission: "2025-01-11",
    role: "student",
    avatar:
      "https://ui-avatars.com/api/?name=Arjun+Singh&background=3B82F6&color=fff",
    joinDate: "2024-08-15",
    department: "Computer Science & Engineering",
    year: "Third Year",
    studentId: "F20230003",
    phone: "+91 98765 43212",
    address: "Delhi, India",
    status: "Active",
    projects: [
      {
        id: "1",
        name: "Linear Regression Analysis",
        grade: "A",
        status: "Submitted",
        score: 95,
        maxScore: 100,
        feedback: "Exceptional work with thorough analysis and documentation.",
        rubric: [
          {
            id: "r1",
            criteria: "Data Exploration & Visualization",
            description:
              "Comprehensive exploration of the dataset with meaningful visualizations",
            points: 20,
            earnedPoints: 19,
          },
          {
            id: "r2",
            criteria: "Data Preprocessing",
            description:
              "Proper handling of missing values, outliers, and data transformation",
            points: 20,
            earnedPoints: 19,
          },
          {
            id: "r3",
            criteria: "Feature Engineering",
            description:
              "Creation of meaningful features and feature selection",
            points: 25,
            earnedPoints: 25,
          },
          {
            id: "r4",
            criteria: "Model Implementation",
            description:
              "Implementation of multiple regression models with proper validation",
            points: 25,
            earnedPoints: 24,
          },
          {
            id: "r5",
            criteria: "Analysis & Interpretation",
            description:
              "Clear interpretation of results and model performance",
            points: 10,
            earnedPoints: 8,
          },
        ],
      },
      {
        id: "2",
        name: "Classification Project",
        grade: "A-",
        status: "Submitted",
        score: 89,
        maxScore: 100,
        feedback: "Strong implementation with good model selection.",
        rubric: [
          {
            id: "r1",
            criteria: "Algorithm Implementation",
            description: "Complete implementation of classification algorithms",
            points: 30,
            earnedPoints: 28,
          },
          {
            id: "r2",
            criteria: "Model Evaluation",
            description:
              "Proper evaluation with multiple metrics and validation",
            points: 25,
            earnedPoints: 23,
          },
          {
            id: "r3",
            criteria: "Code Quality",
            description: "Well-documented and structured code",
            points: 20,
            earnedPoints: 18,
          },
          {
            id: "r4",
            criteria: "Results Analysis",
            description: "Clear analysis and interpretation of results",
            points: 25,
            earnedPoints: 20,
          },
        ],
      },
      {
        id: "3",
        name: "Data Visualization",
        grade: "A",
        status: "Submitted",
        score: 94,
        maxScore: 100,
        feedback: "Excellent storytelling through data visualization.",
        rubric: [
          {
            id: "r1",
            criteria: "Visualization Design",
            description: "Creative and effective visualization design",
            points: 40,
            earnedPoints: 38,
          },
          {
            id: "r2",
            criteria: "Data Storytelling",
            description: "Clear narrative and insights from visualizations",
            points: 35,
            earnedPoints: 34,
          },
          {
            id: "r3",
            criteria: "Technical Implementation",
            description: "Proper use of visualization libraries and techniques",
            points: 25,
            earnedPoints: 22,
          },
        ],
      },
    ],
  },
];

// Rubric-Based Grading Modal Component
function RubricGradingModal({
  open,
  onOpenChange,
  student,
  project,
  onSave,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  student: any;
  project: any;
  onSave: (gradeData: any) => void;
}) {
  const [rubricGrades, setRubricGrades] = useState<{ [key: string]: number }>(
    {}
  );
  const [feedback, setFeedback] = useState(project?.feedback || "");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize rubric grades when modal opens
  React.useEffect(() => {
    if (project?.rubric) {
      const initialGrades: { [key: string]: number } = {};
      project.rubric.forEach((item: RubricItem) => {
        initialGrades[item.id] = item.earnedPoints || 0;
      });
      setRubricGrades(initialGrades);
    }
  }, [project]);

  const handleRubricChange = (rubricId: string, value: string) => {
    const numValue = parseFloat(value) || 0;
    setRubricGrades((prev) => ({
      ...prev,
      [rubricId]: numValue,
    }));
  };

  const calculateTotalScore = () => {
    const totalEarned = Object.values(rubricGrades).reduce(
      (sum, points) => sum + points,
      0
    );
    const totalPossible =
      project?.rubric?.reduce(
        (sum: number, item: RubricItem) => sum + item.points,
        0
      ) || 0;
    return { totalEarned, totalPossible };
  };

  const calculateLetterGrade = (percentage: number) => {
    if (percentage >= 95) return "A";
    if (percentage >= 90) return "A-";
    if (percentage >= 87) return "B+";
    if (percentage >= 83) return "B";
    if (percentage >= 80) return "B-";
    if (percentage >= 77) return "C+";
    if (percentage >= 73) return "C";
    if (percentage >= 70) return "C-";
    if (percentage >= 67) return "D+";
    if (percentage >= 65) return "D";
    return "F";
  };

  const handleSave = async () => {
    // Validate all rubric items have been graded
    const ungraded = project?.rubric?.filter(
      (item: RubricItem) =>
        !rubricGrades[item.id] && rubricGrades[item.id] !== 0
    );

    if (ungraded?.length > 0) {
      toast.error("Please grade all rubric criteria before saving");
      return;
    }

    // Validate points don't exceed maximum
    const invalidGrades = project?.rubric?.filter(
      (item: RubricItem) => rubricGrades[item.id] > item.points
    );

    if (invalidGrades?.length > 0) {
      toast.error("Points cannot exceed maximum for any criteria");
      return;
    }

    setIsSubmitting(true);

    try {
      const { totalEarned, totalPossible } = calculateTotalScore();
      const percentage =
        totalPossible > 0 ? (totalEarned / totalPossible) * 100 : 0;
      const letterGrade = calculateLetterGrade(percentage);

      const gradeData = {
        studentId: student.id,
        projectId: project.id,
        score: totalEarned,
        maxScore: totalPossible,
        letterGrade,
        feedback,
        rubricGrades,
        gradedAt: new Date().toISOString(),
      };

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      onSave(gradeData);
      onOpenChange(false);

      toast.success(`Grade saved successfully for ${student.name}`);

      // Reset form
      setRubricGrades({});
      setFeedback("");
    } catch (error) {
      toast.error("Failed to save grade. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    // Reset form
    if (project?.rubric) {
      const initialGrades: { [key: string]: number } = {};
      project.rubric.forEach((item: RubricItem) => {
        initialGrades[item.id] = item.earnedPoints || 0;
      });
      setRubricGrades(initialGrades);
    }
    setFeedback(project?.feedback || "");
  };

  const { totalEarned, totalPossible } = calculateTotalScore();
  const percentage =
    totalPossible > 0 ? (totalEarned / totalPossible) * 100 : 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto bg-white border">
        <DialogHeader>
          <DialogTitle>Grade Assignment - Rubric-Based Grading</DialogTitle>
          <DialogDescription>
            Grade {project?.name} for {student?.name} using the project rubric
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Student Info */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-bits-blue rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="font-medium">{student?.name}</p>
                <p className="text-sm text-muted-foreground">
                  {student?.email}
                </p>
              </div>
            </div>
          </div>

          {/* Project Info */}
          <div className="p-4 border rounded-lg">
            <div className="flex items-center space-x-3">
              <FileText className="h-5 w-5 text-bits-blue" />
              <div>
                <p className="font-medium">{project?.name}</p>
                <p className="text-sm text-muted-foreground">
                  Status: {project?.status}
                </p>
              </div>
            </div>
          </div>

          {/* Rubric Grading */}
          <div className="space-y-4">
            <h3 className="font-medium">Grading Rubric</h3>

            {project?.rubric?.map((item: RubricItem, index: number) => (
              <div key={item.id} className="border rounded-lg p-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{item.criteria}</h4>
                      <p className="text-xs text-muted-foreground mt-1">
                        {item.description}
                      </p>
                    </div>
                    <div className="text-right ml-4">
                      <p className="text-xs text-muted-foreground">
                        Max: {item.points} pts
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Label
                      htmlFor={`rubric-${item.id}`}
                      className="text-sm min-w-0"
                    >
                      Points:
                    </Label>
                    <Input
                      id={`rubric-${item.id}`}
                      type="number"
                      min="0"
                      max={item.points}
                      step="0.5"
                      value={rubricGrades[item.id] || ""}
                      onChange={(e) =>
                        handleRubricChange(item.id, e.target.value)
                      }
                      className="w-20"
                      placeholder="0"
                    />
                    <span className="text-sm text-muted-foreground">
                      / {item.points}
                    </span>
                  </div>

                  {/* Progress bar showing completion */}
                  <Progress
                    value={
                      rubricGrades[item.id]
                        ? (rubricGrades[item.id] / item.points) * 100
                        : 0
                    }
                    className="h-2"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Total Score Summary */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Total Score</p>
                <p className="text-xs text-muted-foreground">
                  {totalEarned.toFixed(1)} / {totalPossible} points
                </p>
              </div>
              <div className="text-right">
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-bold">
                    {percentage.toFixed(1)}%
                  </span>
                  <Badge variant="outline" className="text-sm">
                    {calculateLetterGrade(percentage)}
                  </Badge>
                </div>
              </div>
            </div>
            <Progress value={percentage} className="mt-2 h-2" />
          </div>

          {/* Feedback */}
          <div className="space-y-2">
            <Label htmlFor="feedback">Overall Feedback (Optional)</Label>
            <Textarea
              id="feedback"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Enter overall feedback for the student..."
              rows={4}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={isSubmitting}
              className="bg-bits-blue hover:bg-bits-blue/90"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSubmitting ? "Saving..." : "Save Grade"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function StudentGradebookView() {
  interface Project {
    id: string;
    trimester: number;
    title: string;
    description: string;
    subject: string;
    instructor: string;
    dueDate: string;
    createdDate: string;
    status: "not_started" | "in_progress" | "submitted" | "graded";
    progress: number;
    grade?: number;
    totalPoints: number;
    difficulty: "Beginner" | "Intermediate" | "Advanced";
    objectives: string[];
    requirements: string[];
    datasets: Dataset[];
    submissions: Submission[];
    rubric: RubricItem[];
    estimatedHours: number;
    tags: string[];
    checkpoints: Checkpoint[];
  }

  interface Checkpoint {
    id: string;
    title: string;
    description: string;
    status: "not_started" | "in_progress" | "submitted" | "graded" | "overdue";
    dueDate: string;
    submittedDate?: string;
    grade?: string;
    instructorFeedback?: string;
  }

  interface Dataset {
    id: string;
    name: string;
    description: string;
    format: string;
    size: string;
    downloadUrl: string;
  }

  interface Submission {
    id: string;
    submittedAt: string;
    files: string[];
    grade?: number;
    feedback?: string;
    status: "submitted" | "graded" | "revision_needed";
  }

  interface RubricItem {
    id: string;
    criteria: string;
    description: string;
    points: number;
    earnedPoints?: number;
  }

  const projectsData: Project[] = [
    {
      id: "proj_1",
      trimester: 2,
      title: "Housing Price Prediction",
      description:
        "In this comprehensive project, you will build and evaluate machine learning models to predict housing prices using the california housing dataset. You will apply data preprocessing techniques, feature engineering, and various regression algorithms to create an accurate prediction model.",
      subject: "Data Science Fundamentals",
      instructor: "Dr. A. Sharma",
      dueDate: "2025-01-15T23:59:00Z",
      createdDate: "2025-01-01T00:00:00Z",
      status: "graded",
      progress: 65,
      totalPoints: 100,
      difficulty: "Intermediate",
      estimatedHours: 15,
      tags: [
        "Machine Learning",
        "Regression",
        "Data Preprocessing",
        "Feature Engineering",
      ],
      objectives: [
        "Understand and apply data preprocessing techniques for real-world datasets",
        "Implement feature engineering strategies to improve model performance",
        "Build and compare multiple regression models (Linear, Ridge, Lasso)",
        "Evaluate model performance using appropriate metrics",
        "Create compelling visualizations to communicate findings",
        "Deploy your model using AWS SageMaker (optional)",
      ],
      requirements: [
        "Python programming proficiency",
        "Basic understanding of statistics and linear algebra",
        "Familiarity with pandas, numpy, and scikit-learn",
        "Jupyter Notebook environment",
        "Matplotlib/Seaborn for visualizations",
      ],
      datasets: [
        {
          id: "ds_1",
          name: "California Housing Dataset",
          description:
            "Housing data from the 1990 California census with 20,640 observations and 10 features including median income, house age, and location coordinates.",
          format: "CSV",
          size: "2.4 MB",
          downloadUrl: "/datasets/california_housing.csv",
        },
        {
          id: "ds_2",
          name: "Housing Features Dictionary",
          description:
            "Detailed description of all features in the housing dataset including data types, ranges, and definitions.",
          format: "PDF",
          size: "156 KB",
          downloadUrl: "/datasets/housing_data_dictionary.pdf",
        },
      ],
      submissions: [
        {
          id: "sub_1",
          submittedAt: "2025-01-05T14:30:00Z",
          files: ["housing_analysis_v1.ipynb", "preprocessing_utils.py"],
          status: "graded",
          grade: 75,
          feedback:
            "Good initial analysis. Consider improving feature engineering and model validation techniques.",
        },
      ],
      rubric: [
        {
          id: "r_1",
          criteria: "Data Exploration & Visualization",
          description:
            "Comprehensive exploration of the dataset with meaningful visualizations",
          points: 20,
          earnedPoints: 18,
        },
        {
          id: "r_2",
          criteria: "Data Preprocessing",
          description:
            "Proper handling of missing values, outliers, and data transformation",
          points: 20,
          earnedPoints: 15,
        },
        {
          id: "r_3",
          criteria: "Feature Engineering",
          description: "Creation of meaningful features and feature selection",
          points: 25,
        },
        {
          id: "r_4",
          criteria: "Model Implementation",
          description:
            "Implementation of multiple regression models with proper validation",
          points: 25,
        },
        {
          id: "r_5",
          criteria: "Analysis & Interpretation",
          description: "Clear interpretation of results and model performance",
          points: 10,
        },
      ],
      checkpoints: [
        {
          id: "cp_1",
          title: "Data Visualization",
          description:
            "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
          status: "graded",
          dueDate: "12 jun - 16 Jul 2025",
          grade: "A",
          instructorFeedback: "Lorem Ipsum",
        },
        {
          id: "cp_2",
          title: "Data Visualization",
          description:
            "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
          status: "graded",
          dueDate: "12 jun - 16 Jul 2025",
          grade: "A",
          instructorFeedback: "Lorem Ipsum",
        },
        {
          id: "cp_3",
          title: "Data Visualization",
          description:
            "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
          status: "graded",
          grade: "A",
          dueDate: "12 jun - 16 Jul 2025",
        },
      ],
    },
    {
      id: "proj_1",
      trimester: 2,
      title:
        "Housing Price Prediction comprehensive project, you will build and evaluate machine learning with modern science ",
      description:
        "In this comprehensive project, you will build and evaluate machine learning models to predict housing prices using the california housing dataset. You will apply data preprocessing techniques, feature engineering, and various regression algorithms to create an accurate prediction model.",
      subject: "Data Science Fundamentals",
      instructor: "Dr. A. Sharma",
      dueDate: "2025-01-15T23:59:00Z",
      createdDate: "2025-01-01T00:00:00Z",
      status: "in_progress",
      progress: 65,
      totalPoints: 100,
      difficulty: "Intermediate",
      estimatedHours: 15,
      tags: [
        "Machine Learning",
        "Regression",
        "Data Preprocessing",
        "Feature Engineering",
      ],
      objectives: [
        "Understand and apply data preprocessing techniques for real-world datasets",
        "Implement feature engineering strategies to improve model performance",
        "Build and compare multiple regression models (Linear, Ridge, Lasso)",
        "Evaluate model performance using appropriate metrics",
        "Create compelling visualizations to communicate findings",
        "Deploy your model using AWS SageMaker (optional)",
      ],
      requirements: [
        "Python programming proficiency",
        "Basic understanding of statistics and linear algebra",
        "Familiarity with pandas, numpy, and scikit-learn",
        "Jupyter Notebook environment",
        "Matplotlib/Seaborn for visualizations",
      ],
      datasets: [
        {
          id: "ds_1",
          name: "California Housing Dataset",
          description:
            "Housing data from the 1990 California census with 20,640 observations and 10 features including median income, house age, and location coordinates.",
          format: "CSV",
          size: "2.4 MB",
          downloadUrl: "/datasets/california_housing.csv",
        },
        {
          id: "ds_2",
          name: "Housing Features Dictionary",
          description:
            "Detailed description of all features in the housing dataset including data types, ranges, and definitions.",
          format: "PDF",
          size: "156 KB",
          downloadUrl: "/datasets/housing_data_dictionary.pdf",
        },
      ],
      submissions: [
        {
          id: "sub_1",
          submittedAt: "2025-01-05T14:30:00Z",
          files: ["housing_analysis_v1.ipynb", "preprocessing_utils.py"],
          status: "graded",
          grade: 75,
          feedback:
            "Good initial analysis. Consider improving feature engineering and model validation techniques.",
        },
      ],
      rubric: [
        {
          id: "r_1",
          criteria: "Data Exploration & Visualization",
          description:
            "Comprehensive exploration of the dataset with meaningful visualizations",
          points: 20,
          earnedPoints: 18,
        },
        {
          id: "r_2",
          criteria: "Data Preprocessing",
          description:
            "Proper handling of missing values, outliers, and data transformation",
          points: 20,
          earnedPoints: 15,
        },
        {
          id: "r_3",
          criteria: "Feature Engineering",
          description: "Creation of meaningful features and feature selection",
          points: 25,
        },
        {
          id: "r_4",
          criteria: "Model Implementation",
          description:
            "Implementation of multiple regression models with proper validation",
          points: 25,
        },
        {
          id: "r_5",
          criteria: "Analysis & Interpretation",
          description: "Clear interpretation of results and model performance",
          points: 10,
        },
      ],
      checkpoints: [
        {
          id: "cp_1",
          title: "Data Visualization",
          description:
            "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
          status: "submitted",
          dueDate: "12 jun - 16 Jul 2025",
          grade: "A",
          instructorFeedback: "Lorem Ipsum",
        },
        {
          id: "cp_2",
          title: "Data Visualization",
          description:
            "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
          status: "in_progress",
          dueDate: "12 jun - 16 Jul 2025",
          grade: "A",
          instructorFeedback: "Lorem Ipsum",
        },
        {
          id: "cp_3",
          title: "Data Visualization",
          description:
            "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
          status: "in_progress",
          dueDate: "12 jun - 16 Jul 2025",
        },
        {
          id: "cp_1",
          title: "Data Visualization",
          description:
            "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
          status: "submitted",
          dueDate: "12 jun - 16 Jul 2025",
          grade: "A",
          instructorFeedback: "Lorem Ipsum",
        },
        {
          id: "cp_1",
          title: "Data Visualization",
          description:
            "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
          status: "submitted",
          dueDate: "12 jun - 16 Jul 2025",
          grade: "A",
          instructorFeedback: "Lorem Ipsum",
        },
      ],
    },
    {
      id: "proj_1",
      trimester: 2,
      title: "Housing Price Prediction",
      description:
        "In this comprehensive project, you will build and evaluate machine learning models to predict housing prices using the california housing dataset. You will apply data preprocessing techniques, feature engineering, and various regression algorithms to create an accurate prediction model.",
      subject: "Data Science Fundamentals",
      instructor: "Dr. A. Sharma",
      dueDate: "2025-01-15T23:59:00Z",
      createdDate: "2025-01-01T00:00:00Z",
      status: "in_progress",
      progress: 65,
      totalPoints: 100,
      difficulty: "Intermediate",
      estimatedHours: 15,
      tags: [
        "Machine Learning",
        "Regression",
        "Data Preprocessing",
        "Feature Engineering",
      ],
      objectives: [
        "Understand and apply data preprocessing techniques for real-world datasets",
        "Implement feature engineering strategies to improve model performance",
        "Build and compare multiple regression models (Linear, Ridge, Lasso)",
        "Evaluate model performance using appropriate metrics",
        "Create compelling visualizations to communicate findings",
        "Deploy your model using AWS SageMaker (optional)",
      ],
      requirements: [
        "Python programming proficiency",
        "Basic understanding of statistics and linear algebra",
        "Familiarity with pandas, numpy, and scikit-learn",
        "Jupyter Notebook environment",
        "Matplotlib/Seaborn for visualizations",
      ],
      datasets: [
        {
          id: "ds_1",
          name: "California Housing Dataset",
          description:
            "Housing data from the 1990 California census with 20,640 observations and 10 features including median income, house age, and location coordinates.",
          format: "CSV",
          size: "2.4 MB",
          downloadUrl: "/datasets/california_housing.csv",
        },
        {
          id: "ds_2",
          name: "Housing Features Dictionary",
          description:
            "Detailed description of all features in the housing dataset including data types, ranges, and definitions.",
          format: "PDF",
          size: "156 KB",
          downloadUrl: "/datasets/housing_data_dictionary.pdf",
        },
      ],
      submissions: [
        {
          id: "sub_1",
          submittedAt: "2025-01-05T14:30:00Z",
          files: ["housing_analysis_v1.ipynb", "preprocessing_utils.py"],
          status: "graded",
          grade: 75,
          feedback:
            "Good initial analysis. Consider improving feature engineering and model validation techniques.",
        },
      ],
      rubric: [
        {
          id: "r_1",
          criteria: "Data Exploration & Visualization",
          description:
            "Comprehensive exploration of the dataset with meaningful visualizations",
          points: 20,
          earnedPoints: 18,
        },
        {
          id: "r_2",
          criteria: "Data Preprocessing",
          description:
            "Proper handling of missing values, outliers, and data transformation",
          points: 20,
          earnedPoints: 15,
        },
        {
          id: "r_3",
          criteria: "Feature Engineering",
          description: "Creation of meaningful features and feature selection",
          points: 25,
        },
        {
          id: "r_4",
          criteria: "Model Implementation",
          description:
            "Implementation of multiple regression models with proper validation",
          points: 25,
        },
        {
          id: "r_5",
          criteria: "Analysis & Interpretation",
          description: "Clear interpretation of results and model performance",
          points: 10,
        },
      ],
      checkpoints: [
        {
          id: "cp_1",
          title: "Data Visualization",
          description:
            "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
          status: "submitted",
          dueDate: "12 jun - 16 Jul 2025",
          grade: "A",
          instructorFeedback: "Lorem Ipsum",
        },
        {
          id: "cp_2",
          title: "Data Visualization",
          description:
            "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
          status: "in_progress",
          dueDate: "12 jun - 16 Jul 2025",
          grade: "A",
          instructorFeedback: "Lorem Ipsum",
        },
        {
          id: "cp_3",
          title: "Data Visualization",
          description:
            "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
          status: "in_progress",
          dueDate: "12 jun - 16 Jul 2025",
        },
      ],
    },
  ];

  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Memoize the sorted and filtered projects list
  const filteredProjects = useMemo(() => {
    const sorted = [...projectsData].sort((a, b) => b.trimester - a.trimester);

    if (!searchTerm) {
      return sorted;
    }
    return sorted.filter(
      (project) =>
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.subject.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [projectsData, searchTerm]);

  const totalPages = Math.ceil(filteredProjects.length / itemsPerPage);

  const paginatedProjects = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredProjects.slice(startIndex, endIndex);
  }, [filteredProjects, currentPage, itemsPerPage]);

  // --- Render Functions (Pagination logic remains the same) ---
  const renderPagination = () => {
    if (totalPages <= 1) return null;
    const pageNumbers: (number | string)[] = [];
    const maxPagesToShow = 5;
    if (totalPages <= maxPagesToShow + 2) {
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      pageNumbers.push(1);
      if (currentPage > 3) pageNumbers.push("ellipsis-start");
      let start = Math.max(2, currentPage - 1);
      let end = Math.min(totalPages - 1, currentPage + 1);
      if (currentPage <= 3) {
        start = 2;
        end = 4;
      }
      if (currentPage >= totalPages - 2) {
        start = totalPages - 3;
        end = totalPages - 1;
      }
      for (let i = start; i <= end; i++) pageNumbers.push(i);
      if (currentPage < totalPages - 2) pageNumbers.push("ellipsis-end");
      pageNumbers.push(totalPages);
    }
    return (
      <div className="flex justify-center mt-6">
        <Pagination className="border-none text-muted-foreground">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                className={`${
                  currentPage === 1
                    ? "pointer-events-none opacity-50"
                    : "cursor-pointer"
                } border-0`}
              />
            </PaginationItem>
            {pageNumbers.map((page, index) => {
              if (typeof page === "string")
                return (
                  <PaginationItem key={`${page}-${index}`}>
                    <PaginationEllipsis />
                  </PaginationItem>
                );
              const isActive = page === currentPage;
              return (
                <PaginationItem key={page}>
                  <PaginationLink
                    onClick={() => setCurrentPage(page)}
                    isActive={page === currentPage}
                    className={`cursor-pointer ${!isActive ? "border-0" : ""}`}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              );
            })}
            <PaginationItem>
              <PaginationNext
                onClick={() =>
                  setCurrentPage(Math.min(totalPages, currentPage + 1))
                }
                className={`${
                  currentPage === totalPages
                    ? "pointer-events-none opacity-50"
                    : "cursor-pointer"
                } border-0`}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-semibold">
              Project Grades
            </CardTitle>
            <div className="flex items-center space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search project, course..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table className="table-fixed w-full min-w-[900px]">
            <TableHeader className="overflow-hidden">
              <TableRow className="text-base font-semibold">
                <TableHead className="py-3 px-2 w-[7%]">Trimester</TableHead>
                <TableHead className="py-3 px-2 w-[33%]">Project</TableHead>
                <TableHead className="py-3 px-2 w-[10%]">Status</TableHead>
                <TableHead className="py-3 px-2 w-[10%]">
                  Overall Grade
                </TableHead>
                <TableHead className="py-3 px-2 w-[25%]">Progress</TableHead>
                <TableHead className="text-center py-3 px-2 ">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedProjects.map((project) => {
                const completedCheckpoints = project.checkpoints.filter(
                  (cp: Checkpoint) =>
                    cp.status === "graded" || cp.status === "submitted"
                ).length;
                const totalCheckpoints = project.checkpoints.length;
                const progressPercentage =
                  totalCheckpoints > 0
                    ? (completedCheckpoints / totalCheckpoints) * 100
                    : 0;
                return (
                  <TableRow key={project.id}>
                    {/* Trimester is now a regular column */}
                    <TableCell className="font-medium text-base text-center py-3 px-2">
                      {project.trimester}
                    </TableCell>
                    <TableCell className="py-3 px-2">
                      <div className="font-semibold text-base truncate line-clamp-1 mr-8">
                        {project.title}
                      </div>
                      <div className="text-sm text-muted-foreground line-clamp-2">
                        {project.subject}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          project.status === "graded" ? "default" : "secondary"
                        }
                        className={
                          project.status === "graded"
                            ? "bg-green-100 text-bits-green hover:bg-green-200"
                            : "bg-yellow-50 text-bits-warning hover:bg-yellow-100"
                        }
                      >
                        {project.status === "in_progress"
                          ? "In Progress"
                          : "Completed"}
                      </Badge>
                    </TableCell>
                    <TableCell>{"-"}</TableCell>
                    <TableCell>
                      <div className="flex flex-col space-y-1">
                        <span className="text-muted-foreground">{`${completedCheckpoints}/${totalCheckpoints} checkpoint submitted`}</span>
                        <Progress
                          value={progressPercentage}
                          className="h-1.5"
                        />
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <GradeDetailsPage project={project} />
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
          {renderPagination()}
        </CardContent>
      </Card>
    </div>
  );
}

function InstructorGradebookView() {
  const [selectedStudent, setSelectedStudent] = useState("all");
  const [selectedCourse, setSelectedCourse] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [showUserProfile, setShowUserProfile] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [showGradingModal, setShowGradingModal] = useState(false);
  const [gradingData, setGradingData] = useState<any>(null);
  const [students, setStudents] = useState(mockStudents);

  const handleViewStudent = (student: any) => {
    setSelectedUser(student);
    setShowUserProfile(true);
  };

  const handleGradeStudent = (student: any) => {
    // For demo purposes, we'll grade the first project
    const project = student.projects[0];
    setGradingData({ student, project });
    setShowGradingModal(true);
  };

  const handleSaveGrade = (gradeData: any) => {
    // Update the student's grade in the mock data
    setStudents((prevStudents) =>
      prevStudents.map((student) =>
        student.id === gradeData.studentId
          ? {
              ...student,
              projects: student.projects.map((project) =>
                project.id === gradeData.projectId
                  ? {
                      ...project,
                      grade: gradeData.letterGrade,
                      score: gradeData.score,
                      maxScore: gradeData.maxScore,
                      feedback: gradeData.feedback,
                      rubric: project.rubric?.map((item: RubricItem) => ({
                        ...item,
                        earnedPoints: gradeData.rubricGrades[item.id] || 0,
                      })),
                    }
                  : project
              ),
            }
          : student
      )
    );
  };

  const filteredStudents = students.filter(
    (student) =>
      student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Students
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-bits-blue">
              {mockStudents.length}
            </div>
            <p className="text-xs text-muted-foreground">Across all courses</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Grade</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">B+</div>
            <p className="text-xs text-muted-foreground">+0.1 from last term</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Submissions</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-bits-gold">24</div>
            <p className="text-xs text-muted-foreground">Pending grading</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">85%</div>
            <Progress value={85} className="mt-2 h-2" />
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search students..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedCourse} onValueChange={setSelectedCourse}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="All Courses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Courses</SelectItem>
                <SelectItem value="ds101">Data Science 101</SelectItem>
                <SelectItem value="ml102">Machine Learning 102</SelectItem>
                <SelectItem value="stats101">Statistics 101</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Students Table */}
      <Card>
        <CardHeader>
          <CardTitle>Student Grades</CardTitle>
          <CardDescription>Overview of all student performance</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Overall Grade</TableHead>
                <TableHead>Completed</TableHead>
                <TableHead>Last Submission</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStudents.map((student) => (
                <TableRow key={student.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{student.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {student.email}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={
                        student.overallGrade.startsWith("A")
                          ? "text-green-600 border-green-600"
                          : student.overallGrade.startsWith("B")
                          ? "text-yellow-600 border-yellow-600"
                          : "text-red-600 border-red-600"
                      }
                    >
                      {student.overallGrade}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Progress
                        value={
                          (student.completedAssignments /
                            student.totalAssignments) *
                          100
                        }
                        className="w-16 h-2"
                      />
                      <span className="text-sm text-muted-foreground">
                        {student.completedAssignments}/
                        {student.totalAssignments}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>{student.lastSubmission}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewStudent(student)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleGradeStudent(student)}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Grade
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* User Profile Modal */}
      <UserProfileModal
        user={selectedUser}
        open={showUserProfile}
        onOpenChange={setShowUserProfile}
      />

      {/* Rubric-Based Grading Modal */}
      <RubricGradingModal
        open={showGradingModal}
        onOpenChange={setShowGradingModal}
        student={gradingData?.student}
        project={gradingData?.project}
        onSave={handleSaveGrade}
      />
    </div>
  );
}

function AdminGradebookView() {
  const [searchTerm, setSearchTerm] = useState("");
  const users = [
    {
      id: "stu_1",
      name: "Rahul Sharma",
      email: "<EMAIL>",
      userId: "2021A7PS001G",
      role: "admin",
      phone: "+91 9876543210",
      address: "Mumbai, Maharashtra",
      dateOfBirth: "2003-05-15",
      enrollmentDate: "2021-08-01",
      status: "active",
      gpa: 8.5,
      totalCredits: 120,
      year: "3rd Year",
      department: "Computer Science",
      courses: [
        {
          courseId: "course_1",
          courseName: "Data Science Fundamentals",
          courseCode: "CS F301",
          instructor: "Dr. A. Sharma",
          enrollmentDate: "2025-01-01",
          currentGrade: "A-",
          status: "enrolled",
          role: "admin",
        },
        {
          courseId: "course_2",
          courseName: "Machine Learning Advanced",
          courseCode: "CS F401",
          instructor: "Dr. B. Patel",
          enrollmentDate: "2025-01-01",
          currentGrade: "B+",
          status: "enrolled",
          role: "admin",
        },
      ],
    },
  ];
  return (
    <div className="space-y-6">
      {/* Admin Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="flex flex-col py-6">
            <CardTitle className="text-2xl font-bold">200</CardTitle>
            {/* <BookOpen className="h-4 w-4 text-muted-foreground" /> */}
            <div className="text-sm text-bits-grey-600">Total Users</div>
          </CardHeader>
          {/* <CardContent>
            <p className="text-xs text-muted-foreground">
              Active this semester
            </p>
          </CardContent> */}
        </Card>

        <Card>
          <CardHeader className="flex flex-col py-6 ">
            <CardTitle className="text-2xl font-bold">200</CardTitle>
            <div className="text-sm text-bits-grey-600">Total Projects</div>
            {/* <Users className="h-4 w-4 text-muted-foreground" /> */}
          </CardHeader>
          {/* <CardContent>
            <p className="text-xs text-muted-foreground">
              Across all courses
            </p>
          </CardContent> */}
        </Card>

        {/* <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Performance</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-bits-gold">82%</div>
            <p className="text-xs text-muted-foreground">
              Platform average
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Submissions</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">1,247</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card> */}
      </div>

      {/* Course Performance Overview */}
      <Card>
        <div className="flex px-5 py-5 justify-between" style={{}}>
          <h1 className="text-xl font-weight-medium">Students Grade Summary</h1>
          <div
            className="flex items-end justify-end"
            style={{ width: "", gap: 10 }}
          >
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search members..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
            <Select defaultValue="all-courses">
              <SelectTrigger className="w-40 bg-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-courses">All courses</SelectItem>
                <SelectItem value="data-science">Data Science</SelectItem>
                <SelectItem value="machine-learning">
                  Machine Learning
                </SelectItem>
                <SelectItem value="deep-learning">Deep Learning</SelectItem>
              </SelectContent>
            </Select>
            <Select defaultValue="all-projects">
              <SelectTrigger className="w-40 bg-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-projects">All projects</SelectItem>
                <SelectItem value="1">1</SelectItem>
                <SelectItem value="2">2</SelectItem>
                <SelectItem value="3">4</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Average Grade</TableHead>
                <TableHead>Progress</TableHead>

                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* {filteredStudents.map((student) => ( */}
              <TableRow key={"student.id"}>
                <TableCell>
                  <div>
                    <div className="font-medium">{"Olivia Rhye"}</div>
                    <div className="text-sm text-muted-foreground">
                      {"<EMAIL>"}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    variant="outline"
                    className="font-medium bg-bits-light-grey rounded-lg"
                    style={{ backgroundColor: "#F2F4F7" }}
                  >
                    A+
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex flex-col">
                    <div className="flex items-center space-x-2">
                      <span className="font-normal text-bits-grey-600">
                        Linear Regression:
                      </span>
                      <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className={`h-full transition-all duration-300 ${
                            // 'bg-green-500'
                            "bg-bits-blue"
                            // 'bg-yellow-500'
                          }`}
                          style={{ width: `${85}%` }}
                        />
                      </div>
                      {/* <span className="text-xs">{85}%</span> */}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-normal text-bits-grey-600">
                        Gold price pre...:
                      </span>
                      <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className={`h-full transition-all duration-300 ${
                            // 'bg-green-500'
                            //  'bg-blue-500' :
                            " bg-bits-blue"
                            // 'bg-yellow-500'
                          }`}
                          style={{ width: `${85}%` }}
                        />
                      </div>
                      {/* <span className="text-xs">{85}%</span> */}
                    </div>
                  </div>
                </TableCell>

                <TableCell className="text-left">
                  <UserProfileModal
                    user={{
                      ...users[0],
                      courses: users?.[0]?.courses || [],
                    }}
                    fromPlatformGrade={true} // ✅ passing custom value
                    trigger={
                      <Button
                        size="sm"
                        variant="outline"
                        className="hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-colors"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Profile
                      </Button>
                    }
                  />
                </TableCell>
              </TableRow>
              {/* ))} */}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

export default function GradebookPage() {
  const { user } = useAuth();
  const { goBack } = useNavigation();

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {user?.role === "student" ? (
            <button
              onClick={goBack}
              className="p-2 rounded-full hover:bg-gray-100"
            >
              <ArrowLeft className="h-7 w-8 text-gray-700" />
            </button>
          ) : (
            ""
          )}
          <div>
            <h1 className="text-2xl font-bold ">
              {user?.role === "student" ? "My Grades" : "Platform Gradebook"}
            </h1>
            <p className="text-muted-foreground">
              {user?.role === "student"
                ? "View your academic performance and project grades"
                : user?.role === "instructor"
                ? "Manage student grades and track class performance"
                : "Overview of all student grades across the platform"}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {user?.role !== "student" && (
            <Button variant="outline" size="sm">
              <BarChart className="h-4 w-4" />
              Analytics
            </Button>
          )}
          {user?.role !== "student" && (
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4" />
              Export
            </Button>
          )}
        </div>
      </div>

      {/* Role-specific content */}
      {user?.role === "student" && <StudentGradebookView />}
      {user?.role === "instructor" && <InstructorGradebookView />}
      {user?.role === "admin" && <AdminGradebookView />}
    </div>
  );
}
