import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Input } from "../components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import {
  ArrowLeft,
  Search,
  Filter,
  Calendar,
  Clock,
  User,
  FileText,
  CheckCircle,
  AlertCircle,
  Star,
  MessageSquare,
  ExternalLink,
  Download,
} from "lucide-react";
import { useAuth, useNavigation } from "../App";
//import useAuth from "../components/Context/AuthContext";
//import useNavigation from "../components/Context/NavigationContext";
interface Submission {
  id: string;
  studentId: string;
  studentName: string;
  studentEmail: string;
  projectId: string;
  projectTitle: string;
  submittedAt: string;
  status: "submitted" | "grading" | "graded" | "returned";
  grade?: number;
  maxPoints: number;
  autoGrade?: number;
  timeSpent: string;
  lastActivity: string;
  commentsCount: number;
  notebookPath: string;
  hasLateSubmission: boolean;
  dueDate: string;
}

// Mock submissions data
const mockSubmissions: Submission[] = [
  {
    id: "sub_1",
    studentId: "student_1",
    studentName: "Rahul Sharma",
    studentEmail: "<EMAIL>",
    projectId: "proj_1",
    projectTitle: "Housing Price Prediction",
    submittedAt: "2025-01-15T14:30:00Z",
    status: "submitted",
    maxPoints: 100,
    autoGrade: 87,
    timeSpent: "4h 25m",
    lastActivity: "2025-01-15T14:28:00Z",
    commentsCount: 0,
    notebookPath: "/submissions/rahul_housing_prediction.ipynb",
    hasLateSubmission: false,
    dueDate: "2025-01-15T23:59:00Z",
  },
  {
    id: "sub_2",
    studentId: "student_2",
    studentName: "Priya Patel",
    studentEmail: "<EMAIL>",
    projectId: "proj_1",
    projectTitle: "Housing Price Prediction",
    submittedAt: "2025-01-16T08:15:00Z",
    status: "grading",
    grade: 92,
    maxPoints: 100,
    autoGrade: 89,
    timeSpent: "5h 12m",
    lastActivity: "2025-01-16T08:10:00Z",
    commentsCount: 3,
    notebookPath: "/submissions/priya_housing_prediction.ipynb",
    hasLateSubmission: true,
    dueDate: "2025-01-15T23:59:00Z",
  },
  {
    id: "sub_3",
    studentId: "student_3",
    studentName: "Arjun Singh",
    studentEmail: "<EMAIL>",
    projectId: "proj_1",
    projectTitle: "Housing Price Prediction",
    submittedAt: "2025-01-14T22:45:00Z",
    status: "graded",
    grade: 95,
    maxPoints: 100,
    autoGrade: 91,
    timeSpent: "3h 55m",
    lastActivity: "2025-01-14T22:40:00Z",
    commentsCount: 5,
    notebookPath: "/submissions/arjun_housing_prediction.ipynb",
    hasLateSubmission: false,
    dueDate: "2025-01-15T23:59:00Z",
  },
  {
    id: "sub_4",
    studentId: "student_4",
    studentName: "Sneha Kumar",
    studentEmail: "<EMAIL>",
    projectId: "proj_2",
    projectTitle: "Customer Segmentation Analysis",
    submittedAt: "2025-01-20T16:20:00Z",
    status: "returned",
    grade: 88,
    maxPoints: 120,
    autoGrade: 85,
    timeSpent: "6h 30m",
    lastActivity: "2025-01-20T16:15:00Z",
    commentsCount: 7,
    notebookPath: "/submissions/sneha_customer_segmentation.ipynb",
    hasLateSubmission: false,
    dueDate: "2025-01-20T23:59:00Z",
  },
];

function SubmissionCard({ submission }: { submission: Submission }) {
  const { navigateTo } = useNavigation();

  const getStatusColor = (status: string) => {
    switch (status) {
      case "submitted":
        return "bg-bits-blue";
      case "grading":
        return "bg-bits-gold";
      case "graded":
        return "bg-bits-blue";
      case "returned":
        return "bg-bits-red";
      default:
        return "bg-muted";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "submitted":
        return <FileText className="h-4 w-4" />;
      case "grading":
        return <Clock className="h-4 w-4" />;
      case "graded":
        return <CheckCircle className="h-4 w-4" />;
      case "returned":
        return <MessageSquare className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const timeAgo = (date: string) => {
    const now = new Date();
    const past = new Date(date);
    const diffInHours = Math.floor(
      (now.getTime() - past.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  const handleGradeSubmission = () => {
    navigateTo("grading-sandbox", {
      submissionId: submission.id,
      studentId: submission.studentId,
      projectId: submission.projectId,
      mode: "grading",
    });
  };

  const handleDownloadNotebook = () => {
    // Simulate download
    console.log("Downloading notebook:", submission.notebookPath);
  };

  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="font-semibold text-lg">
                {submission.studentName}
              </h3>
              {submission.hasLateSubmission && (
                <Badge variant="destructive" className="text-xs">
                  Late
                </Badge>
              )}
            </div>
            <p className="text-sm text-muted-foreground mb-1">
              {submission.studentEmail}
            </p>
            <p className="text-sm font-medium text-bits-blue">
              {submission.projectTitle}
            </p>
          </div>
          <div className="text-right">
            <Badge
              className={`${getStatusColor(submission.status)} text-white mb-2`}
            >
              <div className="flex items-center space-x-1">
                {getStatusIcon(submission.status)}
                <span className="capitalize">{submission.status}</span>
              </div>
            </Badge>
            {submission.grade !== undefined ? (
              <div>
                <div className="text-xl font-bold text-bits-gold">
                  {submission.grade}/{submission.maxPoints}
                </div>
                <div className="text-xs text-muted-foreground">Final Grade</div>
              </div>
            ) : (
              <div>
                <div className="text-lg font-medium text-bits-gold">
                  {submission.autoGrade}/{submission.maxPoints}
                </div>
                <div className="text-xs text-muted-foreground">Auto Grade</div>
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>Submitted: {timeAgo(submission.submittedAt)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span>Time spent: {submission.timeSpent}</span>
            </div>
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
              <span>{submission.commentsCount} comments</span>
            </div>
            <div className="flex items-center space-x-2">
              <Star className="h-4 w-4 text-muted-foreground" />
              <span>Auto: {submission.autoGrade}%</span>
            </div>
          </div>

          <div className="flex items-center justify-between pt-2">
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleDownloadNotebook}
              >
                <Download className="h-3 w-3 mr-1" />
                Download
              </Button>

              <Button
                size="sm"
                variant="outline"
                onClick={() =>
                  navigateTo("submission-details", {
                    submissionId: submission.id,
                  })
                }
              >
                <FileText className="h-3 w-3 mr-1" />
                Details
              </Button>
            </div>

            {submission.status === "submitted" ||
            submission.status === "grading" ? (
              <Button
                size="sm"
                onClick={handleGradeSubmission}
                className="bg-bits-blue hover:bg-bits-blue/90"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                {submission.status === "submitted"
                  ? "Start Grading"
                  : "Continue Grading"}
              </Button>
            ) : submission.status === "graded" ? (
              <Button
                size="sm"
                variant="outline"
                onClick={handleGradeSubmission}
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Review Grade
              </Button>
            ) : (
              <Button
                size="sm"
                variant="outline"
                onClick={handleGradeSubmission}
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                View Feedback
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function GradingPage() {
  const { user } = useAuth();
  const { goBack, pageParams } = useNavigation();

  const projectId = pageParams?.projectId;
  const [submissions] = useState<Submission[]>(mockSubmissions);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [sortBy, setSortBy] = useState("submitted_desc");

  // Filter and sort submissions
  const filteredSubmissions = submissions
    .filter((sub) => {
      const matchesSearch =
        sub.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sub.studentEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sub.projectTitle.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus =
        statusFilter === "all" || sub.status === statusFilter;
      const matchesProject = !projectId || sub.projectId === projectId;
      return matchesSearch && matchesStatus && matchesProject;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "submitted_desc":
          return (
            new Date(b.submittedAt).getTime() -
            new Date(a.submittedAt).getTime()
          );
        case "submitted_asc":
          return (
            new Date(a.submittedAt).getTime() -
            new Date(b.submittedAt).getTime()
          );
        case "grade_desc":
          return (b.grade || b.autoGrade || 0) - (a.grade || a.autoGrade || 0);
        case "grade_asc":
          return (a.grade || a.autoGrade || 0) - (b.grade || b.autoGrade || 0);
        case "name_asc":
          return a.studentName.localeCompare(b.studentName);
        default:
          return 0;
      }
    });

  const statusCounts = {
    all: submissions.length,
    submitted: submissions.filter((s) => s.status === "submitted").length,
    grading: submissions.filter((s) => s.status === "grading").length,
    graded: submissions.filter((s) => s.status === "graded").length,
    returned: submissions.filter((s) => s.status === "returned").length,
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={goBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-bits-blue">
              Assignment Grading
            </h1>
            <p className="text-muted-foreground mt-1">
              Review and grade student submissions
            </p>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{statusCounts.all}</div>
              <p className="text-sm text-muted-foreground">Total</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-bits-blue">
                {statusCounts.submitted}
              </div>
              <p className="text-sm text-muted-foreground">Submitted</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-bits-gold">
                {statusCounts.grading}
              </div>
              <p className="text-sm text-muted-foreground">Grading</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-bits-blue">
                {statusCounts.graded}
              </div>
              <p className="text-sm text-muted-foreground">Graded</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-bits-red">
                {statusCounts.returned}
              </div>
              <p className="text-sm text-muted-foreground">Returned</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by student name, email, or project..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>
            <div className="flex gap-3">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="submitted">Submitted</SelectItem>
                  <SelectItem value="grading">Grading</SelectItem>
                  <SelectItem value="graded">Graded</SelectItem>
                  <SelectItem value="returned">Returned</SelectItem>
                </SelectContent>
              </Select>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="submitted_desc">Latest First</SelectItem>
                  <SelectItem value="submitted_asc">Oldest First</SelectItem>
                  <SelectItem value="grade_desc">Highest Grade</SelectItem>
                  <SelectItem value="grade_asc">Lowest Grade</SelectItem>
                  <SelectItem value="name_asc">Name A-Z</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submissions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6">
        {filteredSubmissions.map((submission) => (
          <SubmissionCard key={submission.id} submission={submission} />
        ))}
      </div>

      {filteredSubmissions.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No submissions found</h3>
            <p className="text-muted-foreground">
              {searchTerm || statusFilter !== "all"
                ? "Try adjusting your search or filter criteria"
                : "No submissions have been made yet"}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
