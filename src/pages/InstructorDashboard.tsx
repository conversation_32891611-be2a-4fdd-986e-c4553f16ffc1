import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Progress } from "../components/ui/progress";
import {
  BookOpen,
  Users,
  Calendar,
  CheckCircle,
  Clock,
  AlertCircle,
  TrendingUp,
  MessageSquare,
  FileText,
  BarChart3,
  Settings,
  UserCheck,
  GraduationCap,
  ExternalLink,
  PenSquare,
  LinkIcon,
  UserIcon,
  Space,
  Edit,
} from "lucide-react";
import { useAuth, useNavigation } from "../App";
// import useAuth from "../components/Context/AuthContext";
// import useNavigation from "../components/Context/NavigationContext";
import { useNavigate } from "react-router-dom";
interface Course {
  id: string;
  name: string;
  code: string;
  students: number;
  assignments: number;
  pendingGrades: number;
  averageGrade: number;
  nextDeadline: string;
  status: "active" | "completed";
}

interface Assignment {
  id: string;
  title: string;
  course: string;
  startDate: string;
  dueDate: string;
  submissions: number;
  totalStudents: number;
  gradedSubmissions: number;
  status: "active" | "overdue" | "grading" | "completed";
}

interface RecentActivity {
  id: string;
  type: "submission" | "grade" | "message" | "announcement";
  description: string;
  time: string;
  course?: string;
}

// Mock data
const mockCourses: Course[] = [
  {
    id: "1",
    name: "Data Science Fundamentals",
    code: "CS F301",
    students: 45,
    assignments: 8,
    pendingGrades: 12,
    averageGrade: 85.2,
    nextDeadline: "2025-01-15T23:59:00Z",
    status: "active",
  },
  {
    id: "2",
    name: "Machine Learning Advanced",
    code: "CS F401",
    students: 32,
    assignments: 6,
    pendingGrades: 8,
    averageGrade: 78.9,
    nextDeadline: "2025-01-20T23:59:00Z",
    status: "active",
  },
  {
    id: "3",
    name: "Database Systems",
    code: "CS F302",
    students: 38,
    assignments: 7,
    pendingGrades: 0,
    averageGrade: 82.1,
    nextDeadline: "2025-08-25T23:59:00Z",
    status: "active",
  },
];

const mockAssignments: Assignment[] = [
  {
    id: "1",
    title: "Housing Price Prediction",
    course: "Data Science Fundamentals",
    startDate: "2025-08-01T16:32:00Z",
    dueDate: "2025-08-15T23:59:00Z",
    submissions: 38,
    totalStudents: 45,
    gradedSubmissions: 33,
    status: "grading",
  },
  {
    id: "2",
    title: "Neural Network Implementation",
    course: "Machine Learning Advanced",
    startDate: "2025-07-25T22:31:00Z",
    dueDate: "2025-08-02T23:59:00Z",
    submissions: 28,
    totalStudents: 32,
    gradedSubmissions: 20,
    status: "overdue",
  },
  {
    id: "3",
    title: "SQL Query Optimization",
    course: "Database Systems",
    startDate: "2025-07-12T21:44:00Z",
    dueDate: "2025-08-25T23:59:00Z",
    submissions: 0,
    totalStudents: 38,
    gradedSubmissions: 0,
    status: "active",
  },
];

const mockRecentActivity: RecentActivity[] = [
  {
    id: "1",
    type: "submission",
    description: "Rahul Sharma submitted Housing Price Prediction assignment",
    time: "10 minutes ago",
    course: "Data Science Fundamentals",
  },
  {
    id: "2",
    type: "grade",
    description: "Graded 5 assignments for Linear Regression project",
    time: "1 hour ago",
    course: "Data Science Fundamentals",
  },
  {
    id: "3",
    type: "message",
    description: "New message from Priya Patel about project clarification",
    time: "2 hours ago",
  },
  {
    id: "4",
    type: "announcement",
    description: "Posted announcement about midterm exam schedule",
    time: "3 hours ago",
    course: "Machine Learning Advanced",
  },
];

function CourseCard({
  course,
  assignment,
}: {
  course: Course;
  assignment: Assignment;
}) {
  const { navigateTo } = useNavigation();
const navigate = useNavigate()
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "overdue":
        return "bg-bits-red-100  text-bits-red-600";
      default:
        return "text-muted-foreground";
    }
  };

  function getDateDifference(startDate: string, dueDate: string) {
    const now = new Date();
    const due = new Date(dueDate);
    const start = new Date(startDate);
    const diffTime = due.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    // const diffHours = Math.floor((diffMs / (1000 * 60 * 60)) % 24);
    // const diffMinutes = Math.floor((diffMs / (1000 * 60)) % 60);
    // const diffSeconds = Math.floor((diffMs / 1000) % 60);

    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    var statusString = "On Track";

    const dateValue =
      start.getDate() +
      " " +
      monthNames[start.getMonth()] +
      " " +
      "-" +
      " " +
      due.getDate() +
      " " +
      monthNames[due.getMonth()] +
      " " +
      due.getFullYear();

    console.log(diffDays);
    console.log(dueDate);

    if (diffDays < 0) statusString = "Overdue";
    if (diffDays === 0) statusString = "Due today";
    if (diffDays === 1) statusString = "1 day left";
    if (diffDays > 0) statusString = diffDays + " days left";
    return dateValue + " (" + statusString + ")";
  }

  const submissionRate =
    ((course.students - course.pendingGrades) / course.students) * 100;

  const submissions = `${assignment.submissions} / ${assignment.totalStudents}`;
  const graded = `${assignment.gradedSubmissions} / ${assignment.submissions}`;

  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow duration-200">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex gap-4 items-start justify-between">
            <div>
              <CardTitle className="text-lg font-semibold text-bits-grey">
                {assignment.title}
              </CardTitle>
              <CardDescription>
                <div className="text-sm font-normal text-bits-grey-600">
                  {course.code}
                </div>
              </CardDescription>
            </div>
            <Badge
              className={getStatusColor(assignment.status)}
              variant="outline"
            >
              {assignment.status}
            </Badge>
          </div>
          <div className="flex gap-4 items-start justify-between">
            <Button
              onClick={() => {navigate(`/view-project-details/${assignment.id}`); 
              //  navigateTo("view-project-details", assignment.id)
              }}
              className="border-text-bits-grey-300"
              variant="outline"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button className="border-text-bits-grey-300" variant="outline">
              <LinkIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <div className="text-2xl font-bold text-bits-blue-neutral-900">
                {submissions}
              </div>
              <div className="text-bits-grey-600 font-normal text-sm">
                Submissions
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-bits-blue-neutral-900">
                {graded}
              </div>
              <div className="text-bits-grey-600 font-normal text-sm">
                Graded
              </div>
            </div>

            <div>
              <div className="text-2xl font-bold text-bits-blue-neutral-900">
                {course.averageGrade}%
              </div>
              <div className="text-bits-grey-600 font-normal text-sm">
                Avg Grade
              </div>
            </div>
          </div>

          <div>
            <div className="flex justify-between text-md font-medium text-bits-grey-600 mb-2">
              <span>Progress</span>
              <span>{submissionRate.toFixed(0)}%</span>
            </div>
            <Progress value={submissionRate} className="h-2" />
          </div>

          <div className="flex items-center gap-20 text-sm">
            <div className="flex items-center space-x-1 justify-between">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-bits-grey-600 text-sm font-semibold">
                {getDateDifference(assignment.startDate, assignment.dueDate)}
              </span>
            </div>
            <div className="flex items-start justify-between">
              <UserIcon className="h-4 w-4 text-muted-foreground" />
              <div className="text-muted-foreground">Your Role:</div>
              <div className="text-sm font-semibold ml-1 text-bits-grey">
                {useAuth().user?.role}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function AssignmentCard({ assignment }: { assignment: Assignment }) {
  const { navigateTo } = useNavigation();

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "text-bits-blue";
      case "grading":
        return "text-bits-gold";
      case "completed":
        return "text-bits-blue";
      default:
        return "text-muted-foreground";
    }
  };

  const submissionRate =
    (assignment.submissions / assignment.totalStudents) * 100;
  const gradingProgress =
    assignment.submissions > 0
      ? (assignment.gradedSubmissions / assignment.submissions) * 100
      : 0;

  return (
    <Card
      className="cursor-pointer hover:shadow-md transition-shadow duration-200"
      onClick={() => navigateTo("submissions", { assignmentId: assignment.id })}
    >
      <CardHeader>
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-lg">{assignment.title}</CardTitle>
            <CardDescription>{assignment.course}</CardDescription>
          </div>
          <Badge
            className={getStatusColor(assignment.status)}
            variant="outline"
          >
            {assignment.status}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="text-lg font-bold">
                {assignment.submissions}/{assignment.totalStudents}
              </div>
              <div className="text-muted-foreground">Submissions</div>
            </div>
            <div>
              <div className="text-lg font-bold">
                {assignment.gradedSubmissions}
              </div>
              <div className="text-muted-foreground">Graded</div>
            </div>
          </div>

          <div>
            <div className="flex justify-between text-xs mb-1">
              <span>Submission Rate</span>
              <span>{submissionRate.toFixed(0)}%</span>
            </div>
            <Progress value={submissionRate} className="h-2 mb-2" />

            <div className="flex justify-between text-xs mb-1">
              <span>Grading Progress</span>
              <span>{gradingProgress.toFixed(0)}%</span>
            </div>
            <Progress value={gradingProgress} className="h-2" />
          </div>

          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                Due: {new Date(assignment.dueDate).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function InstructorDashboard() {
  const { user, logout } = useAuth();
  const { navigateTo } = useNavigation();
  const [courses] = useState<Course[]>(mockCourses);
  const [assignments] = useState<Assignment[]>(mockAssignments);
  const [recentActivity] = useState<RecentActivity[]>(mockRecentActivity);

  const totalStudents = courses.reduce(
    (sum, course) => sum + course.students,
    0
  );
  const totalPendingGrades = courses.reduce(
    (sum, course) => sum + course.pendingGrades,
    0
  );
  const activeCourses = courses.filter((c) => c.status === "active").length;
  const averageGrade =
    courses.reduce((sum, course) => sum + course.averageGrade, 0) /
    courses.length;

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "submission":
        return <FileText className="h-4 w-4 text-bits-blue" />;
      case "grade":
        return <CheckCircle className="h-4 w-4 text-bits-gold" />;
      case "message":
        return <MessageSquare className="h-4 w-4 text-bits-gold" />;
      case "announcement":
        return <AlertCircle className="h-4 w-4 text-bits-gold" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  function getRecentActivity(recentActivity: RecentActivity[]) {
    var count = 0;
    var summary = "";
    recentActivity.forEach((rec: RecentActivity) => {
      if (rec.type === "grade") {
        count++;
        summary = `${count} Assignments Graded`;
      }
    });
    return summary;
  }

  const getCurrentAssignment = (
    assignments: Assignment[],
    courseName: string
  ): Assignment | undefined => {
    return assignments.find((assignment) => assignment.course === courseName);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-semibold text-bits-blue">
            Welcome back, {user?.name}!
          </h1>
          <p className="text-muted-foreground font-md mt-1">
            Manage your courses and student progress
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {/* Header actions can be added here if needed */}
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 justify-between">
              <div>
                <p className="text-sm font-normal text-muted-foreground">
                  Active Projects
                </p>
                <div className="text-3xl font-bold text-bits-blue-neutral-900">
                  {activeCourses}
                </div>
              </div>
              <BookOpen className="h-5 w-5 text-bits-blue" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 justify-between">
              <div>
                <p className="text-sm font-normal text-muted-foreground">
                  Total Students
                </p>
                <div className="text-3xl font-bold text-bits-blue-neutral-900">
                  {totalStudents}
                </div>
              </div>
              <BookOpen className="h-5 w-5 text-bits-blue" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 justify-between">
              <div>
                <p className="text-sm font-normal text-muted-foreground">
                  Pending Grades
                </p>
                <div className="text-3xl font-bold text-bits-blue-neutral-900">
                  {totalPendingGrades}
                </div>
              </div>
              <BookOpen className="h-5 w-5 text-bits-blue" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 justify-between">
              <div>
                <p className="text-sm font-normal text-muted-foreground">
                  Average Grade
                </p>
                <div className="text-3xl font-bold text-bits-blue-neutral-900">
                  {averageGrade.toFixed(1)}%
                </div>
              </div>
              <Users className="h-5 w-5 text-bits-blue" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Courses */}
        <div className="lg:col-span-2">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-bits-blue">
              Your Projects
            </h2>
            <Button
              variant="outline"
              onClick={() => navigateTo("view-all-project")}
            >
              <ExternalLink className="h-4 w-4 mr-2 text-bits-grey-700" />
              <div className="text-sm font-semibold text-bits-grey-700">
                View All Project
              </div>
            </Button>
          </div>
          <div className="space-y-4 text-lg font-semibold">
            {courses.map((course) => (
              <CourseCard
                key={course.id}
                course={course}
                assignment={assignments.find((a) => a.course === course.name)!}
              />
            ))}
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <div className="flex gap-4 items-start">
                <MessageSquare className="h-6 w-6 text-bits-blue " />
                <div className="text-lg font-medium text-bits-grey">
                  <CardTitle>Recent Activity</CardTitle>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-bits-green-success-25 w-full mb-3">
                <div className="flex items-start justify-end pl-2 gap-x-4">
                  <CheckCircle className="h-5 w-5 text-bits-green mt-3" />
                  <div className="items-start justify between">
                    <div className="text-md font-bold pt-2">
                      {getRecentActivity(recentActivity)}
                    </div>
                    <div>
                      <div className="pl-2 pr-2">
                        <div className="items-start justify-between">
                          {recentActivity.map((activity) => (
                            <div
                              key={activity.id}
                              className="flex items-start space-x-3"
                            >
                              <div className="flex-1 min-w-0 ">
                                <p className="text-sm font-normal text-muted-foreground pb-1">
                                  {activity.type === "grade"
                                    ? activity.description
                                    : null}
                                </p>
                              </div>
                              <div className="text-xs font-normal text-muted-foreground pb-1">
                                {activity.type === "grade"
                                  ? activity.time
                                  : null}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-bits-blue-50 w-full">
                <div className="flex items-start justify-end gap-x-4 pl-2">
                  <CheckCircle className="h-5 w-5 mt-3 text-bits-blue-500" />
                  <div className="items-start justify between">
                    <div className="text-md font-bold pt-2">
                      Project Submitted
                    </div>
                    <div>
                      <div className="pl-2 pr-2">
                        <div className="items-start justify-between">
                          {recentActivity.map((activity) => (
                            <div
                              key={activity.id}
                              className="flex items-start space-x-3"
                            >
                              <div className="flex-1 min-w-0 ">
                                <p className="text-sm font-normal text-muted-foreground pb-1">
                                  {activity.type === "submission"
                                    ? activity.description
                                    : null}
                                </p>
                              </div>
                              <div className="text-xs font-normal text-muted-foreground pb-1">
                                {activity.type === "submission"
                                  ? activity.time
                                  : null}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
