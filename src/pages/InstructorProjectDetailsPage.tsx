import { useState } from "react";
import { useNavigation } from "../App";
//import useNavigation from "../components/Context/NavigationContext";
import { publishProject, unpublishProject, getProjectById } from "../api/projectManagementApi";
import { parseISO, format } from "date-fns";
import { AxiosError } from "axios";
import paths from "../routes";
import {
  ArrowLeft,
  Clock,
  Users,
  User,
  Clipboard,
  FileText,
  Flag,
  Calendar,
  BarChart,
  Award,
  TrendingUp,
  Check,
  Search,
  Download,
  Eye,
  MessageSquare,
  MoreVertical,
  Pencil,
  LinkIcon,
  Trash2,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { cn } from "../lib/utils";

import { Progress } from "../components/ui/progress";
import { Input } from "../components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
} from "../components/ui/dropdown-menu";
import { useQueryClient,useMutation,useQuery } from "@tanstack/react-query";
import { DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu";
import {toast } from 'sonner'
import { useNavigate, useParams } from "react-router-dom";
interface Project {
  id: string;
  title: string;
  description: string;
  subject: string;
  instructor: string;
  dueDate: string;
  createdDate: string;
  status: "not_started" | "in_progress" | "submitted" | "graded";
  progress: number;
  grade?: number;
  totalPoints: number;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
  objectives: string[];
  requirements: string[];
  datasets: Dataset[];
  submissions: Submission[];
  rubric: RubricItem[];
  estimatedHours: number;
  totalStudents: number;
  tags: string[];
  checkpoints: Checkpoint[];
}

interface Course {
  id: string;
  name: string;
  code: string;
  description: string;
  instructor: string;
  semester: string;
  credits: number;
  startDate: string;
  endDate: string;
  enrolledStudents: number;
  maxCapacity: number;
  status: "active" | "inactive" | "draft";
  category: string;
}

interface Checkpoint {
  id: string;
  title: string;
  description: string;
  status: "not_started" | "in_progress" | "submitted" | "graded";
  dueDate: string;
  submittedDate?: string;
  grade?: string;
  instructorFeedback?: string;
}

interface Dataset {
  id: string;
  name: string;
  description: string;
  format: string;
  size: string;
  downloadUrl: string;
}

interface Submission {
  id: string;
  studentId: string;
  submittedAt: string;
  files: string[];
  grade?: number;
  feedback?: string;
  status: "submitted" | "graded" | "revision_needed";
}

interface RubricItem {
  id: string;
  criteria: string;
  description: string;
  points: number;
  earnedPoints?: number;
}

interface instructor {
  name: string;
  role: "TA" | "Instructor";
  email: string;
}

interface Student {
  id: string;
  name: string;
  email: string;
  studentId: string;
  status: string;
  checkPointSubmitted: number;
  totalCheckPoints: number;
}
export interface ProjectResponse {
  id: string;
  title: string;
  description: string;
  instructions: string;
  course_id: string;
  created_by: string;
  status: string; // add other statuses if needed
  difficulty_level: "beginner" | "intermediate" | "advanced"; // extend as needed
  estimated_hours: number;
  notebook_template_s3_url: string | null;

  dataset_s3_url: DatasetFile[];
  additional_files_s3_urls: DatasetFile[];

  start_date: string | null;
  due_date: string | null;
  late_submission_allowed: boolean;
  late_penalty_percent: string;
  max_attempts: number;
  auto_grading_enabled: boolean;
  learning_objectives: string | null;
  prerequisites: string | null;
  tags: string[];
  settings: Record<string, any>;
  isScreen: number;
  project_code: string;
  category_id: string;

  instructor_id: string[];
  teaching_ass_id: string[];

  total_points: number;
  project_overview: string | null;
  type: "individual" | "group"; // extend if more types exist
  sandbox_time_duration: number | null;
  late_submission_days_allowed: number;

  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;

  course: ProjectCourse;
  creator: UserBasic;
  rubrics: Rubric[];
  checkpoints: ProjectCheckpoint[];
  template: any; // refine if template structure is known
  assignments: any[]; // refine if structure is known
  assignmentStats: AssignmentStats;

  instructors: UserWithStatus[];
  teachingAssistants: UserWithStatus[];
  students: UserWithStatus[];

  Submission: {
    count: number;
    rows: any[]; // refine if structure is known
  };
}
export interface UserBasic {
  id: string;
  name: string;
  email: string;
}

export interface UserWithStatus extends UserBasic {
  status: string;
}
export interface Rubric {
  id: string;
  title: string;
  description: string;
  criteria: Criterion[];
  total_points: string;
  grading_scale: Record<string, any>;
  is_template: boolean;
  template_name: string | null;
}
interface Criterion {
  name: string;
  points: number;
  description: string;
}

export interface ProjectCourse {
  id: string;
  name: string;
  code: string;
  term: string;
  description: string;
}

interface ProjectCheckpoint {
  id: string;
  title: string;
  description: string;
  checkpoint_number: number;
  due_date: string;
  status: string | any;
  is_required: boolean;
  metadata: Record<string, any>;
}

interface AssignmentStats {
  totalAssignments: number;
  byRole: Record<string, number>;
  roles: string[];
}

const students: Student[] = [
  {
    id: "1",
    name: "Rahul Sharma",
    email: "<EMAIL>",
    studentId: "2023001",
    status: "Active",
    checkPointSubmitted: 2,
    totalCheckPoints: 5,
  },
  {
    id: "2",
    name: "Amit Kumar",
    email: "<EMAIL>",
    studentId: "2023002",
    status: "Active",
    checkPointSubmitted: 1,
    totalCheckPoints: 5,
  },
  {
    id: "3",
    name: "Sneha Singh",
    email: "<EMAIL>",
    studentId: "2023003",
    status: "Active",
    checkPointSubmitted: 4,
    totalCheckPoints: 5,
  },
  {
    id: "4",
    name: "Priya Patel",
    email: "<EMAIL>",
    studentId: "2023004",
    status: "Active",
    checkPointSubmitted: 3,
    totalCheckPoints: 5,
  },
  {
    id: "5",
    name: "Raj Patel",
    email: "<EMAIL>",
    studentId: "2023005",
    status: "Active",
    checkPointSubmitted: 2,
    totalCheckPoints: 5,
  },
];

// Mock courses data
const mockCourses: Course[] = [
  {
    id: "1",
    name: "Data Science 101",
    code: "DS101",
    description:
      "Introduction to Data Science covering fundamentals of statistics, programming, and machine learning.",
    instructor: "Dr. A. Sharma",
    semester: "Fall 2024",
    credits: 4,
    startDate: "2024-08-15",
    endDate: "2024-12-15",
    enrolledStudents: 30,
    maxCapacity: 35,
    status: "active",
    category: "Data Science",
  },
  {
    id: "2",
    name: "Machine Learning Fundamentals",
    code: "ML201",
    description:
      "Advanced machine learning concepts including supervised and unsupervised learning algorithms.",
    instructor: "Dr. B. Patel",
    semester: "Fall 2024",
    credits: 4,
    startDate: "2024-08-15",
    endDate: "2024-12-15",
    enrolledStudents: 25,
    maxCapacity: 30,
    status: "active",
    category: "Machine Learning",
  },
  {
    id: "3",
    name: "Deep Learning Advanced",
    code: "DL301",
    description:
      "Advanced deep learning techniques including neural networks, CNNs, and RNNs.",
    instructor: "Dr. C. Singh",
    semester: "Spring 2025",
    credits: 4,
    startDate: "2025-01-15",
    endDate: "2025-05-15",
    enrolledStudents: 0,
    maxCapacity: 25,
    status: "draft",
    category: "Deep Learning",
  },
];

// instructor sample data
// const instructors: instructor[] = [
//   {
//     name: "Ms. L. Patel",
//     role: "TA",
//     email: "<EMAIL>",
//   },
//   {
//     name: "Ms. R. Sharma",
//     role: "Instructor",
//     email: "<EMAIL>",
//   },
// ];

// Mall Customers Project Data
const mallCustomersProjects: { [key: string]: Project } = {
  "mall-customers-part-1": {
    id: "mall-customers-part-1",
    title: "Mall Customers - Part 1: K-Means Clustering Implementation",
    description:
      "Implement k-means clustering algorithm from scratch to segment mall customers based on their purchasing behavior. This foundational project focuses on understanding unsupervised learning principles, implementing clustering algorithms, and analyzing customer segments for business insights.",
    subject: "Machine Learning",
    instructor: "Dr. A. Sharma",
    dueDate: "2025-01-20T23:59:00Z",
    createdDate: "2025-01-10T00:00:00Z",
    status: "in_progress",
    progress: 60,
    totalPoints: 100,
    difficulty: "Intermediate",
    estimatedHours: 20,
    totalStudents: 35,
    tags: [
      "K-Means",
      "Clustering",
      "Unsupervised Learning",
      "Customer Segmentation",
      "Python",
      "NumPy",
    ],
    objectives: [
      "Implement k-means clustering algorithm from scratch without using sklearn",
      "Apply the algorithm to real mall customer data with age, income, and spending score",
      "Determine optimal number of clusters using elbow method and silhouette analysis",
      "Analyze customer segments and identify distinct purchasing behavior patterns",
      "Create comprehensive visualizations showing cluster distributions and characteristics",
      "Validate clustering results and assess algorithm performance",
      "Document implementation with clear code comments and methodology explanation",
    ],
    requirements: [
      "Python programming proficiency with NumPy and Pandas",
      "Understanding of distance metrics (Euclidean distance)",
      "Basic linear algebra concepts (centroids, vector operations)",
      "Data visualization skills with Matplotlib/Seaborn",
      "Jupyter Notebook environment setup",
      "Understanding of unsupervised learning concepts",
      "Basic statistics knowledge for cluster validation",
    ],
    datasets: [
      {
        id: "mall_customers_main",
        name: "Mall Customers Dataset",
        description:
          "Customer data from a mall containing 200 customer records with demographics and spending behavior. Features include CustomerID, Gender, Age, Annual Income (k$), and Spending Score (1-100).",
        format: "CSV",
        size: "8.2 KB",
        downloadUrl: "/datasets/mall_customers.csv",
      },
      {
        id: "mall_customers_dictionary",
        name: "Data Dictionary",
        description:
          "Comprehensive description of all features in the mall customers dataset including value ranges, data types, and business context for each attribute.",
        format: "PDF",
        size: "245 KB",
        downloadUrl: "/datasets/mall_customers_data_dictionary.pdf",
      },
      {
        id: "clustering_starter_code",
        name: "K-Means Starter Template",
        description:
          "Jupyter notebook template with data loading utilities, visualization functions, and code structure to help you get started with the k-means implementation.",
        format: "IPYNB",
        size: "156 KB",
        downloadUrl: "/templates/kmeans_starter_template.ipynb",
      },
    ],
    submissions: [
      {
        id: "sub_mc1_1",
        studentId: "2023001",
        submittedAt: "2025-01-15T14:30:00Z",
        files: [
          "kmeans_implementation_v1.ipynb",
          "clustering_utils.py",
          "visualization_plots.png",
        ],
        status: "graded",
        grade: 78,
        feedback:
          "Good implementation of k-means algorithm with proper centroid initialization. Your elbow method analysis is thorough. Consider improving cluster validation metrics and adding more detailed business interpretation of segments. The visualization quality is excellent.",
      },
      {
        id: "sub_mc2_1",
        studentId: "2023002",
        submittedAt: "2025-01-18T16:45:00Z",
        files: [
          "customer_personas_draft.ipynb",
          "interactive_dashboard.html",
          "business_insights.pdf",
        ],
        status: "graded",
        grade: 82,
        feedback:
          "Excellent customer persona development with clear business names for clusters. Your interactive dashboard is well-designed and informative. Consider adding more specific marketing recommendations and competitor analysis to strengthen business insights.",
      },
      {
        id: "sub_1",
        studentId: "2023003",
        submittedAt: "2025-01-05T14:30:00Z",
        files: ["housing_analysis_v1.ipynb", "preprocessing_utils.py"],
        status: "graded",
        grade: 75,
        feedback:
          "Good initial analysis. Consider improving feature engineering and model validation techniques.",
      },
    ],
    rubric: [
      {
        id: "mc1_r1",
        criteria: "K-Means Algorithm Implementation",
        description:
          "Complete from-scratch implementation with proper initialization, convergence criteria, and distance calculations",
        points: 30,
        earnedPoints: 25,
      },
      {
        id: "mc1_r2",
        criteria: "Data Preprocessing & Exploration",
        description:
          "Thorough data analysis, preprocessing steps, and feature scaling/normalization",
        points: 20,
        earnedPoints: 18,
      },
      {
        id: "mc1_r3",
        criteria: "Optimal Cluster Selection",
        description:
          "Implementation of elbow method, silhouette analysis, and justified cluster number selection",
        points: 25,
        earnedPoints: 20,
      },
      {
        id: "mc1_r4",
        criteria: "Clustering Analysis & Interpretation",
        description:
          "Clear analysis of customer segments with business insights and actionable recommendations",
        points: 15,
        earnedPoints: 10,
      },
      {
        id: "mc1_r5",
        criteria: "Code Quality & Documentation",
        description:
          "Well-documented code, proper function structure, and clear methodology explanation",
        points: 10,
        earnedPoints: 5,
      },
    ],
    checkpoints: [
      {
        id: "cp_1",
        title: "Data Visualization",
        description:
          "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
        status: "graded",
        dueDate: "16 Jul 2025",
        grade: "A",
        instructorFeedback: "Lorem Ipsum",
      },
      {
        id: "cp_2",
        title: "Data Visualization",
        description:
          "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
        status: "not_started",
        dueDate: "16 Jul 2025",
      },
    ],
  },

  "mall-customers-part-2": {
    id: "mall-customers-part-2",
    title: "Mall Customers - Part 2: Cluster Labeling & Advanced Visualization",
    description:
      "Building on Part 1, create meaningful business labels for customer clusters and develop an interactive visualization dashboard. Focus on translating technical clustering results into actionable business insights through customer personas and strategic recommendations.",
    subject: "Machine Learning",
    instructor: "Dr. A. Sharma",
    dueDate: "2025-01-25T23:59:00Z",
    createdDate: "2025-01-15T00:00:00Z",
    status: "in_progress",
    progress: 45,
    totalPoints: 100,
    difficulty: "Intermediate",
    estimatedHours: 18,
    totalStudents: 50,
    tags: [
      "Data Visualization",
      "Customer Personas",
      "Business Analysis",
      "Plotly",
      "Dashboard",
      "Market Segmentation",
    ],
    objectives: [
      "Analyze cluster characteristics from Part 1 to understand customer segment patterns",
      "Create meaningful business names and labels for each customer cluster",
      "Develop detailed customer personas with demographic and behavioral profiles",
      "Build interactive visualizations using Plotly or similar advanced visualization libraries",
      "Design a comprehensive dashboard showing cluster distributions and key metrics",
      "Generate actionable business recommendations for each customer segment",
      "Create executive summary with strategic insights for mall management",
    ],
    requirements: [
      "Completed Part 1 (K-Means Clustering) with clustering results",
      "Advanced Python visualization libraries (Plotly, Bokeh, or Dash)",
      "Understanding of customer segmentation business concepts",
      "Experience with interactive dashboard development",
      "Knowledge of customer persona development frameworks",
      "Business analysis and strategic thinking skills",
      "Presentation and communication skills for executive reporting",
    ],
    datasets: [
      {
        id: "mall_customers_clustered",
        name: "Clustered Mall Customers Data",
        description:
          "Enhanced mall customers dataset with cluster assignments from Part 1, including cluster IDs and additional derived features for analysis.",
        format: "CSV",
        size: "12.8 KB",
        downloadUrl: "/datasets/mall_customers_with_clusters.csv",
      },
      {
        id: "customer_persona_template",
        name: "Customer Persona Template",
        description:
          "Professional template for creating detailed customer personas including demographics, behaviors, preferences, and strategic recommendations.",
        format: "DOCX",
        size: "1.2 MB",
        downloadUrl: "/templates/customer_persona_template.docx",
      },
      {
        id: "visualization_examples",
        name: "Advanced Visualization Examples",
        description:
          "Sample Jupyter notebook with Plotly examples for creating interactive charts, 3D plots, and dashboard components for customer analytics.",
        format: "IPYNB",
        size: "2.4 MB",
        downloadUrl: "/examples/advanced_visualization_examples.ipynb",
      },
      {
        id: "business_case_studies",
        name: "Retail Customer Segmentation Case Studies",
        description:
          "Collection of real-world case studies showing how major retailers use customer segmentation for marketing strategy and business growth.",
        format: "PDF",
        size: "5.6 MB",
        downloadUrl: "/resources/retail_segmentation_case_studies.pdf",
      },
    ],
    submissions: [
      {
        id: "sub_mc2_1",
        studentId: "stud_2",
        submittedAt: "2025-01-18T16:45:00Z",
        files: [
          "customer_personas_draft.ipynb",
          "interactive_dashboard.html",
          "business_insights.pdf",
        ],
        status: "graded",
        grade: 82,
        feedback:
          "Excellent customer persona development with clear business names for clusters. Your interactive dashboard is well-designed and informative. Consider adding more specific marketing recommendations and competitor analysis to strengthen business insights.",
      },
    ],
    rubric: [
      {
        id: "mc2_r1",
        criteria: "Cluster Analysis & Interpretation",
        description:
          "Thorough analysis of cluster characteristics with statistical summaries and pattern identification",
        points: 25,
        earnedPoints: 22,
      },
      {
        id: "mc2_r2",
        criteria: "Customer Persona Development",
        description:
          "Creative and meaningful cluster labels with detailed customer personas including demographics and behaviors",
        points: 25,
        earnedPoints: 21,
      },
      {
        id: "mc2_r3",
        criteria: "Interactive Visualization & Dashboard",
        description:
          "Professional interactive dashboard with clear, insightful visualizations and user-friendly interface",
        points: 30,
        earnedPoints: 24,
      },
      {
        id: "mc2_r4",
        criteria: "Business Insights & Recommendations",
        description:
          "Actionable business recommendations with strategic insights and marketing implications",
        points: 20,
        earnedPoints: 15,
      },
    ],
    checkpoints: [
      {
        id: "cp_1",
        title: "Data Visualization",
        description:
          "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
        status: "graded",
        dueDate: "16 Jul 2025",
        grade: "A",
        instructorFeedback: "Lorem Ipsum",
      },
      {
        id: "cp_2",
        title: "Data Visualization",
        description:
          "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
        status: "not_started",
        dueDate: "16 Jul 2025",
      },
    ],
  },

  "mall-customers-part-3": {
    id: "mall-customers-part-3",
    title: "Mall Customers - Part 3: kNN Prediction System",
    description:
      "Implement a complete k-nearest neighbors prediction system using the labeled customer clusters from Parts 1-2. Build a production-ready model that can classify new customers into appropriate segments, complete with performance evaluation and deployment pipeline.",
    subject: "Machine Learning",
    instructor: "Dr. A. Sharma",
    dueDate: "2025-01-30T23:59:00Z",
    createdDate: "2025-01-20T00:00:00Z",
    status: "not_started",
    progress: 0,
    totalPoints: 100,
    difficulty: "Advanced",
    estimatedHours: 25,
    totalStudents: 32,
    tags: [
      "kNN",
      "Supervised Learning",
      "Model Evaluation",
      "Cross-Validation",
      "Deployment",
      "Production ML",
    ],
    objectives: [
      "Implement k-nearest neighbors algorithm from scratch with multiple distance metrics",
      "Use labeled customer clusters from Parts 1-2 as training data for supervised learning",
      "Optimize hyperparameter k using cross-validation and grid search techniques",
      "Evaluate model performance using comprehensive metrics (accuracy, precision, recall, F1-score)",
      "Build a complete prediction pipeline for classifying new customers",
      "Develop a user-friendly interface for real-time customer classification",
      "Create model documentation and deployment guidelines for production use",
      "Compare kNN performance with other classification algorithms (optional bonus)",
    ],
    requirements: [
      "Completed Parts 1 & 2 with labeled customer cluster dataset",
      "Advanced Python programming with object-oriented design principles",
      "Understanding of supervised learning and classification algorithms",
      "Experience with model evaluation techniques and cross-validation",
      "Knowledge of hyperparameter optimization methods",
      "Familiarity with Flask/Streamlit for building prediction interfaces",
      "Understanding of production ML pipelines and model deployment",
      "Statistical knowledge for interpreting model performance metrics",
    ],
    datasets: [
      {
        id: "labeled_customers_training",
        name: "Labeled Customer Training Dataset",
        description:
          "Complete training dataset with customer features and their assigned cluster labels from Parts 1-2, formatted for supervised learning with 80/20 train/test split.",
        format: "CSV",
        size: "15.6 KB",
        downloadUrl: "/datasets/labeled_customers_training.csv",
      },
      {
        id: "new_customers_test",
        name: "New Customers Test Dataset",
        description:
          "Fresh customer data for testing your kNN model, containing 50 new customer profiles without cluster labels for prediction validation.",
        format: "CSV",
        size: "3.2 KB",
        downloadUrl: "/datasets/new_customers_test.csv",
      },
      {
        id: "knn_implementation_template",
        name: "kNN Implementation Template",
        description:
          "Structured Python template with class definitions, method stubs, and helper functions to guide your kNN implementation.",
        format: "PY",
        size: "8.4 KB",
        downloadUrl: "/templates/knn_implementation_template.py",
      },
      {
        id: "model_evaluation_toolkit",
        name: "Model Evaluation Toolkit",
        description:
          "Comprehensive Python toolkit with functions for cross-validation, performance metrics, confusion matrices, and model comparison utilities.",
        format: "PY",
        size: "12.7 KB",
        downloadUrl: "/utils/model_evaluation_toolkit.py",
      },
      {
        id: "deployment_guide",
        name: "ML Model Deployment Guide",
        description:
          "Step-by-step guide for deploying machine learning models in production, including Flask API development and Docker containerization.",
        format: "PDF",
        size: "4.8 MB",
        downloadUrl: "/guides/ml_model_deployment_guide.pdf",
      },
    ],
    submissions: [],
    rubric: [
      {
        id: "mc3_r1",
        criteria: "kNN Algorithm Implementation",
        description:
          "Complete from-scratch kNN implementation with multiple distance metrics and efficient nearest neighbor search",
        points: 30,
      },
      {
        id: "mc3_r2",
        criteria: "Model Optimization & Validation",
        description:
          "Proper hyperparameter tuning using cross-validation with justified k selection and robust evaluation methodology",
        points: 25,
      },
      {
        id: "mc3_r3",
        criteria: "Performance Evaluation & Analysis",
        description:
          "Comprehensive model evaluation with multiple metrics, confusion matrices, and statistical significance testing",
        points: 25,
      },
      {
        id: "mc3_r4",
        criteria: "Prediction Pipeline & Interface",
        description:
          "Complete end-to-end prediction system with user interface and real-time classification capabilities",
        points: 15,
      },
      {
        id: "mc3_r5",
        criteria: "Code Quality & Production Readiness",
        description:
          "Professional code structure, documentation, error handling, and deployment-ready implementation",
        points: 5,
      },
    ],
    checkpoints: [
      {
        id: "cp_1",
        title: "Data Visualization",
        description:
          "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
        status: "graded",
        dueDate: "16 Jul 2025",
        grade: "A",
        instructorFeedback: "Lorem Ipsum",
      },
      {
        id: "cp_2",
        title: "Data Visualization",
        description:
          "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
        status: "not_started",
        dueDate: "16 Jul 2025",
        grade: "A",
        instructorFeedback: "Lorem Ipsum",
      },
    ],
  },
};

// Default project for non-Mall Customers projects
const defaultProject: Project = {
  id: "proj_1",
  title: "Housing Price Prediction",
  description:
    "In this comprehensive project, you will build and evaluate machine learning models to predict housing prices using the california housing dataset. You will apply data preprocessing techniques, feature engineering, and various regression algorithms to create an accurate prediction model.",
  subject: "Data Science 101",
  instructor: "Dr. A. Sharma",
  dueDate: "2025-01-15T23:59:00Z",
  createdDate: "2025-01-01T00:00:00Z",
  status: "in_progress",
  progress: 65,
  totalPoints: 100,
  difficulty: "Intermediate",
  estimatedHours: 15,
  totalStudents: 57,
  tags: [
    "Machine Learning",
    "Regression",
    "Data Preprocessing",
    "Feature Engineering",
  ],
  objectives: [
    "Understand and apply data preprocessing techniques for real-world datasets",
    "Implement feature engineering strategies to improve model performance",
    "Build and compare multiple regression models (Linear, Ridge, Lasso)",
    "Evaluate model performance using appropriate metrics",
    "Create compelling visualizations to communicate findings",
    "Deploy your model using AWS SageMaker (optional)",
  ],
  requirements: [
    "Python programming proficiency",
    "Basic understanding of statistics and linear algebra",
    "Familiarity with pandas, numpy, and scikit-learn",
    "Jupyter Notebook environment",
    "Matplotlib/Seaborn for visualizations",
  ],
  datasets: [
    {
      id: "ds_1",
      name: "California Housing Dataset",
      description:
        "Housing data from the 1990 California census with 20,640 observations and 10 features including median income, house age, and location coordinates.",
      format: "CSV",
      size: "2.4 MB",
      downloadUrl: "/datasets/california_housing.csv",
    },
    {
      id: "ds_2",
      name: "Housing Features Dictionary",
      description:
        "Detailed description of all features in the housing dataset including data types, ranges, and definitions.",
      format: "PDF",
      size: "156 KB",
      downloadUrl: "/datasets/housing_data_dictionary.pdf",
    },
  ],
  submissions: [
    {
      id: "sub_1",
      studentId: "2023001",
      submittedAt: "2025-01-05T14:30:00Z",
      files: ["housing_analysis_v1.ipynb", "preprocessing_utils.py"],
      status: "submitted",
      grade: 75,
      feedback:
        "Good initial analysis. Consider improving feature engineering and model validation techniques.",
    },
    {
      id: "sub_2",
      studentId: "2023002",
      submittedAt: "2025-01-05T14:30:00Z",
      files: ["housing_analysis_v1.ipynb", "preprocessing_utils.py"],
      status: "graded",
      grade: 75,
      feedback:
        "Good initial analysis. Consider improving feature engineering and model validation techniques.",
    },
    {
      id: "sub_3",
      studentId: "2023003",
      submittedAt: "2025-01-05T14:30:00Z",
      files: ["housing_analysis_v1.ipynb", "preprocessing_utils.py"],
      status: "submitted",
      grade: 75,
      feedback:
        "Good initial analysis. Consider improving feature engineering and model validation techniques.",
    },
    {
      id: "sub_4",
      studentId: "2023004",
      submittedAt: "2025-01-05T14:30:00Z",
      files: ["housing_analysis_v1.ipynb", "preprocessing_utils.py"],
      status: "graded",
      grade: 75,
      feedback:
        "Good initial analysis. Consider improving feature engineering and model validation techniques.",
    },
  ],
  rubric: [
    {
      id: "r_1",
      criteria: "Data Exploration & Visualization",
      description:
        "Comprehensive exploration of the dataset with meaningful visualizations",
      points: 20,
      earnedPoints: 18,
    },
    {
      id: "r_2",
      criteria: "Data Preprocessing",
      description:
        "Proper handling of missing values, outliers, and data transformation",
      points: 20,
      earnedPoints: 15,
    },
    {
      id: "r_3",
      criteria: "Feature Engineering",
      description: "Creation of meaningful features and feature selection",
      points: 25,
    },
    {
      id: "r_4",
      criteria: "Model Implementation",
      description:
        "Implementation of multiple regression models with proper validation",
      points: 25,
    },
    {
      id: "r_5",
      criteria: "Analysis & Interpretation",
      description: "Clear interpretation of results and model performance",
      points: 10,
    },
  ],
  checkpoints: [
    {
      id: "cp_1",
      title: "Data Visualization1",
      description:
        "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
      status: "graded",
      dueDate: "12 jun - 16 Jul 2025",
      grade: "A",
      instructorFeedback: "Lorem Ipsum",
    },
    {
      id: "cp_2",
      title: "Data Visualization2",
      description:
        "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
      status: "graded",
      dueDate: "12 jun - 16 Jul 2025",
      grade: "A",
      instructorFeedback: "Lorem Ipsum",
    },
    {
      id: "cp_3",
      title: "Data Visualization3",
      description:
        "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
      status: "not_started",
      dueDate: "12 jun - 16 Jul 2025",
    },
  ],
};
interface SubmissionData {
  studentDetails: studentDetails[];
  status: string;
  files: files[];
  grading: grading[];
  submissionTime: string;
  comments: string;
  feedback: string;
  tag: string;
}

interface studentDetails {
  name: string;
  id: string;
  email: string;
}

interface files {
  name: string;
  size: string;
}

interface grading {
  topic: string;
  earnedPoints: number;
  totalPoints: number;
}

const submissionData = [
  {
    studentDetails: [
      {
        name: "Rahul Sharma",
        id: "2023001",
        email: "<EMAIL>",
      },
    ],
    status: "submitted",
    files: [
      { name: "Linear regression analysis.ipynb", size: "2.3 MB" },
      { name: "analysis_report.pdf", size: "11 MB" },
    ],
    grading: [
      {
        topic: "Data Preprocessing",
        earnedPoints: 18,
        totalPoints: 20,
      },
      {
        topic: "Model Implementation",
        earnedPoints: 28,
        totalPoints: 30,
      },
      {
        topic: "Evaluation & Analysis",
        earnedPoints: 28,
        totalPoints: 30,
      },
      {
        topic: "Code Quality",
        earnedPoints: 14,
        totalPoints: 15,
      },
      {
        topic: "Report Writing",
        earnedPoints: 9,
        totalPoints: 10,
      },
    ],
    submissionTime: "1/07/2025 3:50:00 PM",
    comments:
      "Implemented all required algorithms and included extra analysis and feature importance",
    feedback:
      "Excellent work!! Great analysis and clear explanations. Minor suggestion: could explore more feature engineering techniques.",
    tag: "Data Visualization",
  },
  {
    studentDetails: [
      {
        name: "Amit Kumar",
        id: "2023002",
        email: "<EMAIL>",
      },
    ],
    status: "late",
    files: [
      { name: "Linear regression analysis.ipynb", size: "2.3 MB" },
      { name: "analysis_report.pdf", size: "11 MB" },
    ],
    grading: [
      {
        topic: "Data Preprocessing",
        earnedPoints: 18,
        totalPoints: 20,
      },
      {
        topic: "Model Implementation",
        earnedPoints: 28,
        totalPoints: 30,
      },
      {
        topic: "Evaluation & Analysis",
        earnedPoints: 28,
        totalPoints: 30,
      },
      {
        topic: "Code Quality",
        earnedPoints: 14,
        totalPoints: 15,
      },
      {
        topic: "Report Writing",
        earnedPoints: 9,
        totalPoints: 10,
      },
    ],
    submissionTime: "1/07/2025 3:50:00 PM",
    feedback:
      "Excellent work!! Great analysis and clear explanations. Minor suggestion: could explore more feature engineering techniques.",
    comments:
      "Implemented all required algorithms and included extra analysis and feature importance",
    tag: "Data Visualization",
  },
  {
    studentDetails: [
      {
        name: "Sneha Singh",
        id: "2023003",
        email: "<EMAIL>",
      },
    ],
    status: "submitted",
    files: [
      { name: "Linear regression analysis.ipynb", size: "2.3 MB" },
      { name: "analysis_report.pdf", size: "11 MB" },
    ],
    grading: [
      {
        topic: "Data Preprocessing",
        earnedPoints: 18,
        totalPoints: 20,
      },
      {
        topic: "Model Implementation",
        earnedPoints: 28,
        totalPoints: 30,
      },
      {
        topic: "Evaluation & Analysis",
        earnedPoints: 28,
        totalPoints: 30,
      },
      {
        topic: "Code Quality",
        earnedPoints: 14,
        totalPoints: 15,
      },
      {
        topic: "Report Writing",
        earnedPoints: 9,
        totalPoints: 10,
      },
    ],
    submissionTime: "1/07/2025 3:50:00 PM",
    comments:
      "Implemented all required algorithms and included extra analysis and feature importance",
    feedback:
      "Excellent work!! Great analysis and clear explanations. Minor suggestion: could explore more feature engineering techniques.",
    tag: "Data Visualization",
  },
  {
    studentDetails: [
      {
        name: "Priya Patel",
        id: "2023003",
        email: "<EMAIL>",
      },
      {
        name: "Raj Patel",
        id: "2023004",
        email: "<EMAIL>",
      },
    ],
    status: "graded",
    files: [
      { name: "ml project submission.ipynb", size: "3.1 MB" },
      { name: "final.report.pdf", size: "1.8 MB" },
      { name: "presentation.pptx", size: "5.2 MB" },
    ],
    grading: [
      {
        topic: "Data Preprocessing",
        earnedPoints: 18,
        totalPoints: 20,
      },
      {
        topic: "Model Implementation",
        earnedPoints: 28,
        totalPoints: 30,
      },
      {
        topic: "Evaluation & Analysis",
        earnedPoints: 28,
        totalPoints: 30,
      },
      {
        topic: "Code Quality",
        earnedPoints: 14,
        totalPoints: 15,
      },
      {
        topic: "Report Writing",
        earnedPoints: 9,
        totalPoints: 10,
      },
    ],
    submissionTime: "1/07/2025 3:50:00 PM",
    comments:
      "Implemented all required algorithms and included extra analysis and feature importance",
    feedback:
      "Excellent work!! Great analysis and clear explanations. Minor suggestion: could explore more feature engineering techniques.",
    tag: "Data Visualization",
  },
];

type InstructorProjectDetailsProps = {
  projectId?: string;
  isProjectPreview?: boolean;
  setIsProjectPreview?: React.Dispatch<React.SetStateAction<boolean>>;
};

export interface ProjectResponse {
  id: string;
  title: string;
  description: string;
  instructions: string;
  course_id: string;
  created_by: string;
  status: string; // add other statuses if needed
  difficulty_level: "beginner" | "intermediate" | "advanced"; // extend as needed
  estimated_hours: number;
  notebook_template_s3_url: string | null;

  dataset_s3_url: DatasetFile[];
  additional_files_s3_urls: DatasetFile[];

  start_date: string | null;
  due_date: string | null;
  late_submission_allowed: boolean;
  late_penalty_percent: string;
  max_attempts: number;
  auto_grading_enabled: boolean;
  learning_objectives: string | null;
  prerequisites: string | null;
  tags: string[];
  settings: Record<string, any>;
  isScreen: number;
  project_code: string;
  category_id: string;

  instructor_id: string[];
  teaching_ass_id: string[];

  total_points: number;
  project_overview: string | null;
  type: "individual" | "group"; // extend if more types exist
  sandbox_time_duration: number | null;
  late_submission_days_allowed: number;

  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;

  course: ProjectCourse;
  creator: UserBasic;
  rubrics: Rubric[];
  checkpoints: ProjectCheckpoint[];
  template: any; // refine if template structure is known
  assignments: any[]; // refine if structure is known
  assignmentStats: AssignmentStats;

  instructors: UserWithStatus[];
  teachingAssistants: UserWithStatus[];
  students: UserWithStatus[];

  Submission: {
    count: number;
    rows: any[]; // refine if structure is known
  };
}

export interface DatasetFile {
  presignedUrl: string;
  key: string;
  fileName: string;
  fileSize: number;
  contentType: string;
  lastModified: string;
  etag: string;
  metadata: {
    courseid: string;
    uploadtype: string;
    uploadedby: string;
    projectid: string;
    uploadtimestamp: string;
    originalname: string;
  };
}

export interface ProjectCourse {
  id: string;
  name: string;
  code: string;
  term: string;
  description: string;
}

export interface UserBasic {
  id: string;
  name: string;
  email: string;
}

export interface UserWithStatus extends UserBasic {
  status: string;
}

export interface Rubric {
  id: string;
  title: string;
  description: string;
  criteria: Criterion[];
  total_points: string;
  grading_scale: Record<string, any>;
  is_template: boolean;
  template_name: string | null;
}

interface Criterion {
  name: string;
  points: number;
  description: string;
}

interface ProjectCheckpoint {
  id: string;
  title: string;
  description: string;
  checkpoint_number: number;
  due_date: string;
  status: string | any;
  is_required: boolean;
  metadata: Record<string, any>;
}

interface AssignmentStats {
  totalAssignments: number;
  byRole: Record<string, number>;
  roles: string[];
}


export default function InstructorProjectDetailsPage({
  isProjectPreview,
  setIsProjectPreview,
}: InstructorProjectDetailsProps) {
const {projectId}=useParams()

  const { navigateTo, goBack } = useNavigation();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [checkpointsFilter, setCheckpointsFilter] = useState("all");
console.log(projectId)
  const [activeTab, setActiveTab] = useState<
    "overview" | "submissions" | "students"
  >("overview");
  const [selected, setSelected] = useState<SubmissionData | null>(null);
  const [isPublished, setIsPublished] = useState(true);
  console.log("rrrr", isProjectPreview);
const navigate = useNavigate()
  const queryClient = useQueryClient();

  const publishMutation = useMutation({
    mutationFn: (projectId: string) => publishProject(projectId),
    onSuccess: (response) => {
      setIsPublished(true);
      queryClient.invalidateQueries({ queryKey: ["project", projectId] });
      toast.success(response.message || "Project published successfully");
    },
    onError: (error: unknown) => {
  console.error("Publish API Error:", error);
  if (error instanceof AxiosError) {
    toast.error(error.response?.data?.message || "Failed to publish project");
  } else if (error instanceof Error) {
    toast.error(error.message || "Failed to publish project");
  } else {
    toast.error("Failed to publish project");
  }
}
,
  });

  const unpublishMutation = useMutation({
    mutationFn: (projectId: string) => unpublishProject(projectId),
    onSuccess: (response) => {
      setIsPublished(false);
      queryClient.invalidateQueries({ queryKey: ["project", projectId] });
      toast.success(response.message || "Project unpublished successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to unpublish project");
    },
  });

  const handlePublish = () => {
    if (projectId) {
      publishMutation.mutate(projectId);
    }
  };

  const handleUnpublish = () => {
    if (projectId) {
      unpublishMutation.mutate(projectId);
    }
  };

  const { data: apiProject, isLoading: projectLoading, error: projectError } = useQuery({
    queryKey: ["project", projectId],
    queryFn: () => getProjectById(projectId!),
    enabled: !!projectId,
    retry: false,
  });

  const project = (apiProject as ProjectResponse) ||
    (projectId && mallCustomersProjects[projectId]
      ? mallCustomersProjects[projectId]
      : defaultProject);
console.log(apiProject)
  //  const currentCourse = mockCourses.find((c) => c.name === project.subject);

  // const getGraded = () => {
  //   let gradedCount = 0;
  //   if (project.submissions && Array.isArray(project.submissions)) {
  //     project.submissions.forEach((submission) => {
  //       if (submission.status === "graded") {
  //         gradedCount++;
  //       }
  //     });
  //   }
  //   return gradedCount;
  // };

  // const getSubmissions = () => {
  //   let submittedCount = 0;
  //   if (project.submissions && Array.isArray(project.submissions)) {
  //     project.submissions.forEach((submission) => {
  //       submittedCount++;
  //     });
  //   }
  //   return submittedCount;
  // };

  // const submissions = `${getSubmissions()} / ${project.totalStudents || 0}`;
  //const graded = `${getGraded()} / ${getSubmissions()}`;

  const getFileIcon = (type: string) => {
    console.log(type);
    switch (type) {
      case "pdf":
        return <FileText className="h-4 w-4 " />;
        break;
      case "ipynb":
        return <BarChart className="h-4 w-4 text-bits-green" />;
        break;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const handleCheckPoint = (id: string) => {
    console.log(`id = ${id}`);
    setActiveTab("submissions");
    setCheckpointsFilter(id);
    setStatusFilter(id);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Submitted":
        return "text-xs font-medium text-bits-blue-500";
      case "Graded":
        return "text-xs font-medium text-bits-green-success-500";
      case "Late":
        return "text-xs font-medium text-bits-error";
      default:
        return "text-xs font-medium text-muted-foreground";
    }
  };  // const getGraded = () => {
  //   let gradedCount = 0;
  //   project.submissions.forEach((submission) => {
  //     if (submission.status === "graded") {
  //       gradedCount++;
  //     }
  //   });
  //   return gradedCount;
  // };

  // const getSubmissions = () => {
  //   let submittedCount = 0;
  //   project.submissions.forEach((submission) => {
  //     submittedCount++;
  //   });
  //   return submittedCount;
  // };

  // const submissions = `${getSubmissions()} / ${project.totalStudents}`;
  // const graded = `${getGraded()} / ${getSubmissions()}`;

  // const getFileIcon = (type: string) => {
  //   console.log(type);
  //   switch (type) {
  //     case "pdf":
  //       return <FileText className="h-4 w-4 " />;
  //       break;
  //     case "ipynb":
  //       return <BarChart className="h-4 w-4 text-bits-green" />;
  //       break;
  //     default:
  //       return <FileText className="h-4 w-4" />;
  //   }
  // };

  // const handleCheckPoint = (id: string) => {
  //   console.log(`id = ${id}`);
  //   setActiveTab("submissions");
  //   setCheckpointsFilter(id);
  //   setStatusFilter(id);
  // };

  // const getStatusColor = (status: string) => {
  //   switch (status) {
  //     case "Submitted":
  //       return "text-xs font-medium text-bits-blue-500";
  //     case "Graded":
  //       return "text-xs font-medium text-bits-green-success-500";
  //     case "Late":
  //       return "text-xs font-medium text-bits-error";
  //     default:
  //       return "text-xs font-medium text-muted-foreground";
  //   }
  // };

  function getSubmissionStatus(status: string) {
    let count = 0;
    if (project.submissions && Array.isArray(project.submissions)) {
      project.submissions.forEach((submission) => {
        if (
          submission.status?.toLowerCase().trim() === status?.toLowerCase().trim()
        ) {
          count++;
        }
      });
    }
    return count;
  }

  // const selectedCheckpoint = project.checkpoints.find(
  //   (c) => c.id === checkpointsFilter
  // );

  // function getDateDifference(startDate: string, dueDate: string) {
  //   const now = new Date();
  //   const due = new Date(dueDate);
  //   const start = new Date(startDate);
  //   const diffMs = Math.abs(due.getTime() - now.getTime());

  //   const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  //   // const diffHours = Math.floor((diffMs / (1000 * 60 * 60)) % 24);
  //   // const diffMinutes = Math.floor((diffMs / (1000 * 60)) % 60);
  //   // const diffSeconds = Math.floor((diffMs / 1000) % 60);

  //   const monthNames = [
  //     "Jan",
  //     "Feb",
  //     "Mar",
  //     "Apr",
  //     "May",
  //     "Jun",
  //     "Jul",
  //     "Aug",
  //     "Sep",
  //     "Oct",
  //     "Nov",
  //     "Dec",
  //   ];

  //   const dateValue =
  //     start.getDate() +
  //     " " +
  //     monthNames[start.getMonth()] +
  //     " " +
  //     "-" +
  //     " " +
  //     due.getDate() +
  //     " " +
  //     monthNames[due.getMonth()] +
  //     " " +
  //     due.getFullYear();

  //   console.log(diffDays);
  //   console.log(dueDate);

  //   // if (diffDays < 0) statusString = "Overdue";
  //   // if (diffDays === 0) statusString = "Due today";
  //   // if (diffDays === 1) statusString = "1 day left";
  //   // if (diffDays > 0) statusString = diffDays + " days left";
  //   return dateValue;
  // }
console.log(apiProject)
  return (
    <div className={`space-y-6 ${isProjectPreview ? "p-0" : "p-6"}`}>
      {/* Header */}
      <div className="flex flex-row gap-3">
        <div className="mt-1">
          <button
            onClick={() => {

          navigate(paths.manageProjects)
              
            }}
            className="p-2 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft className="h-7 w-8 text-gray-700" /> 
          </button>
        </div>

        <div className="flex flex-col  flex-grow">
          <h1 className="text-2xl font-semibold">{project?.title || 'Untitled Project'}</h1>
          <p className="text-muted-foreground mt-1">
            {project?.course?.code || 'No Subject'} • {project?.creator?.name || 'No Instructor'}
            {project?.course?.code || 'No Subject'} • {project?.creator?.name || 'No Instructor'}
          </p>
        </div>

        {/* <div className="flex items-center space-x-3">
          <div
            className={`flex bg-yellow-50 rounded-lg p-1 mr-2 items-center space-x-2 ${getStatusColor(
              project.status
            )}`}
          >
            {getStatusIcon(project.status)}
            <span className="font-medium capitalize">
              {project.status.replace("_", " ")}
            </span>
          </div>
          {project.grade !== undefined && (
            <Badge className="bg-green-600 text-white">
              {project.grade}/{project.totalPoints} pts
            </Badge>
          )}
        </div> */}
        {console.log(apiProject?.status)}
          <div className="flex items-center gap-2">
            {["published", "draft"].includes(apiProject?.status) ? (
  <Button
    variant={apiProject?.status === "publish" ? "outline" : "default"}
    className={
      apiProject?.status === "publish"
        ? ""
        : "bg-bits-blue text-white hover:bg-bits-blue/90"
    }
    onClick={
      apiProject?.status === "draft" ? handlePublish : handleUnpublish
    }
    disabled={publishMutation.isPending || unpublishMutation.isPending}
  >
    {publishMutation.isPending || unpublishMutation.isPending
      ? "Processing..."
      : apiProject?.status === "draft"
      ? "Publish"
      : "Unpublish"}
  </Button>
) : (
  <Button
    variant="outline"
    className="bg-gray-200 text-gray-600 cursor-not-allowed"
    disabled
  >
    Draft
  </Button>
)}


            {/* 3-dot Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>

              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem className="font-medium">
                  <Pencil className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem className="font-medium">
                  <LinkIcon className="mr-2 h-4 w-4" />
                  Copy Project link
                </DropdownMenuItem>
                <DropdownMenuItem className="text-red-600 focus:text-red-600 font-medium">
                  <Trash2 className="mr-2 h-4 w-4 text-red-600" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        
      </div>
      {!isProjectPreview && (
        <div className="p-0 -mt-2 -mx-2">
          <Card className="mx-2 p-1 flex flex-row gap-1 rounded-md bg-gray-200">
            <button
              onClick={() => setActiveTab("overview")}
              className={cn(
                "flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors  text-grey-800",
                activeTab === "overview" ? "bg-white shadow" : ""
              )}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab("submissions")}
              className={cn(
                "flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors text-grey-700 border-grey-300",
                activeTab === "submissions" ? "bg-white shadow" : ""
              )}
            >
              Submissions
            </button>
            <button
              onClick={() => setActiveTab("students")}
              className={cn(
                "flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors text-gray-700",
                activeTab === "students" ? "bg-white shadow" : ""
              )}
            >
              Students
            </button>
          </Card>
        </div>
      )}
      {activeTab === "overview" && (
        <div className="flex gap-6">
          {/* Main Content */}
          <div className="flex col-span-1 flex-col gap-6 justify-right w-11/12">
            <div className="lg:col-span-2 space-y-6">
              {/* Course Information */}
              <Card className="gap-4 ">
                <CardHeader>
                  <div className="flex">
                    <div className="h-6 w-6 ">
                      <FileText className=" h-6 w-6 text-bits-blue" />
                    </div>
                    <CardTitle className="text-lg text-bits-grey font-medium mt-0 ml-4">
                      Course Information
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="ml-1">
                  <p className="text-bits-blue-neutral-900 text-md font-semibold mb-1">
                    {project?.course?.name}
                  </p>
                  <p className="text-bits-grey-600 text-sm mb-1">
                    {project?.course?.description} 
                    {project?.course?.description} 
                  </p>
                </CardContent>
              </Card>
            </div>
            <div className="lg:col-span-2 space-y-6">
              {/* Project Overview */}
              <Card className="gap-4">
                <CardHeader>
                  <div className="flex">
                    <div className="h-6 w-6 ">
                      <Clipboard className=" h-6 w-6 text-bits-blue" />
                    </div>
                    <CardTitle className="text-lg font-medium text-bits-blue-800 mt-0 ml-4">
                      Project Overview
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="ml-1">
                  <p className="text-bits-grey-600 text-md font-normal mb-4">
                    {project.description || 'No description available'}
                    {project.description || 'No description available'}
                  </p>
                  {project.checkpoints.length>0 ?
                    <div className="lg:col-span-2 space-y-6">
                      {/* Checkpoints */}
                      <Card className="gap-4">
                        <CardHeader>
                          <div className="flex items-center">
                            <div className="h-6 w-6 ">
                              <Flag className=" h-6 w-6 text-indigo-500" />
                            </div>
                            <CardTitle className="text-lg text-bits-blue-800 font-medium mt-0 ml-4">
                              Checkpoints
                            </CardTitle>
                          </div>

                          {(project.checkpoints || []).map((checkpoint) => (
                            <Card className="gap-4 mb-6">
                              <CardHeader>
                                <div className="flex">
                                  <CardTitle className="text-lg font-medium text-bits-blue-800 mt-0">
                                    {checkpoint.title}
                                  </CardTitle>
                                </div>
                              </CardHeader>
                              <CardContent className="ml-1">
                                <p className="text-bits-grey-600 text-md font-normal mb-4">
                                  {checkpoint.description}
                                </p>
                                <div className="grid grid-cols-3 gap-4 text-sm mb-6">
                                  <div>
                                    <div className="text-2xl font-bold text-bits-blue-neutral-900 mb-2">
                                      {/* {submissions} */}
                                    </div>
                                    <div className="text-bits-grey-600 font-normal text-sm">
                                      Submissions
                                    </div>
                                  </div>
                                  <div>
                                    <div className="text-2xl font-bold text-bits-blue-neutral-900 mb-2 ml-20">
                                      {/* {graded} */}
                                    </div>
                                    <div className="text-bits-grey-600 font-normal text-sm ml-20">
                                      Graded
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center text-sm justify-between">
                                  <div className="flex items-center space-x-1">
                                    <Calendar className="h-4 w-4 text-bits-grey-500" />
                                    <span className="text-bits-grey text-sm font-semibold overflow-hidden text-ellipsis w-20">
                              <span>
  {checkpoint.due_date
    ? format(parseISO(checkpoint.due_date), "dd MMM yyyy")
    : "-"}
</span>

                                    </span>
                                  </div>
                                  {!isProjectPreview && (
                                    <div className="flex items-center justify-between text-sm font-semibold text-primary-foreground">
                                      <Button
                                        className="bg-bits-blue  hover:bg-blue-700"
                                        onClick={() =>
                                          handleCheckPoint(checkpoint.id)
                                        }
                                      >
                                        View Checkpoint Submission
                                      </Button>
                                    </div>
                                  )}
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </CardHeader>
                      </Card>
                    </div>
                    : <></>}
                </CardContent>
              </Card>
            </div>
          </div>
          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Stats */}

            <Card>
              <CardContent className="pt-6">
                <div>
                  <div className="flex items-start mb-2">
                    <BarChart className="h-6 w-6 text-bits-warning mr-2"></BarChart>
                    <div className="text-lg font-medium text-bits-grey">
                      Quick Stats
                    </div>
                  </div>
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex justify-between items-start">
                      <Users className="h-6 w-6 mr-3 text-bits-grey-600" />
                      <p className="text-md text-bits-grey-600">Students</p>
                    </div>

                    <p className="text-md text-bits-grey">
                      {project?.students?.length || 0}
                    </p>
                  </div>

                  <div className="flex justify-between items-start">
                    <div className="flex justify-between items-start">
                      <Award className="h-6 w-6 mr-3 text-bits-grey-600" />
                      <p className="text-md text-bits-grey-600">Avg Grade</p>
                    </div>

                    <p className="text-md text-bits-grey">{`B+`}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* TA & Instructors */}
         {(project?.teachingAssistants?.length > 0 || project?.instructors?.length > 0) && (
  <Card className="gap-3">
    <CardHeader>
      <CardTitle className="flex text-xl items-center font-semibold">
        <Users className="h-6 w-6 mr-3 text-blue-500" />
        TA & Instructors
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-3">
      {project?.teachingAssistants?.map((teacher, index) => (
        <div key={index} className="flex flex-col">
          <p className="font-medium">{teacher.name}</p>
          <p className="text-muted-foreground flex items-center space-x-1">
            <span>TA</span>
            <span className="h-1 w-1 rounded-full bg-muted-foreground" />
            <span>{teacher.email}</span>
          </p>
        </div>
      ))}
      {project?.instructors?.map((teacher, index) => (
        <div key={index} className="flex flex-col">
          <p className="font-medium">{teacher?.name}</p>
          <p className="text-muted-foreground flex items-center space-x-1">
            <span>Instructor</span>
            <span className="h-1 w-1 rounded-full bg-muted-foreground" />
            <span>{teacher?.email}</span>
          </p>
        </div>
      ))}
    </CardContent>
  </Card>
)}

            <Card>
              <CardHeader>
                <CardTitle>
                  <div className="flex">
                    <TrendingUp className="h-6 w-6 mr-3 text-bits-blue" />
                    <span className="text-lg font-medium text-bits-grey">
                      Grade Distribution
                    </span>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-md font-normal text-bits-grey-600">
                      A (90-100%)
                    </span>
                    <div className="flex items-center space-x-2">
                      <Progress value={50} className="w-20 h-2" />
                      <span className="text-xs text-bits-grey-600 w-10">
                        1180
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-md font-normal text-bits-grey-600">
                      B (80-89%)
                    </span>
                    <div className="flex items-center space-x-2">
                      <Progress value={50} className="w-20 h-2" />
                      <span className="text-xs text-bits-grey-600 w-10">
                        +85
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-md font-normal text-bits-grey-600">
                      C (70-79%)
                    </span>
                    <div className="flex items-center space-x-2">
                      <Progress value={50} className="w-20 h-2" />
                      <span className="text-xs text-bits-grey-600 w-10">
                        32
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-md font-normal text-bits-grey-600">
                      D/F (&lt;70%)
                    </span>
                    <div className="flex items-center space-x-2">
                      <Progress value={50} className="w-20 h-2" />
                      <span className="text-xs text-bits-grey-600 w-10">
                        32
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            {/* Grading Rubric */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-xl text-bits-blue font-semibold">
                  <Award className="h-6 w-6 mr-2 text-bits-warning" />
                  <span className="text-lg font-medium text-bits-blue-800">
                    Grading Rubric
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="mt-o">
                <div className="space-y-6">
                  {(project.rubrics || []).map((rubric) => (
                    <div key={rubric.id} className="space-y-3 border-b pb-4 last:border-none">
                      {/* Checkpoint Title */}
                      <div>
                        <h3 className="text-lg font-semibold text-bits-blue-800 text-ellipsis overflow-hidden whitespace-nowrap">
                          {rubric.title}
                        </h3>
                        {rubric.description && (
                          <p className="text-sm text-muted-foreground">{rubric.description}</p>
                        )}
                      </div>

                      <div className="space-y-2 pr-3">
                        {rubric.criteria.map((criterion) => (
                          <div
                            key={criterion.name}
                            className="flex justify-between items-start"
                          >
                            <div className="flex-1 ">
                              <span className="block text-md font-medium text-bits-blue-800 text-ellipsis overflow-hidden whitespace-nowrap max-w-[60%]">
                                {criterion.name}
                              </span>
                              <p className="text-sm text-muted-foreground mt-1">
                                {criterion.description}
                              </p>
                            </div>
                            <span className="flex-shrink-0 text-md font-medium text-bits-blue-italic italic">
                              ({criterion.points} points)
                            </span>
                          </div>
                        ))}
                      </div>

                      <div className="text-right text-lg font-semibold text-bits-blue-800">
                        Total: {rubric.total_points} points
                      </div>
                    </div>
                  ))}
                </div>

              </CardContent>
            </Card>
          </div>
        </div>
      )}
      {activeTab === "submissions" && (
        <div>
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold text-bits-blue-neutral-900">
                      {project.students.length || 0}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Total Students
                    </p>
                  </div>
                  <Users className="h-6 w-6 text-bits-blue" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold text-bits-blue-neutral-900">
                      {getSubmissionStatus("submitted")}
                    </div>
                    <p className="text-sm text-muted-foreground">Submitted</p>
                  </div>
                  <Check className="h-6 w-6 text-bits-blue" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold text-bits-blue-neutral-900">
                      {getSubmissionStatus("graded")}
                    </div>
                    <p className="text-sm text-muted-foreground">Graded</p>
                  </div>
                  <Award className="h-6 w-6 text-bits-blue" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold text-bits-blue-neutral-900">
                      {getSubmissionStatus("late")}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Yet to Grade
                    </p>
                  </div>
                  <Clock className="h-6 w-6 text-bits-blue" />
                </div>
              </CardContent>
            </Card>
            <div className="flex col-span-4 flex-col gap-6 justify-right">
              <div className="flex flex-row gap-6">
                <div className="flex-1">
                  <Card>
                    {/* Filters */}

                    <CardContent className="p-4 gap-4">
                      <div className="flex flex-col md:flex-row gap-2">
                        <div>
                          <span className="text-lg font-medium text-bits-grey">
                            Student Submissions
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                            <Input
                              placeholder="Search Students..."
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="pl-10 w-50"
                            />
                          </div>
                        </div>

                        <Select
                          value={statusFilter}
                          onValueChange={setStatusFilter}
                        >
                          <SelectTrigger className="w-40">
                            <SelectValue placeholder="All Status" />
                          </SelectTrigger>
                          <SelectContent>
                            {/* <SelectItem value="all">All Status</SelectItem> */}
                            {(project.checkpoints || []).map((checkpoint) => (
                              <SelectItem
                                key={checkpoint.id}
                                value={checkpoint.id}
                              >
                                {checkpoint.status}
                              </SelectItem>
                            ))}
                          </SelectContent>
                          {/* <SelectContent>
                            <SelectItem value="all">All Status</SelectItem>
                            <SelectItem value="graded">Graded</SelectItem>
                            <SelectItem value="late">late</SelectItem>
                            <SelectItem value="submitted">Submitted</SelectItem>
                          </SelectContent> */}
                        </Select>
                        <Select
                          value={checkpointsFilter}
                          onValueChange={setCheckpointsFilter}
                        >
                          <SelectTrigger className="w-40">
                            <SelectValue placeholder="All Checkpoints" />
                          </SelectTrigger>
                          <SelectContent>
                            {/* <SelectItem value="all">All Checkpoints</SelectItem> */}
                            {(project.checkpoints || []).map((checkpoint) => (
                              <SelectItem
                                key={checkpoint.id}
                                value={checkpoint.id}
                              >
                                {checkpoint.title}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>

                    <CardContent>
                      <div className="space-y-4">
                        {/* console.log(`statusFilter = ${statusFilter}`) */}
                        {
                          submissionData.map((submission, index) => (
                            // submission.status ===
                            //   selectedCheckpoint?.status && (submission.tag === selectedCheckpoint.title) && (
                            <div
                              key={index}
                              onClick={() => setSelected(submission)}
                              className="flex flex-col align-top p-4 border rounded-lg space-y-3"
                            >
                              <div className="flex flex-col align-top">
                                <div className="flex flex-row">
                                  <div className="ml-2">
                                    <div>
                                      <div className="w-10 h-10 bg-bits-blue rounded-full flex items-center justify-center">
                                        <User className="h-5 w-5 text-white" />
                                      </div>
                                      <p className="font-semibold text-xl text-bits-blue-neutral-900">
                                        {submission.studentDetails
                                          .map((student) => student.name)
                                          .join(", ")}
                                      </p>
                                    </div>

                                    <div className="flex flex-col flex-grow">
                                      <p className="text-muted-foreground mt-1">
                                        {submission.studentDetails
                                          .map((student) => student.id)
                                          .join(", ")}{" "}
                                        •{" "}
                                        {submission.studentDetails
                                          .map((student) => student.email)
                                          .join(", ")}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex ml-auto">
                                    <Badge
                                      className={getStatusColor(
                                        submission.status
                                      )}
                                    >
                                      {submission.status}
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                              <div className="flex flex-row items-center justify-between">
                                <div>
                                  <div className="flex flex-row">
                                    <Clock className="h-4 w-4 text-bits-grey-600 mr-2" />
                                    <span className="text-bits-grey-600 text-sm font-normal">
                                      {submission.submissionTime}
                                    </span>
                                    <div className="flex flex-row">
                                      <Flag className="ml-2 h-4 w-4 text-muted-foreground" />

                                      <div className="text-xs font-medium ml-1 text-bits-grey">
                                        {submission.tag}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center justify-between text-sm font-semibold text-primary-foreground">
                                  <Button
                                    className="bg-bits-blue  hover:bg-blue-700"
                                    onClick={() => navigateTo("")}
                                  >
                                    <Award className="h-4 w-4 mr-2" />
                                    <span className="text-sm font-semibold">
                                      Grade
                                    </span>
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ))
                          // )
                        }
                      </div>
                    </CardContent>
                  </Card>
                </div>
                <div className="">
                  <Card>
                    {!selected ? (
                      <div className="flex flex-col items-center gap-4 p-10">
                        <FileText className="h-6 w-6 text-bits-grey-600" />
                        <span className="text-md font-normal text-bits-grey-600">
                          Select a submission to view details
                        </span>
                      </div>
                    ) : (
                      <div className="flex-1">
                        <CardContent className="p-4 gap-4">
                          {/* {selected && ( */}
                          <div className="flex flex-col gap-3">
                            <div className="text-lg font-medium text-bits-grey">
                              Submissions Details
                            </div>
                            <div className="flex flex-col justify-start">
                              <div className="text-md font-medium p-1">
                                Name
                              </div>
                              {selected.studentDetails.map((item) => (
                                <div>
                                  <div className="flex flex-row mb-2">
                                    <User className="h-5 w-5 text-bits-grey-600 mr-2" />
                                    <div className="text-sm font-normal text-bits-grey-600">
                                      {item.name}
                                    </div>
                                  </div>
                                </div>
                              ))}

                              <div className="text-md font-medium p-1">
                                Submitted Date & Time
                              </div>
                              <div className="flex flex-row">
                                <div className="flex flex-row mb-2">
                                  <Clock className="h-5 w-5 text-bits-grey-600 mr-2" />
                                  <div className="text-sm font-normal text-bits-grey-600">
                                    {selected.submissionTime}
                                  </div>
                                </div>
                              </div>
                              <div className="text-lg font-medium text-bits-grey">
                                Submitted Files
                              </div>
                              {selected.files.map((file) => (
                                <Card className="mb-3">
                                  <CardContent className="p-4 gap-4">
                                    <div className="flex flex-row items-center gap-2 w-full">
                                      {getFileIcon(
                                        file.name.substring(
                                          file.name.indexOf(".") + 1,
                                          file.name.length
                                        )
                                      )}
                                      <div className="flex flex-col text-md font-medium">
                                        {file.name}
                                        <span className="text-xs font-normal text-bits-grey-600">
                                          {file.size}
                                        </span>
                                      </div>
                                      <div className="ml-auto flex items-center">
                                        <Download className="h-5 w-5 text-bits-grey-600 mr-2" />
                                      </div>
                                    </div>
                                  </CardContent>
                                </Card>
                              ))}

                              <div className="text-lg font-medium text-bits-grey">
                                Repository
                              </div>
                              <Button
                                variant="outline"
                                onClick={() => navigateTo("")}
                              >
                                <Eye className="h-4 w-4 text-bits-grey-700" />
                                <div className="text-md font-semibold text-bits-grey-700">
                                  View Repository
                                </div>
                              </Button>
                              <div className="text-lg font-medium text-bits-grey mt-2">
                                Student Comments
                              </div>
                              <div className="bg-bits-blue-25 w-full text-sm font-normal text-bits-grey-600 p-4">
                                {selected.comments}
                              </div>
                            </div>
                            <Button
                              className="bg-bits-blue  hover:bg-blue-700"
                              onClick={() => navigateTo("")}
                            >
                              <Award className="h-4 w-4 mr-2 text-bits-white" />
                              <div className="text-md font-semibold text-bits-white">
                                Grade
                              </div>
                            </Button>
                            <Button
                              variant="outline"
                              onClick={() => navigateTo("view-all-project")}
                            >
                              <MessageSquare className="h-4 w-4 mr-2 text-bits-grey-700" />
                              <div className="text-md font-semibold text-bits-grey-700">
                                Message Student
                              </div>
                            </Button>
                            <div className="text-lg font-medium text-bits-grey">
                              Grade Breakdown
                            </div>
                            {selected.grading.map((grade) => (
                              <div className="flex flex-col">
                                <div className="flex flex-row justify-between">
                                  <div className="text-sm font-normal text-bits-grey-600">
                                    {grade.topic}
                                  </div>
                                  <div>
                                    <span className="text-sm font-semibold text-bits-grey">
                                      {grade.earnedPoints}/{grade.totalPoints}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            ))}

                            <div className="text-lg font-medium text-bits-grey">
                              Feedback
                            </div>
                            <div className="bg-bits-blue-25 w-full text-sm font-normal text-bits-grey-600 p-4">
                              {selected.feedback}
                            </div>
                          </div>
                          {/* )} */}
                        </CardContent>
                      </div>
                    )}
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      {activeTab === "students" && (
        <div>
          <div className="flex flex-row ml-auto p-2 w-full">
            <div>
              <span className="text-lg font-medium text-bits-grey">
                Students
              </span>
            </div>
            <div className="flex flex-1 flex-row justify-end items-center mb-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-bits-grey-600" />
                <Input
                  placeholder="Search Students..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-50 text-md font-normal text-bits-grey-600 mr-2"
                />
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={() => navigateTo("")}
              >
                <Download className="h-4 w-4 text-bits-grey" />
                <span className="text-sm font-semibold text-bits-grey">
                  Export
                </span>
              </Button>
            </div>
          </div>
          <Table>
            <TableHeader>
              <TableRow className="mt-6 ml-6 text-sm font-medium text-bits-grey">
                <TableHead>Student</TableHead>
                <TableHead>ID</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {project?.students?.map((student) => (
                <TableRow key={student.id}>
                  <TableCell>
                    <div className="flex flex-col">
                      <span className="text-sm font-medium text-bits-grey">
                        {student.name}
                      </span>
                      <span className="text-sm font-normal text-bits-grey-600">
                        {student.email}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-md font-normal text-bits-grey">
                    {student?.id}
                  </TableCell>
                  <TableCell>
                    <Badge className="bg-bits-completed capitalize text-bits-green text-xs font-medium">
                      {student.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="flex justify-between text-xs font-normal text-bits-grey-600 mb-2">
                        <span>
                          {student?.checkPointSubmitted}/
                          {student?.totalCheckPoints} checkpoint submitted
                        </span>
                      </div>
                      <Progress
                        value={
                          (student.checkPointSubmitted /
                            student.totalCheckPoints) *
                          100
                        }
                        className="h-2"
                      />
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="flex space-x-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => navigateTo("")}
                      >
                        <MessageSquare className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}
