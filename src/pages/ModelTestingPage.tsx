import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { Textarea } from "../components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../components/ui/tabs";
import { Progress } from "../components/ui/progress";
import { Checkbox } from "../components/ui/checkbox";
import { Slider } from "../components/ui/slider";
import { Switch } from "../components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";
import { Alert, AlertDescription, AlertTitle } from "../components/ui/alert";
import { Separator } from "../components/ui/separator";
import {
  Play,
  Square,
  RotateCcw,
  Download,
  Upload,
  Settings,
  Database,
  Target,
  BarChart3,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Info,
  FileText,
  Code,
  Cpu,
  HardDrive,
  Zap,
  ArrowLeft,
  RefreshCw,
  Save,
  Share2,
  Eye,
  GitBranch,
  TestTube,
} from "lucide-react";

interface ModelTestingPageProps {
  studentId?: string;
  studentName?: string;
  assignmentId?: string;
  assignmentTitle?: string;
  submissionId?: string;
  onBack?: () => void;
}

interface TestConfiguration {
  testType: "basic" | "comprehensive" | "custom";
  datasetSize: "sample" | "full" | "custom";
  validationMethod:
    | "holdout"
    | "cross_validation"
    | "time_series"
    | "stratified";
  metrics: string[];
  timeout: number;
  maxMemory: number;
  randomSeed: number;
}

interface TestResults {
  status: "running" | "completed" | "failed" | "cancelled";
  progress: number;
  startTime: string;
  endTime?: string;
  duration?: number;
  metrics: { [key: string]: number };
  predictions?: any[];
  errors?: string[];
  warnings?: string[];
  systemMetrics: {
    memoryUsage: number;
    cpuUsage: number;
    executionTime: number;
  };
}

const defaultTestConfig: TestConfiguration = {
  testType: "basic",
  datasetSize: "sample",
  validationMethod: "holdout",
  metrics: ["accuracy", "precision", "recall", "f1_score"],
  timeout: 300,
  maxMemory: 2048,
  randomSeed: 42,
};

const availableMetrics = [
  { id: "accuracy", name: "Accuracy", category: "Classification" },
  { id: "precision", name: "Precision", category: "Classification" },
  { id: "recall", name: "Recall", category: "Classification" },
  { id: "f1_score", name: "F1 Score", category: "Classification" },
  { id: "auc_roc", name: "AUC-ROC", category: "Classification" },
  {
    id: "confusion_matrix",
    name: "Confusion Matrix",
    category: "Classification",
  },
  { id: "mse", name: "Mean Squared Error", category: "Regression" },
  { id: "mae", name: "Mean Absolute Error", category: "Regression" },
  { id: "rmse", name: "Root Mean Squared Error", category: "Regression" },
  { id: "r2_score", name: "R² Score", category: "Regression" },
  { id: "adjusted_r2", name: "Adjusted R²", category: "Regression" },
  { id: "silhouette_score", name: "Silhouette Score", category: "Clustering" },
  {
    id: "davies_bouldin_score",
    name: "Davies-Bouldin Score",
    category: "Clustering",
  },
];

export default function ModelTestingPage({
  studentId = "2023001",
  studentName = "Rahul Sharma",
  assignmentId = "proj_001",
  assignmentTitle = "Linear Regression Analysis",
  submissionId = "sub_001",
  onBack,
}: ModelTestingPageProps) {
  const [testConfig, setTestConfig] =
    useState<TestConfiguration>(defaultTestConfig);
  const [testResults, setTestResults] = useState<TestResults | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [activeTab, setActiveTab] = useState("configuration");
  const [advancedMode, setAdvancedMode] = useState(false);

  // Mock project information
  const projectInfo = {
    name: "Linear Regression Model",
    type: "Supervised Learning - Regression",
    framework: "scikit-learn",
    modelFile: "linear_regression_model.pkl",
    dataFile: "housing_data.csv",
    features: ["sqft", "bedrooms", "bathrooms", "age"],
    target: "price",
    trainingSamples: 800,
    testingSamples: 200,
    lastTrained: "2025-01-12T10:30:00Z",
  };

  const runModelTest = async () => {
    setIsRunning(true);
    setActiveTab("results");

    // Initialize test results
    const initialResults: TestResults = {
      status: "running",
      progress: 0,
      startTime: new Date().toISOString(),
      metrics: {},
      systemMetrics: {
        memoryUsage: 0,
        cpuUsage: 0,
        executionTime: 0,
      },
    };
    setTestResults(initialResults);

    // Simulate test execution with progress updates
    const progressSteps = [
      { progress: 10, message: "Loading model and data..." },
      { progress: 25, message: "Preprocessing data..." },
      { progress: 40, message: "Running predictions..." },
      { progress: 60, message: "Calculating metrics..." },
      { progress: 80, message: "Generating reports..." },
      { progress: 100, message: "Test completed!" },
    ];

    for (const step of progressSteps) {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setTestResults((prev) =>
        prev
          ? {
              ...prev,
              progress: step.progress,
              systemMetrics: {
                memoryUsage: Math.min(
                  85,
                  prev.systemMetrics.memoryUsage + Math.random() * 15
                ),
                cpuUsage: Math.min(
                  90,
                  prev.systemMetrics.cpuUsage + Math.random() * 20
                ),
                executionTime: prev.systemMetrics.executionTime + 1,
              },
            }
          : prev
      );
    }

    // Final results
    const finalResults: TestResults = {
      status: "completed",
      progress: 100,
      startTime: initialResults.startTime,
      endTime: new Date().toISOString(),
      duration: 6,
      metrics: {
        mse: 1250000000.85,
        mae: 25430.67,
        rmse: 35355.34,
        r2_score: 0.7856,
        adjusted_r2: 0.7821,
        mean_absolute_percentage_error: 8.92,
        explained_variance_score: 0.7863,
      },
      predictions: [
        { actual: 250000, predicted: 248500, error: 1500, sample_id: 1 },
        { actual: 320000, predicted: 315200, error: 4800, sample_id: 2 },
        { actual: 180000, predicted: 182300, error: -2300, sample_id: 3 },
        { actual: 450000, predicted: 442100, error: 7900, sample_id: 4 },
        { actual: 230000, predicted: 227800, error: 2200, sample_id: 5 },
      ],
      warnings: [
        "Model shows slight overfitting on training data",
        "Feature scaling recommended for better performance",
      ],
      systemMetrics: {
        memoryUsage: 78.5,
        cpuUsage: 65.2,
        executionTime: 6.2,
      },
    };

    setTestResults(finalResults);
    setIsRunning(false);
  };

  const stopTest = () => {
    setIsRunning(false);
    setTestResults((prev) =>
      prev
        ? {
            ...prev,
            status: "cancelled",
            endTime: new Date().toISOString(),
          }
        : prev
    );
  };

  const resetTest = () => {
    setTestResults(null);
    setActiveTab("configuration");
  };

  const updateTestConfig = (updates: Partial<TestConfiguration>) => {
    setTestConfig((prev) => ({ ...prev, ...updates }));
  };

  const updateMetrics = (metricId: string, checked: boolean) => {
    const newMetrics = checked
      ? [...testConfig.metrics, metricId]
      : testConfig.metrics.filter((m) => m !== metricId);
    updateTestConfig({ metrics: newMetrics });
  };

  const getMetricsByCategory = (category: string) => {
    return availableMetrics.filter((metric) => metric.category === category);
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "running":
        return "text-bits-blue";
      case "completed":
        return "text-green-600";
      case "failed":
        return "text-red-600";
      case "cancelled":
        return "text-orange-600";
      default:
        return "text-muted-foreground";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "running":
        return <RefreshCw className="h-4 w-4 animate-spin" />;
      case "completed":
        return <CheckCircle className="h-4 w-4" />;
      case "failed":
        return <AlertCircle className="h-4 w-4" />;
      case "cancelled":
        return <Square className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      // Fallback if no onBack prop is provided
      window.history.back();
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Submission
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-bits-blue">Model Testing</h1>
            <p className="text-muted-foreground">
              Testing submission by {studentName} for {assignmentTitle}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-bits-blue border-bits-blue">
            <TestTube className="h-3 w-3 mr-1" />
            Testing Environment
          </Badge>
          {testResults && (
            <Badge
              variant="outline"
              className={`${getStatusColor(testResults.status)} border-current`}
            >
              {getStatusIcon(testResults.status)}
              <span className="ml-1 capitalize">{testResults.status}</span>
            </Badge>
          )}
        </div>
      </div>

      {/* Project Information */}
      <Card className="border-bits-blue">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Info className="h-5 w-5 mr-2 text-bits-blue" />
            Project Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">
                Model Name
              </Label>
              <p className="font-medium">{projectInfo.name}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">
                Type
              </Label>
              <p className="font-medium">{projectInfo.type}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">
                Framework
              </Label>
              <p className="font-medium">{projectInfo.framework}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">
                Last Trained
              </Label>
              <p className="font-medium">
                {new Date(projectInfo.lastTrained).toLocaleDateString()}
              </p>
            </div>
          </div>
          <Separator className="my-4" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">
                Features
              </Label>
              <div className="flex flex-wrap gap-1 mt-1">
                {projectInfo.features.map((feature) => (
                  <Badge key={feature} variant="secondary" className="text-xs">
                    {feature}
                  </Badge>
                ))}
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">
                Target Variable
              </Label>
              <Badge
                variant="outline"
                className="mt-1 text-bits-blue border-bits-blue"
              >
                {projectInfo.target}
              </Badge>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">
                Data Samples
              </Label>
              <p className="font-medium">
                {projectInfo.trainingSamples + projectInfo.testingSamples} total
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Testing Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="configuration">Configuration</TabsTrigger>
          <TabsTrigger value="execution">Execution</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
        </TabsList>

        {/* Configuration Tab */}
        <TabsContent value="configuration" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Mandatory Configuration */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center">
                      <Settings className="h-5 w-5 mr-2" />
                      Test Configuration
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                      <Label htmlFor="advanced-mode" className="text-sm">
                        Advanced Mode
                      </Label>
                      <Switch
                        id="advanced-mode"
                        checked={advancedMode}
                        onCheckedChange={setAdvancedMode}
                      />
                    </div>
                  </div>
                  <CardDescription>
                    Configure mandatory test parameters
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Test Type */}
                  <div className="space-y-2">
                    <Label className="font-medium">Test Type *</Label>
                    <Select
                      value={testConfig.testType}
                      onValueChange={(value: any) =>
                        updateTestConfig({ testType: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="basic">
                          Basic Testing - Essential metrics only
                        </SelectItem>
                        <SelectItem value="comprehensive">
                          Comprehensive Testing - Full evaluation
                        </SelectItem>
                        <SelectItem value="custom">
                          Custom Testing - User-defined metrics
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Dataset Size */}
                  <div className="space-y-2">
                    <Label className="font-medium">Dataset Size *</Label>
                    <Select
                      value={testConfig.datasetSize}
                      onValueChange={(value: any) =>
                        updateTestConfig({ datasetSize: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="sample">
                          Sample (20%) - Quick testing
                        </SelectItem>
                        <SelectItem value="full">
                          Full Dataset - Complete evaluation
                        </SelectItem>
                        <SelectItem value="custom">
                          Custom Size - Specify percentage
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Validation Method */}
                  <div className="space-y-2">
                    <Label className="font-medium">Validation Method *</Label>
                    <Select
                      value={testConfig.validationMethod}
                      onValueChange={(value: any) =>
                        updateTestConfig({ validationMethod: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="holdout">
                          Hold-out Validation (80-20 split)
                        </SelectItem>
                        <SelectItem value="cross_validation">
                          K-Fold Cross Validation
                        </SelectItem>
                        <SelectItem value="time_series">
                          Time Series Split
                        </SelectItem>
                        <SelectItem value="stratified">
                          Stratified Validation
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Advanced Configuration */}
                  {advancedMode && (
                    <>
                      <Separator />
                      <div className="space-y-4">
                        <h4 className="font-medium text-bits-blue">
                          Advanced Settings
                        </h4>

                        {/* Timeout */}
                        <div className="space-y-2">
                          <Label>Timeout (seconds): {testConfig.timeout}</Label>
                          <Slider
                            value={[testConfig.timeout]}
                            onValueChange={([value]) =>
                              updateTestConfig({ timeout: value })
                            }
                            max={1800}
                            min={60}
                            step={30}
                            className="w-full"
                          />
                        </div>

                        {/* Memory Limit */}
                        <div className="space-y-2">
                          <Label>
                            Memory Limit (MB): {testConfig.maxMemory}
                          </Label>
                          <Slider
                            value={[testConfig.maxMemory]}
                            onValueChange={([value]) =>
                              updateTestConfig({ maxMemory: value })
                            }
                            max={8192}
                            min={512}
                            step={256}
                            className="w-full"
                          />
                        </div>

                        {/* Random Seed */}
                        <div className="space-y-2">
                          <Label>Random Seed</Label>
                          <Input
                            type="number"
                            value={testConfig.randomSeed}
                            onChange={(e) =>
                              updateTestConfig({
                                randomSeed: parseInt(e.target.value) || 42,
                              })
                            }
                            placeholder="42"
                          />
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Metrics Selection */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="h-5 w-5 mr-2" />
                    Evaluation Metrics
                  </CardTitle>
                  <CardDescription>Select metrics to evaluate</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {["Classification", "Regression", "Clustering"].map(
                    (category) => {
                      const categoryMetrics = getMetricsByCategory(category);
                      if (categoryMetrics.length === 0) return null;

                      return (
                        <div key={category} className="space-y-2">
                          <Label className="font-medium text-sm">
                            {category}
                          </Label>
                          <div className="space-y-2">
                            {categoryMetrics.map((metric) => (
                              <div
                                key={metric.id}
                                className="flex items-center space-x-2"
                              >
                                <Checkbox
                                  id={metric.id}
                                  checked={testConfig.metrics.includes(
                                    metric.id
                                  )}
                                  onCheckedChange={(checked) =>
                                    updateMetrics(metric.id, checked as boolean)
                                  }
                                />
                                <Label htmlFor={metric.id} className="text-sm">
                                  {metric.name}
                                </Label>
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    }
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Execution Tab */}
        <TabsContent value="execution" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Play className="h-5 w-5 mr-2" />
                Test Execution
              </CardTitle>
              <CardDescription>
                Run model testing with configured parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Control Buttons */}
              <div className="flex items-center space-x-3">
                <Button
                  onClick={runModelTest}
                  disabled={isRunning}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isRunning ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Running Test...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Start Test
                    </>
                  )}
                </Button>

                {isRunning && (
                  <Button onClick={stopTest} variant="destructive">
                    <Square className="h-4 w-4 mr-2" />
                    Stop Test
                  </Button>
                )}

                <Button
                  onClick={resetTest}
                  variant="outline"
                  disabled={isRunning}
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset
                </Button>
              </div>

              {/* Progress and System Metrics */}
              {testResults && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <Label className="text-sm">Progress</Label>
                        </div>
                        <Progress
                          value={testResults.progress}
                          className="mt-2"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          {testResults.progress}% Complete
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center space-x-2">
                          <HardDrive className="h-4 w-4 text-muted-foreground" />
                          <Label className="text-sm">Memory Usage</Label>
                        </div>
                        <Progress
                          value={testResults.systemMetrics.memoryUsage}
                          className="mt-2"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          {testResults.systemMetrics.memoryUsage.toFixed(1)}% of{" "}
                          {testConfig.maxMemory}MB
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center space-x-2">
                          <Cpu className="h-4 w-4 text-muted-foreground" />
                          <Label className="text-sm">CPU Usage</Label>
                        </div>
                        <Progress
                          value={testResults.systemMetrics.cpuUsage}
                          className="mt-2"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          {testResults.systemMetrics.cpuUsage.toFixed(1)}%
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Execution Info */}
                  <Card>
                    <CardContent className="pt-6">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <Label className="text-muted-foreground">
                            Status
                          </Label>
                          <div
                            className={`flex items-center space-x-1 ${getStatusColor(
                              testResults.status
                            )}`}
                          >
                            {getStatusIcon(testResults.status)}
                            <span className="capitalize font-medium">
                              {testResults.status}
                            </span>
                          </div>
                        </div>
                        <div>
                          <Label className="text-muted-foreground">
                            Started
                          </Label>
                          <p className="font-medium">
                            {new Date(
                              testResults.startTime
                            ).toLocaleTimeString()}
                          </p>
                        </div>
                        <div>
                          <Label className="text-muted-foreground">
                            Duration
                          </Label>
                          <p className="font-medium">
                            {testResults.duration
                              ? formatDuration(testResults.duration)
                              : "Running..."}
                          </p>
                        </div>
                        <div>
                          <Label className="text-muted-foreground">
                            Test Type
                          </Label>
                          <p className="font-medium capitalize">
                            {testConfig.testType}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          {testResults ? (
            <>
              {/* Results Summary */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center">
                      <BarChart3 className="h-5 w-5 mr-2" />
                      Test Results
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Export
                      </Button>
                      <Button variant="outline" size="sm">
                        <Share2 className="h-4 w-4 mr-2" />
                        Share
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {testResults.status === "completed" && (
                    <div className="space-y-6">
                      {/* Metrics Grid */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {Object.entries(testResults.metrics).map(
                          ([metric, value]) => (
                            <Card key={metric}>
                              <CardContent className="pt-6 text-center">
                                <p className="text-sm text-muted-foreground capitalize">
                                  {metric.replace(/_/g, " ")}
                                </p>
                                <p className="text-2xl font-bold text-bits-blue">
                                  {typeof value === "number"
                                    ? value.toFixed(4)
                                    : value}
                                </p>
                              </CardContent>
                            </Card>
                          )
                        )}
                      </div>

                      {/* Predictions Table */}
                      {testResults.predictions && (
                        <div className="space-y-4">
                          <h4 className="font-medium">Sample Predictions</h4>
                          <div className="border rounded-lg overflow-hidden">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Sample</TableHead>
                                  <TableHead>Actual</TableHead>
                                  <TableHead>Predicted</TableHead>
                                  <TableHead>Error</TableHead>
                                  <TableHead>Abs Error</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {testResults.predictions.map((pred) => (
                                  <TableRow key={pred.sample_id}>
                                    <TableCell>#{pred.sample_id}</TableCell>
                                    <TableCell>
                                      ${pred.actual.toLocaleString()}
                                    </TableCell>
                                    <TableCell>
                                      ${pred.predicted.toLocaleString()}
                                    </TableCell>
                                    <TableCell
                                      className={
                                        pred.error > 0
                                          ? "text-red-600"
                                          : "text-green-600"
                                      }
                                    >
                                      {pred.error > 0 ? "+" : ""}$
                                      {pred.error.toLocaleString()}
                                    </TableCell>
                                    <TableCell>
                                      ${Math.abs(pred.error).toLocaleString()}
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        </div>
                      )}

                      {/* Warnings */}
                      {testResults.warnings &&
                        testResults.warnings.length > 0 && (
                          <Alert>
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>Warnings</AlertTitle>
                            <AlertDescription className="space-y-1">
                              {testResults.warnings.map((warning, index) => (
                                <p key={index}>• {warning}</p>
                              ))}
                            </AlertDescription>
                          </Alert>
                        )}
                    </div>
                  )}

                  {testResults.status === "failed" && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Test Failed</AlertTitle>
                      <AlertDescription>
                        {testResults.errors?.join(", ") ||
                          "An unexpected error occurred during testing."}
                      </AlertDescription>
                    </Alert>
                  )}

                  {testResults.status === "cancelled" && (
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Test Cancelled</AlertTitle>
                      <AlertDescription>
                        The test was cancelled by the user.
                      </AlertDescription>
                    </Alert>
                  )}

                  {testResults.status === "running" && (
                    <div className="text-center py-8">
                      <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-bits-blue" />
                      <p className="text-muted-foreground">
                        Test is running...
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <TestTube className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  No test results available. Run a test to see results here.
                </p>
                <Button
                  onClick={() => setActiveTab("execution")}
                  className="mt-4 bg-bits-blue hover:bg-bits-blue/90"
                >
                  Go to Execution
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
