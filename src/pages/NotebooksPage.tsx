import React, { use<PERSON>emo, useState } from "react";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Search, Upload, Download, Copy, MoreHorizontal, FileText, ArrowLeft, Plus, Eye } from "lucide-react";
import { Input } from "../components/ui/input";
import { Badge } from "../components/ui/badge";
import { toast } from "sonner";
import useNavigation from "../components/Context/NavigationContext";
import { useUpdateContentMutation, getContents } from "../api/jupyterManagementApi";
import { useJupyterWorkspace } from "../contexts/JupyterWorkspaceContext";

type NotebookType = "Project File" | "Template" | "User Created";

interface Notebook {
  id: string;
  name: string;
  description?: string;
  author?: string;
  lastModified?: string;
  size?: string;
  version?: string;
  type?: NotebookType;
  tags?: string[];
  // Carry path in practice, even if not declared in the interface
  // path?: string;
}

function getTypeColor(type?: NotebookType) {
  switch (type) {
    case "Project File":
      return "bg-bits-blue/10 text-bits-blue border-bits-blue";
    case "Template":
      return "bg-bits-gold/10 text-bits-gold border-bits-gold";
    case "User Created":
    default:
      return "bg-muted text-muted-foreground border-border";
  }
}

// Helpers to allow only .ipynb files
const getExt = (name?: string) => {
  if (!name) return "";
  const i = name.lastIndexOf(".");
  return i === -1 ? "" : name.slice(i + 1).toLowerCase();
};
const isIpynb = (name?: string) => getExt(name) === "ipynb";

export default function NotebooksPage() {
  const { goBack, pageParams, navigateTo } = useNavigation();
  const initialFromParams: Notebook[] = (pageParams?.files as Notebook[]) ?? [];

  // Seed state with only .ipynb files
  const [notebooks, setNotebooks] = useState<Notebook[]>(
    initialFromParams.filter((n) => isIpynb(n?.name))
  );
  const [searchTerm, setSearchTerm] = useState("");

  const { workspace } = useJupyterWorkspace();
  const updateContentMutation = useUpdateContentMutation();

  const filteredNotebooks = useMemo(() => {
    const q = searchTerm.trim().toLowerCase();
    const base = notebooks.filter((n) => isIpynb(n?.name));
    if (!q) return base;
    return base.filter(
      (n) =>
        n.name.toLowerCase().includes(q) ||
        (n.description ?? "").toLowerCase().includes(q) ||
        (n.tags ?? []).some((t) => t.toLowerCase().includes(q))
    );
  }, [notebooks, searchTerm]);

  const buildTargetPath = (filename: string) => {
    const basePath = workspace?.folderPath;
    return `${basePath}/${filename}`;
  };

  const handleUpload = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".ipynb";
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        const filePath = buildTargetPath(file.name);
        const text = await file.text();

        let parsed: any;
        try {
          parsed = JSON.parse(text);
        } catch {
          toast.error("Invalid notebook file. Could not parse JSON.");
          return;
        }

        const res: any = await updateContentMutation.mutateAsync({
          path: filePath,
          content: parsed,
          type: "notebook",
          format: "json",
        });

        const added: Notebook = {
          id: res.id ?? res.path ?? filePath,
          name: res.name ?? file.name,
          description: res.description ?? "",
          author: res.author ?? "",
          lastModified:
            res.last_modified ?? res.lastModified ?? new Date().toISOString(),
          size:
            typeof res.size === "number" ? `${res.size}` : res.size ?? `${text.length}`,
          version: res.version ?? "",
          type: "User Created",
          tags: res.tags ?? [],
          // @ts-ignore carry path for downstream use
          path: res.path ?? filePath,
        };

        setNotebooks((prev) => {
          // even though upload accept enforces .ipynb, guard with extension check
          if (!isIpynb(added.name)) return prev;
          const exists = prev.some((n) => n.id === added.id || n.name === added.name);
          return exists ? prev : [added, ...prev];
        });

        toast.success(`Uploaded ${added.name}`);
      } catch (err) {
        console.error(err);
        toast.error("Upload failed");
      }
    };
    input.click();
  };

  const handleDownload = async (notebook: Notebook) => {
    try {
      const filePath = (notebook as any).path ?? buildTargetPath(notebook.name);
      const model = await getContents(filePath);

      const json = model?.content ? JSON.stringify(model.content, null, 2) : "";
      if (!json) {
        toast.error("No content available to download.");
        return;
      }

      const blob = new Blob([json], {
        // Many environments recognize application/x-ipynb+json; application/json is also acceptable
        type: "application/x-ipynb+json;charset=utf-8;",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        notebook.name.endsWith(".ipynb") ? notebook.name : `${notebook.name}.ipynb`
      );
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success(`Downloading ${notebook.name}`);
    } catch (err) {
      console.error("Download failed:", err);
      toast.error("Download failed");
    }
  };

  const handleDuplicate = async (notebook: Notebook) => {
    try {
      const resolvePath = (name: string) => {
        const basePath = workspace?.folderPath || "";
        return `${basePath}/${name}`;
      };
      const sourcePath = (notebook as any).path ?? resolvePath(notebook.name);

      const sourceModel = await getContents(sourcePath);
      const sourceJson = sourceModel?.content;
      if (!sourceJson) {
        toast.error("No content available to duplicate.");
        return;
      }

      // Generate unique -copy name
      const existingNames = new Set(notebooks.map((n) => n.name.toLowerCase()));
      const parts = notebook.name.split(".");
      const ext = parts.length > 1 ? `.${parts.pop()}` : ".ipynb";
      const base = parts.join(".");
      const mkName = (idx: number | null) =>
        idx === null ? `${base}-copy${ext}` : `${base}-copy-${idx}${ext}`;

      let candidate = mkName(null);
      let idx = 1;
      while (existingNames.has(candidate.toLowerCase())) {
        candidate = mkName(idx++);
      }

      const targetPath = resolvePath(candidate);

      const res: any = await updateContentMutation.mutateAsync({
        path: targetPath,
        content: sourceJson,
        type: "notebook",
        format: "json",
      });

      const added: Notebook = {
        id: res.id ?? res.path ?? targetPath,
        name: res.name ?? candidate,
        description: res.description ?? "",
        author: res.author ?? "",
        lastModified:
          res.last_modified ?? res.lastModified ?? new Date().toISOString(),
        size:
          typeof res.size === "number" ? `${res.size}` : res.size ?? notebook.size ?? "",
        version: res.version ?? "",
        type: notebook.type ?? "User Created",
        tags: res.tags ?? notebook.tags ?? [],
        // @ts-ignore
        path: res.path ?? targetPath,
      };

      setNotebooks((prev) => {
        if (!isIpynb(added.name)) return prev;
        const exists = prev.some((n) => n.id === added.id || n.name === added.name);
        return exists ? prev : [added, ...prev];
      });

      toast.success(`Duplicated ${notebook.name} → ${added.name}`);
    } catch (err: any) {
      console.error("Duplicate failed:", err);
      toast.error(err?.response?.data?.message || "Duplicate failed");
    }
  };

  const handleCreateNew = async () => {
    try {
      const basePath = workspace?.folderPath || "";
      const existingNames = new Set(notebooks.map((n) => n.name.toLowerCase()));
      const base = "Untitled";
      const ext = ".ipynb";
      const mkName = (i: number) => (i === 0 ? `${base}${ext}` : `${base}-${i}${ext}`);

      let i = 0;
      let filename = mkName(i);
      while (existingNames.has(filename.toLowerCase())) {
        i += 1;
        filename = mkName(i);
      }

      const filePath = `${basePath}/${filename}`;

      // Minimal valid notebook JSON
      const notebookJson = {
        nbformat: 4,
        nbformat_minor: 5,
        metadata: {
          kernelspec: {
            display_name: "Python 3",
            language: "python",
            name: "python3",
          },
          language_info: {
            name: "python",
          },
        },
        cells: [
          {
            cell_type: "code",
            execution_count: null,
            metadata: {},
            outputs: [],
            source: [""],
          },
        ],
      };

      const res: any = await updateContentMutation.mutateAsync({
        path: filePath,
        content: notebookJson,
        type: "notebook",
        format: "json",
      });

      const added: Notebook = {
        id: res.id ?? res.path ?? filePath,
        name: res.name ?? filename,
        description: res.description ?? "",
        author: res.author ?? "",
        lastModified:
          res.last_modified ?? res.lastModified ?? new Date().toISOString(),
        size: typeof res.size === "number" ? `${res.size}` : res.size ?? "0",
        version: res.version ?? "",
        type: "User Created",
        tags: res.tags ?? [],
        // @ts-ignore
        path: res.path ?? filePath,
      };

      setNotebooks((prev) => {
        if (!isIpynb(added.name)) return prev;
        const exists = prev.some((n) => n.id === added.id || n.name === added.name);
        return exists ? prev : [added, ...prev];
      });

      toast.success(`Created ${added.name}`);
    } catch (err: any) {
      console.error("Create new notebook failed:", err);
      toast.error(err?.response?.data?.message || "Failed to create notebook");
    }
  };

  const handleView = (notebook: Notebook) => {
    navigateTo("notebook", { file: notebook });
  };

  return (
    <div className="p-4 mt-4 space-y-2 bg-white">
      <div className="flex items-center">
        <Button variant="destructive" className="p-4" onClick={goBack}>
         <ArrowLeft className="!h-6 !w-8 text-gray-700" />
        </Button>
        <h2 className="text-xl font-semibold flex items-center gap-2">
          Notebooks
        </h2>
        <div className="ml-auto flex items-center gap-2">
          <Button onClick={handleUpload} variant="secondary" className="mt-4">
            <Upload className="h-4 w-4" />
            Upload notebook
          </Button>
          <Button onClick={handleCreateNew} variant="secondary"  className="mt-4">
            <Plus className="h-4 w-4" />
            New notebook
          </Button>
        </div>
      </div>

      <p className="text-muted-foreground">
        Manage Jupyter notebooks, upload new files, or create new notebooks for assignments
      </p>

      <div className="relative">
        <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search notebooks..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-9"
        />
      </div>

      <div className="grid gap-3">
        {filteredNotebooks.map((n) => (
          <div key={n.id} className="border rounded-lg p-3 bg-background">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {/* <span className={`border px-2 py-0.5 rounded text-xs ${getTypeColor(n.type)}`}>
                  {n.type ?? "User Created"}
                </span> */}
                <span className="font-medium">{n.name}</span>
                {/* <span className="text-muted-foreground">· {n.version ?? "v1"}</span> */}
              </div>
              <div className="flex items-center gap-2">
                <Button size="sm" variant="outline" onClick={() => handleView(n)}>
                 <Eye className="h-4 w-4" />
                  Open
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleDownload(n)}>
                  Download
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleDuplicate(n)}>
                  Duplicate
                </Button>
              </div>
            </div>

            <div className="mt-1 text-sm text-muted-foreground">{n.description ?? ""}</div>

            <div className="mt-2 flex items-center gap-3 text-xs text-muted-foreground">
              {/* <span>👤 {n.author ?? "—"}</span>
              <span>⏰ {n.lastModified ?? "—"}</span> */}
              <span>📁 {n.size ?? "—"}kb</span>
            </div>

            <div className="mt-2 flex gap-2">
              {(n.tags ?? []).map((tag) => (
                <Badge key={tag} variant="outline">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        ))}

        {filteredNotebooks.length === 0 && (
          <div className="text-sm text-muted-foreground">No notebooks match the current search.</div>
        )}
      </div>
    </div>
  );
}
