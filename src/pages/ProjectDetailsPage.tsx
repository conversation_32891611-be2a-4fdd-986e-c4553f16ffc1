import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { parseISO, format } from "date-fns";
import { useQuery } from "@tanstack/react-query";
import { EnhancedProject, getProjectById } from "../api/projectManagementApi";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Progress } from "../components/ui/progress";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { Textarea } from "../components/ui/textarea";
import paths from "../routes";
import {
  Calendar,
  Clock,
  CheckCircle,
  Download,
  Upload,
  BookOpen,
  AlertCircle,
  Users,
  Target,
  FileText,
  Database,
  Play,
  Terminal,
  RefreshCw,
  CheckSquare,
  ExternalLink,
  Power,
  Settings,
  X,
  Plus,
  Trash2,
  Send,
  ArrowLeft,
  Clipboard,
  Check,
  User,
  Flag,
  ChevronDown,
  ChevronUp,
  MessageSquare,
  Eye,
  Rocket,
  Award,
  RotateCcw,
} from "lucide-react";
// import useAuth from "../components/Context/AuthContext";
// import useNavigation from "../components/Context/NavigationContext";
import { toast } from "sonner";
import { useAuth, useNavigation } from "../App";
import { useJupyterWorkspace } from "../contexts/JupyterWorkspaceContext";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../components/ui/dialog";
import { Separator } from "../components/ui/separator";
import { cn } from "../lib/utils";
import axios from "axios";
import { Avatar, AvatarFallback, AvatarImage } from "@radix-ui/react-avatar";
import { useCreateWorkspaceMutation } from "../api/jupyterManagementApi";
import { useNavigate, useParams } from "react-router-dom";

interface Project {
  id: string;
  title: string;
  description: string;
  subject: string;
  instructor: string;
  dueDate: string;
  createdDate: string;
  status: "not_started" | "in_progress" | "submitted" | "graded";
  progress: number;
  grade?: number;
  totalPoints: number;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
  objectives: string[];
  requirements: string[];
  datasets: Dataset[];
  submissions: Submission[];
  rubrics: RubricItem[];
  estimatedHours: number;
  tags: string[];
  checkpoints: Checkpoint[];
  total_points: number
}

interface Checkpoint {
  id: string;
  title: string;
  description: string;
  status: "not_started" | "in_progress" | "submitted" | "graded" | "overdue";
  due_date: string;
  due_date: string;
  submittedDate?: string;
  grade?: string;
  instructorFeedback?: string;
}

interface Dataset {
  id: string;
  name: string;
  description: string;
  format: string;
  size: string;
  downloadUrl: string;
}

interface Submission {
  id: string;
  submittedAt: string;
  files: string[];
  grade?: number;
  feedback?: string;
  status: "submitted" | "graded" | "revision_needed";
}

interface RubricItem {
  id: string;
  title: string,
  criteria: any[];
  description: string;
  total_points: number;
  earnedPoints?: number;
}

interface SandboxStatus {
  id?: string;
  status: "none" | "provisioning" | "active" | "stopping" | "stopped" | "error";
  instanceId?: string;
  provisioningProgress?: number;
  estimatedTime?: string;
  createdAt?: string;
  lastActivity?: string;
  errorMessage?: string;
  autoSaveEnabled?: boolean;
}

interface SubmissionFile {
  id: string;
  name: string;
  size: number;
  type: string;
  file: File;
}

interface TeamMember {
  name: string;
  email: string;
  avatar: string;
}

interface instructor {
  name: string;
  role: "TA" | "Instructor";
  email: string;
}

// // instructor sample data
// const instructors: instructor[] = [
//   {
//     name: "Ms. L. Patel",
//     role: "TA",
//     email: "<EMAIL>",
//   },
//   {
//     name: "Ms. R. Sharma",
//     role: "Instructor",
//     email: "<EMAIL>",
//   },
// ];

// the team members array
// const teamMembers: TeamMember[] = [
//   {
//     name: "Ms. L. Patel",
//     email: "<EMAIL>",
//     avatar: "../assets/teammember-avatar.jpg",
//   },
//   {
//     name: "Ms. R. Sharma",
//     email: "<EMAIL>",
//     avatar: "../assets/teammember-avatar.jpg",
//   },
// ];

// // Mall Customers Project Data
// const mallCustomersProjects: { [key: string]: Project } = {
//   "mall-customers-part-1": {
//     id: "mall-customers-part-1",
//     title: "Mall Customers - Part 1: K-Means Clustering Implementation",
//     description:
//       "Implement k-means clustering algorithm from scratch to segment mall customers based on their purchasing behavior. This foundational project focuses on understanding unsupervised learning principles, implementing clustering algorithms, and analyzing customer segments for business insights.",
//     subject: "Machine Learning",
//     instructor: "Dr. A. Sharma",
//     dueDate: "2025-01-20T23:59:00Z",
//     createdDate: "2025-01-10T00:00:00Z",
//     status: "in_progress",
//     progress: 60,
//     totalPoints: 100,
//     difficulty: "Intermediate",
//     estimatedHours: 20,
//     tags: [
//       "K-Means",
//       "Clustering",
//       "Unsupervised Learning",
//       "Customer Segmentation",
//       "Python",
//       "NumPy",
//     ],
//     objectives: [
//       "Implement k-means clustering algorithm from scratch without using sklearn",
//       "Apply the algorithm to real mall customer data with age, income, and spending score",
//       "Determine optimal number of clusters using elbow method and silhouette analysis",
//       "Analyze customer segments and identify distinct purchasing behavior patterns",
//       "Create comprehensive visualizations showing cluster distributions and characteristics",
//       "Validate clustering results and assess algorithm performance",
//       "Document implementation with clear code comments and methodology explanation",
//     ],
//     requirements: [
//       "Python programming proficiency with NumPy and Pandas",
//       "Understanding of distance metrics (Euclidean distance)",
//       "Basic linear algebra concepts (centroids, vector operations)",
//       "Data visualization skills with Matplotlib/Seaborn",
//       "Jupyter Notebook environment setup",
//       "Understanding of unsupervised learning concepts",
//       "Basic statistics knowledge for cluster validation",
//     ],
//     datasets: [
//       {
//         id: "mall_customers_main",
//         name: "Mall Customers Dataset",
//         description:
//           "Customer data from a mall containing 200 customer records with demographics and spending behavior. Features include CustomerID, Gender, Age, Annual Income (k$), and Spending Score (1-100).",
//         format: "CSV",
//         size: "8.2 KB",
//         downloadUrl: "/datasets/mall_customers.csv",
//       },
//       {
//         id: "mall_customers_dictionary",
//         name: "Data Dictionary",
//         description:
//           "Comprehensive description of all features in the mall customers dataset including value ranges, data types, and business context for each attribute.",
//         format: "PDF",
//         size: "245 KB",
//         downloadUrl: "/datasets/mall_customers_data_dictionary.pdf",
//       },
//       {
//         id: "clustering_starter_code",
//         name: "K-Means Starter Template",
//         description:
//           "Jupyter notebook template with data loading utilities, visualization functions, and code structure to help you get started with the k-means implementation.",
//         format: "IPYNB",
//         size: "156 KB",
//         downloadUrl: "/templates/kmeans_starter_template.ipynb",
//       },
//     ],
//     submissions: [
//       {
//         id: "sub_mc1_1",
//         submittedAt: "2025-01-15T14:30:00Z",
//         files: [
//           "kmeans_implementation_v1.ipynb",
//           "clustering_utils.py",
//           "visualization_plots.png",
//         ],
//         status: "graded",
//         grade: 78,
//         feedback:
//           "Good implementation of k-means algorithm with proper centroid initialization. Your elbow method analysis is thorough. Consider improving cluster validation metrics and adding more detailed business interpretation of segments. The visualization quality is excellent.",
//       },
//     ],
//     rubric: [
//       {
//         id: "mc1_r1",
//         criteria: "K-Means Algorithm Implementation",
//         description:
//           "Complete from-scratch implementation with proper initialization, convergence criteria, and distance calculations",
//         points: 30,
//         earnedPoints: 25,
//       },
//       {
//         id: "mc1_r2",
//         criteria: "Data Preprocessing & Exploration",
//         description:
//           "Thorough data analysis, preprocessing steps, and feature scaling/normalization",
//         points: 20,
//         earnedPoints: 18,
//       },
//       {
//         id: "mc1_r3",
//         criteria: "Optimal Cluster Selection",
//         description:
//           "Implementation of elbow method, silhouette analysis, and justified cluster number selection",
//         points: 25,
//         earnedPoints: 20,
//       },
//       {
//         id: "mc1_r4",
//         criteria: "Clustering Analysis & Interpretation",
//         description:
//           "Clear analysis of customer segments with business insights and actionable recommendations",
//         points: 15,
//         earnedPoints: 10,
//       },
//       {
//         id: "mc1_r5",
//         criteria: "Code Quality & Documentation",
//         description:
//           "Well-documented code, proper function structure, and clear methodology explanation",
//         points: 10,
//         earnedPoints: 5,
//       },
//     ],
//     checkpoints: [
//       {
//         id: "cp_1",
//         title: "Data Visualization",
//         description:
//           "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
//         status: "graded",
//         dueDate: "16 Jul 2025",
//         grade: "A",
//         instructorFeedback: "Lorem Ipsum",
//       },
//       {
//         id: "cp_2",
//         title: "Data Visualization",
//         description:
//           "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
//         status: "not_started",
//         dueDate: "16 Jul 2025",
//       },
//     ],
//   },

//   "mall-customers-part-2": {
//     id: "mall-customers-part-2",
//     title: "Mall Customers - Part 2: Cluster Labeling & Advanced Visualization",
//     description:
//       "Building on Part 1, create meaningful business labels for customer clusters and develop an interactive visualization dashboard. Focus on translating technical clustering results into actionable business insights through customer personas and strategic recommendations.",
//     subject: "Machine Learning",
//     instructor: "Dr. A. Sharma",
//     dueDate: "2025-01-25T23:59:00Z",
//     createdDate: "2025-01-15T00:00:00Z",
//     status: "in_progress",
//     progress: 45,
//     totalPoints: 100,
//     difficulty: "Intermediate",
//     estimatedHours: 18,
//     tags: [
//       "Data Visualization",
//       "Customer Personas",
//       "Business Analysis",
//       "Plotly",
//       "Dashboard",
//       "Market Segmentation",
//     ],
//     objectives: [
//       "Analyze cluster characteristics from Part 1 to understand customer segment patterns",
//       "Create meaningful business names and labels for each customer cluster",
//       "Develop detailed customer personas with demographic and behavioral profiles",
//       "Build interactive visualizations using Plotly or similar advanced visualization libraries",
//       "Design a comprehensive dashboard showing cluster distributions and key metrics",
//       "Generate actionable business recommendations for each customer segment",
//       "Create executive summary with strategic insights for mall management",
//     ],
//     requirements: [
//       "Completed Part 1 (K-Means Clustering) with clustering results",
//       "Advanced Python visualization libraries (Plotly, Bokeh, or Dash)",
//       "Understanding of customer segmentation business concepts",
//       "Experience with interactive dashboard development",
//       "Knowledge of customer persona development frameworks",
//       "Business analysis and strategic thinking skills",
//       "Presentation and communication skills for executive reporting",
//     ],
//     datasets: [
//       {
//         id: "mall_customers_clustered",
//         name: "Clustered Mall Customers Data",
//         description:
//           "Enhanced mall customers dataset with cluster assignments from Part 1, including cluster IDs and additional derived features for analysis.",
//         format: "CSV",
//         size: "12.8 KB",
//         downloadUrl: "/datasets/mall_customers_with_clusters.csv",
//       },
//       {
//         id: "customer_persona_template",
//         name: "Customer Persona Template",
//         description:
//           "Professional template for creating detailed customer personas including demographics, behaviors, preferences, and strategic recommendations.",
//         format: "DOCX",
//         size: "1.2 MB",
//         downloadUrl: "/templates/customer_persona_template.docx",
//       },
//       {
//         id: "visualization_examples",
//         name: "Advanced Visualization Examples",
//         description:
//           "Sample Jupyter notebook with Plotly examples for creating interactive charts, 3D plots, and dashboard components for customer analytics.",
//         format: "IPYNB",
//         size: "2.4 MB",
//         downloadUrl: "/examples/advanced_visualization_examples.ipynb",
//       },
//       {
//         id: "business_case_studies",
//         name: "Retail Customer Segmentation Case Studies",
//         description:
//           "Collection of real-world case studies showing how major retailers use customer segmentation for marketing strategy and business growth.",
//         format: "PDF",
//         size: "5.6 MB",
//         downloadUrl: "/resources/retail_segmentation_case_studies.pdf",
//       },
//     ],
//     submissions: [
//       {
//         id: "sub_mc2_1",
//         submittedAt: "2025-01-18T16:45:00Z",
//         files: [
//           "customer_personas_draft.ipynb",
//           "interactive_dashboard.html",
//           "business_insights.pdf",
//         ],
//         status: "graded",
//         grade: 82,
//         feedback:
//           "Excellent customer persona development with clear business names for clusters. Your interactive dashboard is well-designed and informative. Consider adding more specific marketing recommendations and competitor analysis to strengthen business insights.",
//       },
//     ],
//     rubric: [
//       {
//         id: "mc2_r1",
//         criteria: "Cluster Analysis & Interpretation",
//         description:
//           "Thorough analysis of cluster characteristics with statistical summaries and pattern identification",
//         points: 25,
//         earnedPoints: 22,
//       },
//       {
//         id: "mc2_r2",
//         criteria: "Customer Persona Development",
//         description:
//           "Creative and meaningful cluster labels with detailed customer personas including demographics and behaviors",
//         points: 25,
//         earnedPoints: 21,
//       },
//       {
//         id: "mc2_r3",
//         criteria: "Interactive Visualization & Dashboard",
//         description:
//           "Professional interactive dashboard with clear, insightful visualizations and user-friendly interface",
//         points: 30,
//         earnedPoints: 24,
//       },
//       {
//         id: "mc2_r4",
//         criteria: "Business Insights & Recommendations",
//         description:
//           "Actionable business recommendations with strategic insights and marketing implications",
//         points: 20,
//         earnedPoints: 15,
//       },
//     ],
//     checkpoints: [
//       {
//         id: "cp_1",
//         title: "Data Visualization",
//         description:
//           "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
//         status: "graded",
//         dueDate: "16 Jul 2025",
//         grade: "A",
//         instructorFeedback: "Lorem Ipsum",
//       },
//       {
//         id: "cp_2",
//         title: "Data Visualization",
//         description:
//           "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
//         status: "not_started",
//         dueDate: "16 Jul 2025",
//       },
//     ],
//   },

//   "mall-customers-part-3": {
//     id: "mall-customers-part-3",
//     title: "Mall Customers - Part 3: kNN Prediction System",
//     description:
//       "Implement a complete k-nearest neighbors prediction system using the labeled customer clusters from Parts 1-2. Build a production-ready model that can classify new customers into appropriate segments, complete with performance evaluation and deployment pipeline.",
//     subject: "Machine Learning",
//     instructor: "Dr. A. Sharma",
//     dueDate: "2025-01-30T23:59:00Z",
//     createdDate: "2025-01-20T00:00:00Z",
//     status: "not_started",
//     progress: 0,
//     totalPoints: 100,
//     difficulty: "Advanced",
//     estimatedHours: 25,
//     tags: [
//       "kNN",
//       "Supervised Learning",
//       "Model Evaluation",
//       "Cross-Validation",
//       "Deployment",
//       "Production ML",
//     ],
//     objectives: [
//       "Implement k-nearest neighbors algorithm from scratch with multiple distance metrics",
//       "Use labeled customer clusters from Parts 1-2 as training data for supervised learning",
//       "Optimize hyperparameter k using cross-validation and grid search techniques",
//       "Evaluate model performance using comprehensive metrics (accuracy, precision, recall, F1-score)",
//       "Build a complete prediction pipeline for classifying new customers",
//       "Develop a user-friendly interface for real-time customer classification",
//       "Create model documentation and deployment guidelines for production use",
//       "Compare kNN performance with other classification algorithms (optional bonus)",
//     ],
//     requirements: [
//       "Completed Parts 1 & 2 with labeled customer cluster dataset",
//       "Advanced Python programming with object-oriented design principles",
//       "Understanding of supervised learning and classification algorithms",
//       "Experience with model evaluation techniques and cross-validation",
//       "Knowledge of hyperparameter optimization methods",
//       "Familiarity with Flask/Streamlit for building prediction interfaces",
//       "Understanding of production ML pipelines and model deployment",
//       "Statistical knowledge for interpreting model performance metrics",
//     ],
//     datasets: [
//       {
//         id: "labeled_customers_training",
//         name: "Labeled Customer Training Dataset",
//         description:
//           "Complete training dataset with customer features and their assigned cluster labels from Parts 1-2, formatted for supervised learning with 80/20 train/test split.",
//         format: "CSV",
//         size: "15.6 KB",
//         downloadUrl: "/datasets/labeled_customers_training.csv",
//       },
//       {
//         id: "new_customers_test",
//         name: "New Customers Test Dataset",
//         description:
//           "Fresh customer data for testing your kNN model, containing 50 new customer profiles without cluster labels for prediction validation.",
//         format: "CSV",
//         size: "3.2 KB",
//         downloadUrl: "/datasets/new_customers_test.csv",
//       },
//       {
//         id: "knn_implementation_template",
//         name: "kNN Implementation Template",
//         description:
//           "Structured Python template with class definitions, method stubs, and helper functions to guide your kNN implementation.",
//         format: "PY",
//         size: "8.4 KB",
//         downloadUrl: "/templates/knn_implementation_template.py",
//       },
//       {
//         id: "model_evaluation_toolkit",
//         name: "Model Evaluation Toolkit",
//         description:
//           "Comprehensive Python toolkit with functions for cross-validation, performance metrics, confusion matrices, and model comparison utilities.",
//         format: "PY",
//         size: "12.7 KB",
//         downloadUrl: "/utils/model_evaluation_toolkit.py",
//       },
//       {
//         id: "deployment_guide",
//         name: "ML Model Deployment Guide",
//         description:
//           "Step-by-step guide for deploying machine learning models in production, including Flask API development and Docker containerization.",
//         format: "PDF",
//         size: "4.8 MB",
//         downloadUrl: "/guides/ml_model_deployment_guide.pdf",
//       },
//     ],
//     submissions: [],
//     rubric: [
//       {
//         id: "mc3_r1",
//         criteria: "kNN Algorithm Implementation",
//         description:
//           "Complete from-scratch kNN implementation with multiple distance metrics and efficient nearest neighbor search",
//         points: 30,
//       },
//       {
//         id: "mc3_r2",
//         criteria: "Model Optimization & Validation",
//         description:
//           "Proper hyperparameter tuning using cross-validation with justified k selection and robust evaluation methodology",
//         points: 25,
//       },
//       {
//         id: "mc3_r3",
//         criteria: "Performance Evaluation & Analysis",
//         description:
//           "Comprehensive model evaluation with multiple metrics, confusion matrices, and statistical significance testing",
//         points: 25,
//       },
//       {
//         id: "mc3_r4",
//         criteria: "Prediction Pipeline & Interface",
//         description:
//           "Complete end-to-end prediction system with user interface and real-time classification capabilities",
//         points: 15,
//       },
//       {
//         id: "mc3_r5",
//         criteria: "Code Quality & Production Readiness",
//         description:
//           "Professional code structure, documentation, error handling, and deployment-ready implementation",
//         points: 5,
//       },
//     ],
//     checkpoints: [
//       {
//         id: "cp_1",
//         title: "Data Visualization",
//         description:
//           "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
//         status: "graded",
//         dueDate: "16 Jul 2025",
//         grade: "A",
//         instructorFeedback: "Lorem Ipsum",
//       },
//       {
//         id: "cp_2",
//         title: "Data Visualization",
//         description:
//           "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
//         status: "not_started",
//         dueDate: "16 Jul 2025",
//         grade: "A",
//         instructorFeedback: "Lorem Ipsum",
//       },
//     ],
//   },
// };

// Default project for non-Mall Customers projects
const defaultProject: Project = {
  id: "proj_1",
  title: "Housing Price Prediction",
  description:
    "In this comprehensive project, you will build and evaluate machine learning models to predict housing prices using the california housing dataset. You will apply data preprocessing techniques, feature engineering, and various regression algorithms to create an accurate prediction model.",
  subject: "Data Science Fundamentals",
  instructor: "Dr. A. Sharma",
  due_date: "2025-01-15T23:59:00Z",
  createdDate: "2025-01-01T00:00:00Z",
  status: "in_progress",
  progress: 65,
  total_points: 100,
  difficulty_level: "Intermediate",
  estimated_hours: 15,
  tags: [
    "Machine Learning",
    "Regression",
    "Data Preprocessing",
    "Feature Engineering",
  ],
  objectives: [
    "Understand and apply data preprocessing techniques for real-world datasets",
    "Implement feature engineering strategies to improve model performance",
    "Build and compare multiple regression models (Linear, Ridge, Lasso)",
    "Evaluate model performance using appropriate metrics",
    "Create compelling visualizations to communicate findings",
    "Deploy your model using AWS SageMaker (optional)",
  ],
  requirements: [
    "Python programming proficiency",
    "Basic understanding of statistics and linear algebra",
    "Familiarity with pandas, numpy, and scikit-learn",
    "Jupyter Notebook environment",
    "Matplotlib/Seaborn for visualizations",
  ],
  datasets: [
    {
      id: "ds_1",
      name: "California Housing Dataset",
      description:
        "Housing data from the 1990 California census with 20,640 observations and 10 features including median income, house age, and location coordinates.",
      format: "CSV",
      size: "2.4 MB",
      downloadUrl: "/datasets/california_housing.csv",
    },
    {
      id: "ds_2",
      name: "Housing Features Dictionary",
      description:
        "Detailed description of all features in the housing dataset including data types, ranges, and definitions.",
      format: "PDF",
      size: "156 KB",
      downloadUrl: "/datasets/housing_data_dictionary.pdf",
    },
  ],
  submissions: [
    {
      id: "sub_1",
      submittedAt: "2025-01-05T14:30:00Z",
      files: ["housing_analysis_v1.ipynb", "preprocessing_utils.py"],
      status: "graded",
      grade: 75,
      feedback:
        "Good initial analysis. Consider improving feature engineering and model validation techniques.",
    },
  ],
  rubric: [
    {
      id: "r_1",
      criteria: "Data Exploration & Visualization",
      description:
        "Comprehensive exploration of the dataset with meaningful visualizations",
      points: 20,
      earnedPoints: 18,
    },
    {
      id: "r_2",
      criteria: "Data Preprocessing",
      description:
        "Proper handling of missing values, outliers, and data transformation",
      points: 20,
      earnedPoints: 15,
    },
    {
      id: "r_3",
      criteria: "Feature Engineering",
      description: "Creation of meaningful features and feature selection",
      points: 25,
    },
    {
      id: "r_4",
      criteria: "Model Implementation",
      description:
        "Implementation of multiple regression models with proper validation",
      points: 25,
    },
    {
      id: "r_5",
      criteria: "Analysis & Interpretation",
      description: "Clear interpretation of results and model performance",
      points: 10,
    },
  ],
  checkpoints: [
    {
      id: "cp_1",
      title: "Data Visualization",
      description:
        "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
      status: "submitted",
      due_date: "12 jun - 16 Jul 2025",
      grade: "A",
      instructorFeedback: "Lorem Ipsum",
    },
    {
      id: "cp_2",
      title: "Data Visualization",
      description:
        "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
      status: "in_progress",
      due_date: "12 jun - 16 Jul 2025",
      grade: "A",
      instructorFeedback: "Lorem Ipsum",
    },
    {
      id: "cp_3",
      title: "Data Visualization",
      description:
        "Visualized distributions, correlations, and trends using histograms, boxplots, and heatmaps to understand feature relationships and guide model development.",
      status: "in_progress",
      due_date: "12 jun - 16 Jul 2025",
    },
  ],
};

function SandboxLaunchDialog({
  onLaunch,
  sandboxStatus,
}: {
  onLaunch: () => void;
  sandboxStatus: SandboxStatus;
}) {
  const [open, setOpen] = useState(false);
  const [agreed, setAgreed] = useState(false);

  const handleLaunch = () => {
    if (agreed) {
      onLaunch();
      setOpen(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          className="bg-bits-blue hover:bg-bits-blue/90 w-full h-full p-3"
          disabled={sandboxStatus.status === "provisioning"}
        >
          {sandboxStatus.status === "provisioning" ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Preparing Sandbox..
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              Launch Sandbox
            </>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl bg-white border">
        <DialogHeader>
          <DialogTitle>Launch Sandbox Environment</DialogTitle>
          <DialogDescription>
            This will provision a dedicated Jupyter notebook environment in your
            account.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">
              What will be provisioned:
            </h4>
            <ul className="text-blue-800 text-sm space-y-1">
              <li>• EC2 t3.medium instance with Jupyter Lab</li>
              <li>• Pre-installed Python data science libraries</li>
              <li>• Project datasets and starter code</li>
              <li>• Auto-save functionality (every 2 minutes)</li>
              <li>• 8-hour session with auto-shutdown</li>
            </ul>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-medium text-yellow-900 mb-2">
              Important Notes:
            </h4>
            <ul className="text-yellow-800 text-sm space-y-1">
              <li>• Provisioning typically takes 2-3 minutes</li>
              <li>• Your work is automatically saved and versioned</li>
              <li>
                • Environment will shutdown after 8 hours or upon submission
              </li>
              <li>• Additional costs may apply to your account</li>
            </ul>
          </div>

          <div className="flex items-start space-x-2">
            <input
              type="checkbox"
              id="agreement"
              checked={agreed}
              onChange={(e) => setAgreed(e.target.checked)}
              className="mt-1"
            />
            <label htmlFor="agreement" className="text-sm">
              I understand the provisioning process and associated costs. I
              agree to the
              <a href="#" className="text-bits-blue hover:underline ml-1">
                terms and conditions
              </a>
              .
            </label>
          </div>

          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleLaunch}
              disabled={!agreed}
              className="bg-bits-blue hover:bg-bits-blue/90"
            >
              Launch Sandbox
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function ProvisioningProgress({
  sandboxStatus,
}: {
  sandboxStatus: SandboxStatus;
}) {
  if (sandboxStatus.status !== "provisioning") return null;

  return (
    <Card className="border-blue-200 bg-blue-50">
      <CardContent className="p-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <RefreshCw className="h-6 w-6 text-blue-600 animate-spin" />
            <div>
              <h3 className="font-medium text-blue-900">
                Provisioning Sandbox Environment
              </h3>
              <p className="text-blue-700 text-sm">
                Setting up your dedicated Jupyter notebook environment...
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-blue-700">Progress</span>
              <span className="text-blue-700">
                {Math.floor(sandboxStatus.provisioningProgress || 0)}%
              </span>
            </div>
            <Progress
              value={Math.floor(sandboxStatus.provisioningProgress || 0)}
              className="h-3"
            />
          </div>

          <div className="text-sm text-blue-700">
            Estimated time remaining:{" "}
            {sandboxStatus.estimatedTime || "2-3 minutes"}
          </div>

          <div className="space-y-1 text-xs text-blue-600">
            <div>✓ credentials validated</div>
            <div>✓ Instance launching...</div>
            <div className="opacity-50">⏳ Installing dependencies</div>
            <div className="opacity-50">⏳ Setting up Jupyter environment</div>
            <div className="opacity-50">⏳ Loading project data</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function ActiveSandboxCard({
  sandboxStatus,
  onOpenSandbox,
}: {
  sandboxStatus: SandboxStatus;
  onOpenSandbox: () => void;
}) {
  if (sandboxStatus.status !== "active") return null;

  const timeAgo = (date: string) => {
    const now = new Date();
    const past = new Date(date);
    const diffInMinutes = Math.floor(
      (now.getTime() - past.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    return `${diffInHours}h ago`;
  };

  return (
    <Card className="border-green-200 bg-green-50 mb-2">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
              <h3 className="font-medium text-green-900">
                Sandbox Environment Active
              </h3>
            </div>

            <div className="space-y-1 text-sm text-green-700">
              <div>Instance: {sandboxStatus.instanceId}</div>
              <div>
                Last activity: {timeAgo(sandboxStatus.lastActivity || "")}
              </div>
              <div>
                Auto-save:{" "}
                {sandboxStatus.autoSaveEnabled ? "Enabled" : "Disabled"}
              </div>
            </div>
          </div>

          {/* <div className="space-y-2">
            <Button
              onClick={onOpenSandbox}
              className="bg-green-600 hover:bg-green-700"
            >
              <Terminal className="h-4 w-4 mr-2" />
              Open Sandbox
            </Button>
          </div> */}
        </div>
      </CardContent>
    </Card>
  );
}

function SubmissionDialog({
  project,
  open,
  onOpenChange,
  onSubmit,
}: {
  project: Project;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (files: SubmissionFile[], comments: string) => void;
}) {
  const [files, setFiles] = useState<SubmissionFile[]>([]);
  const [comments, setComments] = useState("");
  const [dragActive, setDragActive] = useState(false);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  };

  const handleFiles = (fileList: FileList) => {
    const newFiles: SubmissionFile[] = [];

    for (let i = 0; i < fileList.length; i++) {
      const file: any = fileList[i];
      const submissionFile: SubmissionFile = {
        id: `file_${Date.now()}_${i}`,
        name: file.name,
        size: file.size,
        type: file.type || "application/octet-stream",
        file: file,
      };
      newFiles.push(submissionFile);
    }

    setFiles((prev) => [...prev, ...newFiles]);
  };

  const removeFile = (fileId: string) => {
    setFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleSubmit = () => {
    if (files.length === 0) {
      toast.error("Please upload at least one file");
      return;
    }

    onSubmit(files, comments);
    // Reset form
    setFiles([]);
    setComments("");
    onOpenChange(false);
  };

  const totalSize = files.reduce((sum, file) => sum + file.size, 0);
  const maxSize = 100 * 1024 * 1024;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto bg-white border">
        <DialogHeader>
          <DialogTitle>Submit Work - {project.title}</DialogTitle>
          <DialogDescription>
            Upload your project files and add submission comments
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Project Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-blue-900">Due Date:</span>
                <span className="ml-2 text-blue-800">
                  {new Date(project.due_date).toLocaleDateString()} at{" "}
                  {new Date(project.due_date).toLocaleTimeString()}
                </span>
              </div>
              <div>
                <span className="font-medium text-blue-900">Total Points:</span>
                <span className="ml-2 text-blue-800">
                  {project.total_points} points
                </span>
              </div>
              <div>
                <span className="font-medium text-blue-900">Subject:</span>
                <span className="ml-2 text-blue-800">{project.subject}</span>
              </div>
              <div>
                <span className="font-medium text-blue-900">Instructor:</span>
                <span className="ml-2 text-blue-800">{project.instructor}</span>
              </div>
            </div>
          </div>

          {/* File Upload Area */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Upload Files</Label>
            <div
              className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${dragActive
                ? "border-bits-blue bg-blue-50"
                : "border-gray-300 hover:border-gray-400"
                }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-900">
                  Drop files here or click to browse
                </p>
                <p className="text-sm text-gray-500">
                  Supported formats: .ipynb, .py, .pdf, .csv, .png, .jpg, .txt,
                  .md
                </p>
                <p className="text-xs text-gray-400">
                  Maximum total size: 100MB
                </p>
              </div>
              <input
                type="file"
                multiple
                onChange={handleChange}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                accept=".ipynb,.py,.pdf,.csv,.png,.jpg,.jpeg,.txt,.md,.docx,.xlsx,.zip"
              />
            </div>
          </div>

          {/* File List */}
          {files.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">
                  Uploaded Files ({files.length})
                </Label>
                <div className="text-sm text-gray-500">
                  Total: {formatFileSize(totalSize)} / {formatFileSize(maxSize)}
                </div>
              </div>

              <div className="space-y-2 max-h-48 overflow-y-auto">
                {files?.map((file) => (
                  <div
                    key={file.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center space-x-3 flex-1 min-w-0">
                      <FileText className="h-5 w-5 text-blue-600 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <p className="font-medium truncate">{file.name}</p>
                        <p className="text-sm text-gray-500">
                          {formatFileSize(file.size)} • {file.type}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(file.id)}
                      className="text-red-600 hover:text-red-800 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>

              {totalSize > maxSize && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-800 text-sm">
                    Total file size exceeds the 100MB limit. Please remove some
                    files.
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Comments */}
          <div className="space-y-2">
            <Label htmlFor="comments" className="text-base font-medium">
              Submission Comments (Optional)
            </Label>
            <Textarea
              id="comments"
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              placeholder="Add any comments about your submission, challenges faced, or additional notes for the instructor..."
              rows={4}
              className="resize-none"
            />
          </div>

          {/* Submission Guidelines */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">
              Submission Guidelines
            </h4>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>
                • Ensure all required files are included and properly named
              </li>
              <li>
                • Include a main notebook (.ipynb) file with your complete
                analysis
              </li>
              <li>• Add comments and documentation to your code</li>
              <li>
                • Test your code before submission to ensure it runs without
                errors
              </li>
              <li>• Include any additional resources or references used</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={files.length === 0 || totalSize > maxSize}
              className="bg-bits-blue hover:bg-bits-blue/90"
            >
              <Send className="h-4 w-4 mr-2" />
              Submit Work
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

let cleanupHasRun = false;
let persistentSandboxState = {
  status: "none" as
    | "none"
    | "provisioning"
    | "active"
    | "stopping"
    | "stopped"
    | "error",
  token: null as string | null,
};

interface ProjectDetailsProps {
  project: Project;
}

const ProjectDetails: React.FC<ProjectDetailsProps> = ({ project }) => {
  const dueDate = new Date(project.dueDate);
  const now = new Date();
  const daysLeft = Math.ceil(
    (dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
  );
  const dueInfo = {
    urgent: daysLeft <= 3,
    text:
      daysLeft > 0
        ? `${daysLeft} days left`
        : daysLeft === 0
          ? "Due today"
          : "Overdue",
  };
  console.log(project)
  return (
    <div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Project Overview */}
          <Card className="gap-4">
            <CardHeader>
              <div className="flex">
                <div className="h-6 w-8 ">
                  <Clipboard className=" h-6 w-6 text-bits-blue" />
                </div>
                <CardTitle className="text-xl text-bits-blue font-semibold mt-0">
                  Project Overview
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="ml-1">
              <p className="text-muted-foreground mb-8">
                {project.description}
              </p>

              <div className="flex justify-between items-center mx-14">
                <div className="text-center">
                  <div className=" text-2xl font-bold">
                    {project.estimated_hours}h
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Estimated Time
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {project.total_points}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Total Points
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold capitalize">{project.difficulty_level}</div>
                  <div className="text-xs text-muted-foreground">
                    Difficulty
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Learning Objectives */}
          <Card className="gap-4">
            <CardHeader>
              <CardTitle className="flex text-xl text-bits-blue items-center font-semibold">
                <Target className="h-6 w-6 mr-2  text-bits-green" />
                Learning Objectives
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {project?.learning_objectives}
              </ul>
            </CardContent>
          </Card>

          {/* Requirements */}
          <Card>
            <CardHeader>
              <CardTitle className="flex text-xl text-bits-blue items-center font-semibold">
                <BookOpen className="h-6 w-6 mr-3 text-blue-500" />
                Prerequisites & Requirements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {project?.prerequisites
                  ?.split("\n\n")
                  .map((requirement, index) => {
                    // clean the block
                    const cleaned = requirement.replace(/^Prerequisites:\s*/i, "").trim();

                    // skip empty results (like when only "Prerequisites:" was present)
                    if (!cleaned) return null;

                    return (
                      <li key={index} className="flex items-start space-x-2">
                        <div className="w-2 h-2 bg-bits-blue rounded-full m-2 flex-shrink-0" />
                        <span className="text-muted-foreground whitespace-pre-line">
                          {cleaned}
                        </span>
                      </li>
                    );
                  })}

              </ul>

            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Due Date */}
          <Card>
            <CardHeader>
              <CardTitle className="flex text-xl text-bits-blue items-center font-semibold">
                <Calendar className="h-6 w-6 mr-2 text-bits-red" />
                Upcoming Due Date
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-2xl font-bold mb-3">
                  {project?.due_date
                    ? (() => {
                      const date = parseISO(project.due_date);
                      return isNaN(date.getTime()) ? "Invalid Date" : format(date, "dd/MM/yyyy");
                    })()
                    : "N/A"}


                </div>
                <Badge
                  variant={dueInfo.urgent ? "destructive" : "secondary"}
                  className="!bg-red-600 "
                >
                  {dueInfo.text}
                </Badge>
              </div>
            </CardContent>
          </Card>
          {/* Team Members */}
          {/* <Card className="gap-3">
            <CardHeader>
              <CardTitle className="flex text-xl items-center font-semibold">
                <User className="h-7 w-7 mr-2 text-bits-warning" />
                Team Members
              </CardTitle>
            </CardHeader>
            <CardContent>
              {teamMembers.map((member: TeamMember, index: number) => (
                <div
                  key={index}
                  className="flex items-center space-x-4 space-y-4"
                >
                  <Avatar className="h-10 w-10">
                    <AvatarImage
                      src={member.avatar}
                      alt={`@${member.name}`}
                      className="rounded-full"
                    />
                  </Avatar>
                  <div>
                    <p className="font-medium">{member.name}</p>
                    <p className="text-muted-foreground">{member.email}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card> */}
          {/* TA & Instructors */}
          {(project?.teachingAssistants?.length > 0 || project?.instructors?.length > 0) && (
            <Card className="gap-3">
              <CardHeader>
                <CardTitle className="flex text-xl items-center font-semibold">
                  <Users className="h-6 w-6 mr-3 text-blue-500" />
                  TA & Instructors
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {project.teachingAssistants?.map((teacher, index) => (
                  <div key={index} className="flex flex-col">
                    <p className="font-medium">{teacher.name}</p>
                    <p className="text-muted-foreground flex items-center space-x-1">
                      <span>TA</span>
                      <span className="h-1 w-1 rounded-full bg-muted-foreground" />
                      <span>{teacher.email}</span>
                    </p>
                  </div>
                ))}
                {project?.instructors?.map((teacher, index) => (
                  <div key={index} className="flex flex-col">
                    <p className="font-medium">{teacher?.name}</p>
                    <p className="text-muted-foreground flex items-center space-x-1">
                      <span>Instructor</span>
                      <span className="h-1 w-1 rounded-full bg-muted-foreground" />
                      <span>{teacher?.email}</span>
                    </p>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

        </div>
      </div>
    </div>
  );
};

interface CheckpointsAndSubmissionsProps {
  project: EnhancedProject;
}

const CheckpointsAndSubmissions: React.FC<CheckpointsAndSubmissionsProps> = ({
  project,
}) => {

  const { user } = useAuth();
  const { navigateTo } = useNavigation();
  const { projectId } = useParams()
  const navigate = useNavigate()
  // Jupyter workspace context
  const { workspace, updateWorkspace, resetWorkspace } = useJupyterWorkspace();

  // React Query mutation for workspace creation
  const createWorkspaceMutation = useCreateWorkspaceMutation();

  // Sandbox status state
  const [sandboxStatus, setSandboxStatus] = useState<SandboxStatus>({
    status: "none",
  });

  // Track which checkpoint is using the sandbox (single sandbox at a time)
  const [activeSandboxCheckpoint, setActiveSandboxCheckpoint] = useState<
    string | null
  >(null);

  useEffect(() => {
    // Check if the workspace in the context belongs to the current project
    if (workspace.projectId === project.id && workspace.status !== "none") {
      setSandboxStatus({
        status: workspace.status
      });
      setActiveSandboxCheckpoint(null);
    } else {
      setSandboxStatus({ status: "none" });
      setActiveSandboxCheckpoint(null);
    }
  }, [
    workspace.projectId,
    workspace.status,
    workspace.instanceId,
    workspace.createdAt,
    workspace.lastActivity,
    workspace.errorMessage,
    project.id,
  ]);

  type CheckpointStatus =
    | "not_started"
    | "in_progress"
    | "submitted"
    | "graded"
    | "overdue";

  const statusStyles: Record<CheckpointStatus, { text: string; bg: string }> = {
    graded: {
      text: "text-bits-blue",
      bg: "bg-indigo-100",
    },
    submitted: {
      text: "text-bits-light-green",
      bg: "bg-green-100",
    },
    overdue: {
      text: "text-bits-red",
      bg: "bg-red-100",
    },
    in_progress: {
      text: "text-bits-orange",
      bg: "bg-orange-100",
    },
    not_started: {
      text: "text-bits-orange",
      bg: "bg-orange-100",
    },
  };

  const handleLaunchSandbox = async (userCheckpointId?: string) => {
    if (
      sandboxStatus.status === "provisioning" ||
      sandboxStatus.status === "active"
    ) {
      toast.error("A sandbox environment is already running or provisioning.");
      return;
    }

    if (!user?.name) {
      toast.error("User not found. Please log in again.");
      return;
    }

    const username = user.name;
    setActiveSandboxCheckpoint(userCheckpointId ?? null);
    setSandboxStatus({ status: "provisioning", provisioningProgress: 10 });
    toast.info("Starting sandbox provisioning...");

    try {
      // Use the project ID for workspace creation
      const projectId = project.id;

      setSandboxStatus((prev) => ({ ...prev, provisioningProgress: 30 }));
      toast.info("Creating Jupyter workspace...");

      // Call backend API to create workspace
      const workspaceResponse = await createWorkspaceMutation.mutateAsync(
        projectId
      );
      console.log("workspaceResponse", workspaceResponse);

      setSandboxStatus((prev) => ({ ...prev, provisioningProgress: 60 }));

      if (workspaceResponse.isSuccess && workspaceResponse.data.server.ready) {
        // Workspace is ready immediately
        toast.success("Jupyter workspace is ready!");
        setSandboxStatus({
          status: "active",
          instanceId:
            workspaceResponse.data.server.state.object_name ||
            `jupyter-${username}`,
          createdAt: new Date().toISOString(),
          lastActivity: new Date().toISOString(),
          autoSaveEnabled: true,
          provisioningProgress: 100,
        });
        persistentSandboxState.status = "active";
        persistentSandboxState.token = "backend-managed"; // Token is managed by backend

        // Store workspace information in context
        updateWorkspace({
          projectId: projectId,
          kernelId: workspaceResponse.data.kernel.id,
          sessionId: workspaceResponse.data.session.id,
          workspaceUrl: workspaceResponse.data.server.url,
          status: "active",
          instanceId: workspaceResponse.data.server.state.object_name,
          createdAt: workspaceResponse.data.server.started,
          lastActivity: workspaceResponse.data.kernel.last_activity,
          folderPath: workspaceResponse.data.folderPath,
          notebookPath: workspaceResponse.data.notebookPath,
          serverReady: workspaceResponse.data.server.ready,
          kernelExecutionState: workspaceResponse.data.kernel.execution_state,
        });
      } else {
        // Workspace is still provisioning, poll for status
        toast.info("Workspace is being prepared...");
        setSandboxStatus((prev) => ({ ...prev, provisioningProgress: 80 }));

        // In a real implementation, you would poll the backend for workspace status
        // For now, simulate completion after a delay
        setTimeout(() => {
          toast.success("Jupyter workspace is ready!");
          setSandboxStatus({
            status: "active",
            instanceId:
              workspaceResponse.data.server.state.object_name ||
              `jupyter-${username}`,
            createdAt: new Date().toISOString(),
            lastActivity: new Date().toISOString(),
            autoSaveEnabled: true,
            provisioningProgress: 100,
          });
          persistentSandboxState.status = "active";
          persistentSandboxState.token = "backend-managed";

          // Store workspace information in context (delayed case)
          updateWorkspace({
            projectId: projectId,
            kernelId: workspaceResponse.data.kernel.id,
            sessionId: workspaceResponse.data.session.id,
            workspaceUrl: workspaceResponse.data.server.url,
            status: "active",
            instanceId: workspaceResponse.data.server.state.object_name,
            createdAt: workspaceResponse.data.server.started,
            lastActivity: workspaceResponse.data.kernel.last_activity,
            folderPath: workspaceResponse.data.folderPath,
            notebookPath: workspaceResponse.data.notebookPath,
            serverReady: workspaceResponse.data.server.ready,
            kernelExecutionState: workspaceResponse.data.kernel.execution_state,
          });
        }, 3000);
      }
    } catch (error) {
      let errorMessage = "An unknown error occurred.";
      if (axios.isAxiosError(error)) {
        errorMessage = error.response?.data?.message || error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      console.error("Sandbox launch failed:", error);
      toast.error(`Sandbox launch failed: ${errorMessage}`);

      setSandboxStatus({ status: "error", errorMessage });
    }
  };

  const handleOpenSandbox = () => {
    if (!user?.name) {
      toast.error("Cannot open sandbox. User not found.");
      return;
    }

    if (sandboxStatus.status !== "active") {
      toast.error("Sandbox is not active. Please launch it first.");
      return;
    }

    setSandboxStatus((prev) => ({
      ...prev,
      lastActivity: new Date().toISOString(),
    }));

    // Navigate to the NotebookPage which will handle backend API integration
    //navigateTo("notebook", { projectId: project.id });

    navigate(
      `${paths.notebook.replace(":projectId", project.id)}`
    );
  };
  console.log(project)
  const handleStopSandbox = async () => {
    if (!user?.name) {
      toast.error("User not found. Cannot stop sandbox.");
      return;
    }

    setSandboxStatus((prev) => ({ ...prev, status: "stopping" }));
    toast.info("Stopping sandbox environment...");

    try {
      // TODO: Implement backend API call to stop workspace
      // For now, simulate stopping the sandbox
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast.success("Sandbox environment has been stopped.");
      setSandboxStatus({ status: "stopped" });
      setActiveSandboxCheckpoint(null);
      persistentSandboxState.status = "stopped";
      persistentSandboxState.token = null;
      resetWorkspace();
    } catch (error) {
      let errorMessage = "Failed to stop the sandbox.";
      if (axios.isAxiosError(error)) {
        errorMessage = error.response?.data?.message || error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      console.error("Failed to stop sandbox:", error);
      toast.error(errorMessage);
      // Revert status to active if stopping fails
      setSandboxStatus((prev) => ({ ...prev, status: "active" }));
    }
  };

  const handleDownloadResources = async () => {
    try {
      toast.info("Preparing download...");

      // Simulate creating zip file with all resources
      const resourceList = project?.datasets?.map((d) => d.name).join(", ");

      // In a real implementation, this would:
      // 1. Create a zip file containing all datasets
      // 2. Include project requirements, rubric as PDF
      // 3. Add any starter code/templates
      // 4. Trigger download

      setTimeout(() => {
        toast.success(`Downloaded: ${resourceList}`);

        // Create a mock download link
        const link = document.createElement("a");
        link.href =
          "data:text/plain;charset=utf-8," +
          encodeURIComponent(
            `Project Resources for: ${project.title
            }\n\nIncluded files:\n${project.datasets
              ?.map((d) => `- ${d.name} (${d.format}, ${d.size})`)
              .join(
                "\n"
              )}\n\nThis is a mock download. In production, this would be a zip file containing all project resources.`
          );
        link.download = `${project?.title?.replace(
          /[^a-zA-Z0-9]/g,
          "_"
        )}_resources.txt`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }, 1500);
    } catch (error) {
      toast.error("Failed to download resources. Please try again.");
    }
  };

  const handleDownloadDataset = async (dataset: Dataset) => {
    try {
      toast.info(`Downloading ${dataset.name}...`);

      // Simulate individual file download
      setTimeout(() => {
        toast.success(`Downloaded: ${dataset.name}`);

        // Create a mock download
        const link = document.createElement("a");
        link.href =
          "data:text/plain;charset=utf-8," +
          encodeURIComponent(
            `Mock dataset: ${dataset.name}\nDescription: ${dataset.description}\nFormat: ${dataset.format}\nSize: ${dataset.size}`
          );
        link.download = dataset.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }, 1000);
    } catch (error) {
      toast.error("Failed to download dataset. Please try again.");
    }
  };


  const [collapsedCheckpoints, setCollapsedCheckpoints] = useState<string[]>(
    defaultProject?.checkpoints?.map((cp) => cp.id)
  );

  const handleToggleCollapse = (checkpointId: string) => {
    setCollapsedCheckpoints((prevCollapsed) =>
      prevCollapsed.includes(checkpointId)
        ? prevCollapsed.filter((id) => id !== checkpointId)
        : [...prevCollapsed, checkpointId]
    );
  };

  return (
    <div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Card className="gap-4">
            <CardHeader>
              <div className="flex">
                <div className="h-6 w-8 ">
                  <Flag className=" h-6 w-6 text-indigo-500" />
                </div>
                <CardTitle className="text-xl text-bits-blue font-semibold mt-0">
                  Checkpoints
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">


              {project.checkpoints.map((checkpoint) => {
                const isCollapsed = collapsedCheckpoints.includes(checkpoint.id);
                const dueDateFormatted = checkpoint.due_date
                  ? (() => {
                    const date = parseISO(checkpoint.due_date);
                    return isNaN(date.getTime()) ? "Invalid Date" : format(date, "dd/MM/yyyy");
                  })()
                  : "N/A";

                return (
                  <Card key={checkpoint.id} className="gap-0 border rounded-lg shadow-sm">
                    <CardHeader
                      className="p-4 cursor-pointer"
                      onClick={() => handleToggleCollapse(checkpoint.id)}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <CardTitle className="text-lg text-bits-blue font-semibold">
                            {checkpoint.title}
                          </CardTitle>
                        </div>
                        <div className="flex items-center space-x-2">
                          {checkpoint.status !== "not_started" && (
                            <span
                              className={`px-2 py-1 text-sm font-medium rounded-md ${statusStyles[checkpoint.status]?.text
                                } ${statusStyles[checkpoint.status]?.bg}`}
                            >
                              {checkpoint.status.charAt(0).toUpperCase() +
                                checkpoint.status.slice(1)}
                            </span>
                          )}
                          {isCollapsed ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronUp className="h-4 w-4" />
                          )}
                        </div>
                      </div>
                    </CardHeader>

                    {!isCollapsed && (
                      <CardContent className="p-4 pt-0">
                        <p className="text-muted-foreground mb-2 mt-0">
                          {checkpoint.description}
                        </p>

                        {activeSandboxCheckpoint === checkpoint.id && (
                          <>
                            <ProvisioningProgress sandboxStatus={sandboxStatus} />
                            <ActiveSandboxCard
                              sandboxStatus={sandboxStatus}
                              onOpenSandbox={() => {
                                setSandboxStatus((prev) => ({
                                  ...prev,
                                  lastActivity: new Date().toISOString(),
                                }));
                                navigateTo("notebook");
                              }}
                            />
                          </>
                        )}
                        {checkpoint.status === "graded" ? (
                          <>
                            <div className=" p-4 rounded-md mb-4">
                              <p className="text-lg text-gray-600 font-medium">
                                <span className="text-3xl text-gray-800 bg font-bold">
                                  {checkpoint.grade}
                                </span>{" "}
                                Grade
                              </p>
                              <Card className="flex-row p-4 mt-4 border-none bg-background items-center">
                                <MessageSquare className="h-6 w-6 text-bits-warning" />
                                <div>
                                  <p className="font-semibold text-base">
                                    Instructor Feedback
                                  </p>
                                  <p className="text-muted-foreground">
                                    {checkpoint.instructorFeedback}
                                  </p>
                                </div>
                              </Card>
                            </div>
                            <div className="flex justify-between items-center text-sm text-gray-500">
                              <p className="flex items-center font-bold text-gray-800">
                                <Calendar className="h-5 w-5 mr-1 text-gray-500" />
                                {format(parseISO(checkpoint.due_date), "dd MMM yyyy")}

                              </p>
                              <div className="flex mt-2 mr-1 space-x-2 text-base font-semibold text-gray-700">
                                {/* <button className="flex items-center  px-3 py-2 border rounded-lg shadow-md">
                                  <Download className="h-4 w-4 mr-2" /> Download
                                  Dataset
                                </button> */}
                                <button className="flex items-center px-3 py-1 border rounded-lg shadow-md">
                                  <Eye className="h-4 w-4 mr-2" /> Review
                                </button>
                              </div>
                            </div>
                          </>
                        ) : (
                          <div className="flex justify-between items-center text-sm text-gray-500">
                            <p className="flex items-center font-bold text-gray-800">
                              <Calendar className="h-5 w-5 mr-1 text-gray-500" />
                              {format(parseISO(checkpoint.due_date), "dd MMM yyyy")}
                            </p>
                            <div className="flex mt-2 mr-1 space-x-2 text-base font-semibold">
                              {/* <button className="flex items-center  px-3 py-2  text-gray-700 border rounded-lg shadow-md">
                                <Download className="h-4 w-4 mr-2" /> Download
                                Dataset
                              </button> */}
                              {checkpoint.status === "in_progress" ? (
                                <div>
                                  {sandboxStatus.status === "active" ||
                                    sandboxStatus.status === "stopping" ? (
                                    <div className="flex">
                                      <Button
                                        onClick={handleOpenSandbox}
                                        className="flex items-center h-full mr-2  px-3 py-4 bg-green-600 hover:bg-green-700 border rounded-lg shadow-md"
                                      >
                                        <ExternalLink className="h-4 w-4 mr-2" />
                                        Open Jupyter Notebook
                                      </Button>
                                      <Button
                                        className="flex px-3 py-4 h-full bg-bits-error  hover:bg-red-800  border-none"
                                        onClick={handleStopSandbox}
                                        disabled={
                                          sandboxStatus.status === "stopping"
                                        }
                                      >
                                        <Power className="h-4 w-4 mr-2" />
                                        {sandboxStatus.status === "stopping"
                                          ? "Stopping..."
                                          : "Stop Sandbox"}
                                      </Button>
                                    </div>
                                  ) : (
                                    <SandboxLaunchDialog
                                      onLaunch={() =>
                                        handleLaunchSandbox(checkpoint.id)
                                      }
                                      sandboxStatus={sandboxStatus}
                                    />
                                  )}
                                </div>
                              ) : checkpoint.status === "submitted" ? (
                                <>
                                  <button className="flex items-center px-3 py-1 text-gray-700 border rounded-lg shadow-md">
                                    <Eye className="h-4 w-4 mr-2" /> Review
                                  </button>
                                  <button className="flex items-center px-3 py-2 text-gray-700 border rounded-lg shadow-md">
                                    <RotateCcw className="h-4 w-4 mr-2" />{" "}
                                    Withdraw submission
                                  </button>
                                </>
                              ) : checkpoint.status === "overdue" ? (<></>
                                // <button className="flex items-center  px-3 py-2  text-gray-700 border rounded-lg shadow-md">
                                //   <Download className="h-4 w-4 mr-2" /> Download
                                //   Dataset
                                // </button>
                              ) : null}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    )}
                  </Card>
                );
              })}

            </CardContent>
          </Card>
        </div>
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card className="gap-2">
            <CardHeader>
              <CardTitle className="flex items-center text-xl text-bits-blue font-semibold">
                <Rocket className="h-6 w-6 mr-2 text-purple-500" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {sandboxStatus.status !== "active" &&
                sandboxStatus.status !== "stopping" && (
                  <SandboxLaunchDialog
                    onLaunch={handleLaunchSandbox}
                    sandboxStatus={sandboxStatus}
                  />
                )}

              {(sandboxStatus.status === "active" ||
                sandboxStatus.status === "stopping") && (
                  <Button
                    onClick={handleOpenSandbox}
                    className="w-full h-full p-3 bg-green-600 hover:bg-green-700"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open Jupyter Notebook
                  </Button>
                )}
              {(sandboxStatus.status === "active" ||
                sandboxStatus.status === "stopping") && (
                  <Button
                    variant="bits"
                    className="w-full h-full p-3 bg-bits-error hover:bg-red-700 border-none"
                    onClick={handleStopSandbox}
                    disabled={sandboxStatus.status === "stopping"}
                  >
                    <Power className="h-4 w-4 mr-2" />
                    {sandboxStatus.status === "stopping"
                      ? "Stopping..."
                      : "Stop Sandbox"}
                  </Button>
                )}

              <Button
                variant="outline"
                className="w-full h-full p-3"
                onClick={handleDownloadResources}
              >
                <Download className="h-4 w-4 mr-2" />
                Download Datasets
              </Button>

              <Button
                variant="outline"
                className="w-full h-full p-3"
                onClick={handleDownloadResources}
              >
                <MessageSquare className="h-4 w-4 mr-1" />
                Message
              </Button>
            </CardContent>
          </Card>

          {/* Grading Rubric */}
          {/* <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-xl text-bits-blue font-semibold">
                <Award className="h-6 w-6 mr-2 text-bits-warning" />
                Your Grades
              </CardTitle>
            </CardHeader>
            <CardContent className="mt-o">
              <div className="space-y-3">
                {project?.rubrics?.map((item) => (
  <div key={item.id} className="flex justify-between items-start mb-3">
    <div className="flex-1 mr-10">
      <p className="font-medium text-bits-blue">{item.title}</p>
      <p className="flex flex-wrap text-sm text-muted-foreground mt-1">
        {item.description}
      </p>
      {item.criteria?.map((c) => (
        <div key={c.name} className="mt-1 ml-2">
          <span className="text-gray-500">{c.description}</span>
          <span className="text-sm text-muted-foreground ml-2">
            ({c.points} pts)
          </span>
        </div>
      ))}
    </div>
    <div className="flex flex-shrink-0 items-center text-sm text-muted-foreground">
      {item.earnedPoints !== undefined ? `${item.earnedPoints}/` : ""}
      {item.total_points} pts
    </div>
  </div>
))}

{project?.rubrics?.length > 0 && (
  <div className="pt-4 flex justify-end gap-8 text-muted-foreground font-semibold">
    <span>Total Score</span>
    <span className="font-semibold">
      {project.rubrics.reduce(
        (sum, item) => sum + (item.earnedPoints ?? 0),
        0
      )}
      /
      {project.rubrics.reduce(
        (sum, item) => sum + Number(item.total_points ?? 0),
        0
      )}
    </span>
  </div>
)}

              </div>
            </CardContent>
          </Card> */}
        </div>
      </div>
    </div>
  );
};

export default function ProjectDetailsPage({
}: {
  }) {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<"details" | "checkpoints">(
    "details"
  );
  const { projectId } = useParams()
  const { data, isLoading, error } = useQuery({
    queryKey: ["project", projectId],
    queryFn: () => getProjectById(projectId || ""),
    enabled: !!projectId,
  });
  const navigate = useNavigate()
  console.log(data, isLoading, error)
  // Get project data with safe fallback; cast to any to align with local mock shape
  const project: any = (data as any) || (defaultProject as any);
  console.log("here")
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "not_started":
        return <AlertCircle className="h-5 w-5" />;
      case "in_progress":
        return <Clock className="h-5 w-5" />;
      case "submitted":
        return <CheckSquare className="h-5 w-5" />;
      case "graded":
        return <CheckCircle className="h-5 w-5" />;
      default:
        return <AlertCircle className="h-5 w-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "not_started":
        return "text-muted-foreground";
      case "in_progress":
        return "text-bits-warning";
      case "submitted":
        return "text-bits-green";
      case "graded":
        return "text-bits-blue";
      default:
        return "text-muted-foreground";
    }
  };
  console.log(user?.role)
  const handlePrevious = () => {

    navigate(`/`)
  }
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-row gap-3">
        <div className="mt-1">
          <button
            onClick={handlePrevious}
            className="p-2 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft className="h-7 w-8 text-gray-700" />
          </button>
        </div>

        <div className="flex flex-col  flex-grow">
          <h1 className="text-2xl font-semibold">{project.title}</h1>
          <p className="text-muted-foreground mt-1">
            {project.subject} • {project.instructor}
          </p>
        </div>

        {/* <div className="flex items-center space-x-3">
          <div
            className={`flex bg-yellow-50 rounded-lg p-1 mr-2 items-center space-x-2 ${getStatusColor(
              project?.status
              project?.status
            )}`}
          >
            {getStatusIcon(project?.status)}
            {getStatusIcon(project?.status)}
            <span className="font-medium capitalize">
              {project?.status?.replace("_", " ")}
              {project?.status?.replace("_", " ")}
            </span>
          </div>
          {project?.grade !== undefined && (
            <Badge className="bg-green-600 text-white">
              {project?.grade}/{project?.totalPoints} pts
              {project?.grade}/{project?.totalPoints} pts
            </Badge>
          )}
        </div> */}
      </div>

      <div className="p-0 -mt-2 -mx-2">
        <Card className="mx-2 p-1 flex flex-row gap-1 rounded-full bg-gray-200">
          <button
            onClick={() => setActiveTab("details")}
            className={cn(
              "flex-1 py-2 px-4 text-sm font-medium rounded-full transition-colors  text-gray-800",
              activeTab === "details" ? "bg-white shadow" : ""
            )}
          >
            Project Details
          </button>
          <button
            onClick={() => setActiveTab("checkpoints")}
            className={cn(
              "flex-1 py-2 px-4 text-sm font-medium rounded-full transition-colors text-gray-600",
              activeTab === "checkpoints" ? "bg-white shadow" : ""
            )}
          >
            Checkpoints & Submissions
          </button>
        </Card>
      </div>

      {isLoading ? (
        <div className="p-4 text-gray-500">Fetching Project Details...</div>
      ) : error ? (
        <div className="p-4 text-red-500">Error loading project details</div>
      ) : (
        <>
          {activeTab === "details" && <ProjectDetails project={project} />}
          {activeTab === "checkpoints" && (
            <CheckpointsAndSubmissions project={project} />
          )}
        </>
      )}
    </div>
  );
}
