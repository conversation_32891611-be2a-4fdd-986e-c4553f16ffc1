import React, { useMemo, useState } from "react";
import { But<PERSON> } from "../components/ui/button";
import { Search, Upload, Download, MoreHorizontal, FileCode, ArrowLeft, Plus, Eye } from "lucide-react";
import { useNavigation } from "../App";
import { Input } from "../components/ui/input";
import { Badge } from "../components/ui/badge";
import { toast } from "sonner";

import { useUpdateContentMutation, getContents } from "../api/jupyterManagementApi";
import { useJupyterWorkspace } from "../contexts/JupyterWorkspaceContext";

type ScriptType = "Project File" | "Template" | "User Created";

interface ScriptItem {
  id: string;
  name: string;
  description?: string;
  author?: string;
  lastModified?: string;
  size?: string;
  version?: string;
  type?: ScriptType;
  tags?: string[];
  // path?: string; // often present at runtime
}

const getExt = (name?: string) => {
  if (!name) return "";
  const i = name.lastIndexOf(".");
  return i === -1 ? "" : name.slice(i + 1).toLowerCase();
};

const isPy = (name?: string) => getExt(name) === "py";

function getTypeColor(type?: ScriptType) {
  switch (type) {
    case "Project File":
      return "bg-bits-blue/10 text-bits-blue border-bits-blue";
    case "Template":
      return "bg-bits-gold/10 text-bits-gold border-bits-gold";
    case "User Created":
    default:
      return "bg-muted-foreground/10 text-muted-foreground border-muted-foreground";
  }
}

export default function PythonScriptsPage() {
  const { goBack, pageParams, navigateTo } = useNavigation();
  const initialFromParams: ScriptItem[] = (pageParams?.files as ScriptItem[]) ?? [];
  const [scripts, setScripts] = useState<ScriptItem[]>(
    initialFromParams.filter((s) => isPy(s?.name))
  );
  const { workspace } = useJupyterWorkspace();

  const updateContentMutation = useUpdateContentMutation();

  const [searchTerm, setSearchTerm] = useState("");

  const filteredScripts = useMemo(() => {
    const q = searchTerm.trim().toLowerCase();
    const base = scripts.filter(s => isPy(s?.name));
    if (!q) return scripts;
    return base.filter(
      (s) =>
        s.name.toLowerCase().includes(q) ||
        (s.description ?? "").toLowerCase().includes(q) ||
        (s.tags ?? []).some((t) => t.toLowerCase().includes(q))
    );
  }, [scripts, searchTerm]);

  const buildTargetPath = (filename: string) => {
    const basePath = workspace?.folderPath;
    return `${basePath}/${filename}`;
  };

  const handleUpload = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".py";
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        const filePath = buildTargetPath(file.name);
        const content = await file.text();

        const res: any = await updateContentMutation.mutateAsync({
          path: filePath,
          content,
          type: "file",
          format: "text",
        });

        const added: ScriptItem = {
          id: res.id ?? res.path ?? filePath,
          name: res.name ?? file.name,
          description: res.description ?? "",
          author: res.author ?? "",
          lastModified:
            res.last_modified ?? res.lastModified ?? new Date().toISOString(),
          size:
            typeof res.size === "number" ? `${res.size}` : res.size ?? `${content.length}`,
          version: res.version ?? "",
          type: "User Created",
          tags: res.tags ?? [],
          // @ts-ignore
          path: res.path ?? filePath,
        };

        setScripts((prev) => {
          const exists = prev.some((s) => s.id === added.id || s.name === added.name);
          return exists ? prev : [added, ...prev];
        });

        toast.success(`Uploaded ${added.name}`);
      } catch (err) {
        console.error(err);
        toast.error("Upload failed");
      }
    };
    input.click();
  };

  const handleDownload = async (script: ScriptItem) => {
    try {
      const filePath = (script as any).path ?? buildTargetPath(script.name);
      const fileModel = await getContents(filePath);

      const contentText =
        typeof fileModel?.content === "string" ? fileModel.content : "";

      if (!contentText) {
        toast.error("No content available to download.");
        return;
      }

      const blob = new Blob([contentText], {
        type: "text/plain;charset=utf-8;",
      });
      const url = URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        script.name.endsWith(".py") ? script.name : `${script.name}.py`
      );
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);

      toast.success(`Downloading ${script.name}`);
    } catch (err) {
      console.error("Download failed:", err);
      toast.error("Download failed");
    }
  };

  const handleDuplicate = async (script: ScriptItem) => {
    try {
      const resolvePath = (name: string) => {
        const basePath = workspace?.folderPath || "";
        return `${basePath}/${name}`;
      };
      const sourcePath = (script as any).path ?? resolvePath(script.name);

      const sourceModel = await getContents(sourcePath);
      const sourceText =
        typeof sourceModel?.content === "string" ? sourceModel.content : "";

      if (!sourceText) {
        toast.error("No content available to duplicate.");
        return;
      }

      // Generate unique -copy name in the same folder
      const existingNames = new Set(scripts.map((s) => s.name.toLowerCase()));
      const parts = script.name.split(".");
      const ext = parts.length > 1 ? `.${parts.pop()}` : ".py";
      const base = parts.join(".");
      const mkName = (idx: number | null) =>
        idx === null ? `${base}-copy${ext}` : `${base}-copy-${idx}${ext}`;

      let candidate = mkName(null);
      let idx = 1;
      while (existingNames.has(candidate.toLowerCase())) {
        candidate = mkName(idx++);
      }

      const targetPath = resolvePath(candidate);

      const res: any = await updateContentMutation.mutateAsync({
        path: targetPath,
        content: sourceText,
        type: "file",
        format: "text",
      });

      const added: ScriptItem = {
        id: res.id ?? res.path ?? targetPath,
        name: res.name ?? candidate,
        description: res.description ?? "",
        author: res.author ?? "",
        lastModified:
          res.last_modified ?? res.lastModified ?? new Date().toISOString(),
        size:
          typeof res.size === "number" ? `${res.size}` : res.size ?? script.size ?? "",
        version: res.version ?? "",
        type: script.type ?? "User Created",
        tags: res.tags ?? script.tags ?? [],
        // @ts-ignore
        path: res.path ?? targetPath,
      };

      setScripts((prev) => {
        const exists = prev.some((s) => s.id === added.id || s.name === added.name);
        return exists ? prev : [added, ...prev];
      });

      toast.success(`Duplicated ${script.name} → ${added.name}`);
    } catch (err: any) {
      console.error("Duplicate failed:", err);
      toast.error(err?.response?.data?.message || "Duplicate failed");
    }
  };

  const handleCreateNew = async () => {
    try {
      const basePath = workspace?.folderPath || "";
      const existingNames = new Set(scripts.map((s) => s.name.toLowerCase()));
      const base = "Untitled";
      const ext = ".py";
      const mkName = (i: number) => (i === 0 ? `${base}${ext}` : `${base}-${i}${ext}`);

      let i = 0;
      let filename = mkName(i);
      while (existingNames.has(filename.toLowerCase())) {
        i += 1;
        filename = mkName(i);
      }

      const filePath = `${basePath}/${filename}`;

      const starter = "# Python script\n";

      const res: any = await updateContentMutation.mutateAsync({
        path: filePath,
        content: starter,
        type: "file",
        format: "text",
      });

      const added: ScriptItem = {
        id: res.id ?? res.path ?? filePath,
        name: res.name ?? filename,
        description: res.description ?? "",
        author: res.author ?? "",
        lastModified:
          res.last_modified ?? res.lastModified ?? new Date().toISOString(),
        size: typeof res.size === "number" ? `${res.size}` : res.size ?? `${starter.length}`,
        version: res.version ?? "",
        type: "User Created",
        tags: res.tags ?? [],
        // @ts-ignore
        path: res.path ?? filePath,
      };

      setScripts((prev) => {
        const exists = prev.some((s) => s.id === added.id || s.name === added.name);
        return exists ? prev : [added, ...prev];
      });

      toast.success(`Created ${added.name}`);
    } catch (err: any) {
      console.error("Create new script failed:", err);
      toast.error(err?.response?.data?.message || "Failed to create script");
    }
  };

  const handleView = (script: ScriptItem) => {
    navigateTo("notebook", { file: script });
  };

  return (
    <div className="p-4 mt-4 space-y-2  bg-white">
      <div className="flex items-center">
        <Button variant="destructive" className="p-4" onClick={goBack}>
          <ArrowLeft className="!h-6 !w-8 text-gray-700" />
        </Button>
        <h2 className="text-xl font-semibold flex items-center gap-2">
          Python Scripts Management
        </h2>
        <div className="ml-auto flex items-center gap-2">
          <Button onClick={handleUpload} variant="secondary"  className="mt-4">
            <Upload className="h-4 w-4" />
            Upload Script
          </Button>
          <Button onClick={handleCreateNew} variant="secondary"  className="mt-4">
            <Plus className=" h-4 w-4" />
            New Script
          </Button>
        </div>
      </div>

      <p className="text-muted-foreground">
        Manage Python scripts, upload new files, create new files, or duplicate existing ones
      </p>

      <div className="relative">
        <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search scripts..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-9"
        />
      </div>

      <div className="grid gap-3">
        {filteredScripts.map((s) => (
          <div key={s.id} className="border rounded-lg p-3 bg-background">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {/* <span className={`border px-2 py-0.5 rounded text-xs ${getTypeColor(s.type)}`}>
                  {s.type ?? "User Created"}
                </span> */}
                <span className="font-medium">{s.name}</span>
                {/* <span className="text-muted-foreground">· {s.version ?? "v1"}</span> */}
              </div>
              <div className="flex items-center gap-2">
                <Button size="sm"  variant="outline" onClick={() => handleView(s)}>
                  <Eye className="h-4 w-4" /> 
                  Open
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleDownload(s)}>
                  Download
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleDuplicate(s)}>
                  Duplicate
                </Button>
              </div>
            </div>

            <div className="mt-1 text-sm text-muted-foreground">{s.description ?? ""}</div>

            <div className="mt-2 flex items-center gap-3 text-xs text-muted-foreground">
              {/* <span>👤 {s.author ?? ""}</span>
              <span>⏰ {s.lastModified ?? ""}</span> */}
              <span>📁 {s.size ?? ""}kb</span>
            </div>

            <div className="mt-2 flex gap-2">
              {(s.tags ?? []).map((tag) => (
                <Badge key={tag} variant="outline">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        ))}

        {filteredScripts.length === 0 && (
          <div className="text-sm text-muted-foreground">No scripts match the current search.</div>
        )}
      </div>
    </div>
  );
}
