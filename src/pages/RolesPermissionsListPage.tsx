import { <PERSON>, Eye } from "lucide-react";

import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import { EditPermissionsModal } from "../components/EditPermissionsModal";
import { useState } from "react";
import { ViewPermissionsModal } from "../components/ViewPermissionsModal";

export interface Roles {
  id: string;
  roleName: string;
  numUsers: number;
  status: "active" | "inactive";
  createdOn: string;
  lastUpdatedOn: string;
  permissions: Permission[];
}

export interface BasicPermission {
  isChecked: boolean;
  name: string;
}

export interface Permission {
  id: number;
  isChecked: boolean;
  name: string;
  nestedPermissions: BasicPermission[] | null;
}

const getStatusIconColor = (status: string) => {
  switch (status) {
    case "active":
      return "bg-bits-completed text-bits-green";
    default:
      return "bg-muted text-muted-foreground";
  }
};

export default function RolesPermissionsListPage() {
  const initialRoles: Roles[] = [
    {
      id: "1",
      roleName: "Admin",
      numUsers: 3,
      status: "active",
      createdOn: "12 Jun 2025",
      lastUpdatedOn: "14 Aug 2025",
      permissions: [
        {
          id: 1,
          isChecked: true,
          name: "Can view user details",
          nestedPermissions: [],
        },
        {
          id: 2,
          isChecked: true,
          name: "View courses and project details",
          nestedPermissions: [],
        },
        {
          id: 3,
          isChecked: true,
          name: "Access to platform gradebook",
          nestedPermissions: [],
        },
        {
          id: 4,
          isChecked: true,
          name: "Project Management",
          nestedPermissions: [
            { isChecked: true, name: "Create & Edit projects" },
            { isChecked: true, name: "Delete projects" },
            { isChecked: true, name: "Publish/Unpublish projects" },
            { isChecked: true, name: "Create new project template" },
          ],
        },
        {
          id: 5,
          isChecked: true,
          name: "Reports and analytics",
          nestedPermissions: [
            { isChecked: false, name: "View reports" },
            { isChecked: false, name: "Export reports" },
          ],
        },
        {
          id: 6,
          isChecked: false,
          name: "Export audit logs",
          nestedPermissions: [],
        },
        {
          id: 7,
          isChecked: false,
          name: "Configure system settings",
          nestedPermissions: [],
        },
        {
          id: 8,
          isChecked: true,
          name: "Can send new announcements",
          nestedPermissions: [],
        },
        {
          id: 9,
          isChecked: true,
          name: "Can send message",
          nestedPermissions: [],
        },
      ],
    },
    {
      id: "2",
      roleName: "TA",
      numUsers: 13,
      status: "active",
      createdOn: "1 Aug 2025",
      lastUpdatedOn: "11 Aug 2025",
      permissions: [
        {
          id: 10,
          isChecked: true,
          name: "Access Sandbox Environment",
          nestedPermissions: [],
        },
        {
          id: 11,
          isChecked: true,
          name: "Enable Submission",
          nestedPermissions: [],
        },
        {
          id: 12,
          isChecked: true,
          name: "Download datasets",
          nestedPermissions: [],
        },
        {
          id: 13,
          isChecked: true,
          name: "Access to gradebook",
          nestedPermissions: [],
        },
        {
          id: 14,
          isChecked: true,
          name: "Send messages to instructor",
          nestedPermissions: [],
        },
        {
          id: 15,
          isChecked: true,
          name: "Send messages to peers",
          nestedPermissions: [],
        },
      ],
    },
    {
      id: "3",
      roleName: "Instructor",
      numUsers: 5,
      status: "active",
      createdOn: "12 Jul 2025",
      lastUpdatedOn: "24 Aug 2025",
      permissions: [
        {
          id: 16,
          isChecked: true,
          name: "Project Management",
          nestedPermissions: [
            { isChecked: true, name: "Create & Edit projects" },
            { isChecked: true, name: "Delete projects" },
            { isChecked: true, name: "Publish/Unpublish projects" },
            { isChecked: true, name: "Create new project template" },
          ],
        },
        {
          id: 17,
          isChecked: true,
          name: "Grade and provide feedback on submissions",
          nestedPermissions: [],
        },
        {
          id: 18,
          isChecked: true,
          name: "Can send new announcements",
          nestedPermissions: [],
        },
        {
          id: 19,
          isChecked: true,
          name: "Can send message",
          nestedPermissions: [],
        },
      ],
    },
    {
      id: "4",
      roleName: "Student",
      numUsers: 3,
      status: "active",
      createdOn: "25 Jun 2025",
      lastUpdatedOn: "16 Aug 2025",
      permissions: [
        {
          id: 20,
          isChecked: true,
          name: "Access Sandbox Environment",
          nestedPermissions: [],
        },
        {
          id: 21,
          isChecked: true,
          name: "Enable Submission",
          nestedPermissions: [],
        },
        {
          id: 22,
          isChecked: true,
          name: "Download datasets",
          nestedPermissions: [],
        },
        {
          id: 23,
          isChecked: true,
          name: "Access to gradebook",
          nestedPermissions: [],
        },
        {
          id: 24,
          isChecked: true,
          name: "Send messages to instructor",
          nestedPermissions: [],
        },
        {
          id: 25,
          isChecked: true,
          name: "Send messages to peers",
          nestedPermissions: [],
        },
      ],
    },
    {
      id: "5",
      roleName: "SA",
      numUsers: 13,
      status: "active",
      createdOn: "1 Aug 2025",
      lastUpdatedOn: "11 Aug 2025",
      permissions: [
        {
          id: 10,
          isChecked: true,
          name: "Access Sandbox Environment",
          nestedPermissions: [],
        },
        {
          id: 11,
          isChecked: true,
          name: "Enable Submission",
          nestedPermissions: [],
        },
        {
          id: 12,
          isChecked: true,
          name: "Download datasets",
          nestedPermissions: [],
        },
        {
          id: 13,
          isChecked: true,
          name: "Access to gradebook",
          nestedPermissions: [],
        },
        {
          id: 14,
          isChecked: true,
          name: "Send messages to instructor",
          nestedPermissions: [],
        },
        {
          id: 15,
          isChecked: true,
          name: "Send messages to peers",
          nestedPermissions: [],
        },
      ],
    },
  ];

  const [roles, setRoles] = useState<Roles[]>(initialRoles);
  const [selectedRole, setSelectedRole] = useState<Roles | null>(null);
  const [open, setOpen] = useState(false);

  const handleSavePermissions = (updatedRole: Roles) => {
    setRoles((prev) =>
      prev.map((r) => (r.id === updatedRole.id ? updatedRole : r))
    );
  };

  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-semibold text-gray-900 mb-4 mt-4">
            Roles
          </h1>
        </div>
      </div>

      <div className="flex flex-row mb-4 mt-4">
        <div>
          <span className="text-xl font-medium">Roles and Permission</span>
        </div>

        <div className="w-60 text-bits-grey-600 ml-auto">
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="mt-1 bg-bits-grey-100">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <Table>
        <TableHeader>
          <TableRow className="text-sm font-normal text-bits-grey">
            <TableHead>Role</TableHead>
            <TableHead>Assigned Users</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Created On</TableHead>
            <TableHead>Last Updated On</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {roles.map((role) => (
            <TableRow key={role.id}>
              <TableCell>
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-bits-grey-600">
                    {role.roleName}
                  </span>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex flex-col">
                  <span className="text-md font-normal text-bits-grey-600">
                    {role.numUsers}
                  </span>
                </div>
              </TableCell>

              <TableCell>
                <Badge className={getStatusIconColor(role.status)}>
                  {role.status}
                </Badge>
              </TableCell>

              <TableCell className="text-sm font-medium text-bits-grey-600">
                {role.createdOn}
              </TableCell>

              <TableCell className="text-sm font-medium text-bits-grey-600">
                {role.lastUpdatedOn}
              </TableCell>

              <TableCell className="space-x-2">
                {/* Eye opens View modal */}
                <ViewPermissionsModal
                  role={role}
                  onSave={handleSavePermissions}
                  trigger={
                    <Button>
                      <Eye className="h-5 w-5 cursor-pointer text-bits-grey-700 hover:text-blue-700 transition-colors" />
                    </Button>
                  }
                />
                {/* Edit opens Edit modal */}
                {role.roleName !== "SA" && (
                  <EditPermissionsModal
                    role={role}
                    onSave={handleSavePermissions}
                    trigger={
                      <Button>
                        <Edit className="h-5 w-5 cursor-pointer text-bits-grey-700 hover:text-blue-700 transition-colors" />
                      </Button>
                    }
                  />
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
