import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import { Progress } from "../components/ui/progress";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from "recharts";
import {
  Activity,
  TrendingUp,
  TrendingDown,
  Users,
  Clock,
  Server,
  Zap,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Pie<PERSON>hart as PieChartIcon,
  Monitor,
  Download,
  RefreshCw,
  Calendar,
  Filter,
} from "lucide-react";

interface UsageMetrics {
  totalSessions: number;
  activeSessions: number;
  averageSessionDuration: number;
  successRate: number;
  resourceUtilization: {
    cpu: number;
    memory: number;
    storage: number;
  };
  peakConcurrency: number;
  costOptimization: number;
}

interface TimeSeriesData {
  timestamp: string;
  sessions: number;
  failures: number;
  cpuUsage: number;
  memoryUsage: number;
  responseTime: number;
}

interface UserUsageData {
  userId: string;
  userName: string;
  role: string;
  sessionsCount: number;
  totalDuration: number;
  avgDuration: number;
  successRate: number;
  lastActive: Date;
}

interface FailureAnalysis {
  type: string;
  count: number;
  percentage: number;
  trend: "up" | "down" | "stable";
}

const COLORS = [
  "#2B2B88",
  "#B78A2D",
  "#CF2027",
  "#5CCAE8",
  "#FFE275",
  "#8884d8",
  "#82ca9d",
];

const mockTimeSeriesData: TimeSeriesData[] = Array.from(
  { length: 24 },
  (_, i) => ({
    timestamp: `${String(i).padStart(2, "0")}:00`,
    sessions: Math.floor(Math.random() * 50) + 10,
    failures: Math.floor(Math.random() * 5),
    cpuUsage: Math.random() * 80 + 20,
    memoryUsage: Math.random() * 70 + 30,
    responseTime: Math.random() * 2000 + 500,
  })
);

const mockUserData: UserUsageData[] = [
  {
    userId: "1",
    userName: "Alice Chen",
    role: "Student",
    sessionsCount: 45,
    totalDuration: 18000,
    avgDuration: 400,
    successRate: 92,
    lastActive: new Date(Date.now() - 3600000),
  },
  {
    userId: "2",
    userName: "Bob Smith",
    role: "Student",
    sessionsCount: 38,
    totalDuration: 15200,
    avgDuration: 380,
    successRate: 88,
    lastActive: new Date(Date.now() - 7200000),
  },
  {
    userId: "3",
    userName: "Dr. Johnson",
    role: "Instructor",
    sessionsCount: 28,
    totalDuration: 14000,
    avgDuration: 500,
    successRate: 96,
    lastActive: new Date(Date.now() - 1800000),
  },
  {
    userId: "4",
    userName: "Carol Davis",
    role: "Student",
    sessionsCount: 52,
    totalDuration: 20800,
    avgDuration: 350,
    successRate: 85,
    lastActive: new Date(Date.now() - 5400000),
  },
  {
    userId: "5",
    userName: "Admin User",
    role: "Admin",
    sessionsCount: 15,
    totalDuration: 9000,
    avgDuration: 600,
    successRate: 98,
    lastActive: new Date(Date.now() - 900000),
  },
];

const mockFailureData: FailureAnalysis[] = [
  { type: "Resource Exhaustion", count: 23, percentage: 35, trend: "down" },
  { type: "Network Timeout", count: 18, percentage: 28, trend: "stable" },
  { type: "Container Start Failed", count: 12, percentage: 18, trend: "up" },
  { type: "Quota Exceeded", count: 8, percentage: 12, trend: "down" },
  { type: "Dependency Error", count: 5, percentage: 7, trend: "stable" },
];

export default function SandboxAnalyticsDashboard() {
  const [timeRange, setTimeRange] = useState("24h");
  const [selectedMetric, setSelectedMetric] = useState("sessions");
  const [metrics, setMetrics] = useState<UsageMetrics>({
    totalSessions: 0,
    activeSessions: 0,
    averageSessionDuration: 0,
    successRate: 0,
    resourceUtilization: { cpu: 0, memory: 0, storage: 0 },
    peakConcurrency: 0,
    costOptimization: 0,
  });
  const [loading, setLoading] = useState(false);

  // Simulate real-time data updates
  useEffect(() => {
    const updateMetrics = () => {
      setMetrics({
        totalSessions: Math.floor(Math.random() * 500) + 200,
        activeSessions: Math.floor(Math.random() * 50) + 10,
        averageSessionDuration: Math.floor(Math.random() * 600) + 300,
        successRate: Math.random() * 10 + 85,
        resourceUtilization: {
          cpu: Math.random() * 40 + 30,
          memory: Math.random() * 50 + 25,
          storage: Math.random() * 30 + 20,
        },
        peakConcurrency: Math.floor(Math.random() * 80) + 40,
        costOptimization: Math.random() * 30 + 15,
      });
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [timeRange]);

  const refreshData = async () => {
    setLoading(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));
    setLoading(false);
  };

  const exportData = () => {
    const data = {
      metrics,
      timeSeriesData: mockTimeSeriesData,
      userData: mockUserData,
      failureAnalysis: mockFailureData,
      exportedAt: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `sandbox-analytics-${
      new Date().toISOString().split("T")[0]
    }.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  };

  const getTrendIcon = (trend: "up" | "down" | "stable") => {
    switch (trend) {
      case "up":
        return <TrendingUp className="w-4 h-4 text-red-500" />;
      case "down":
        return <TrendingDown className="w-4 h-4 text-green-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6 mt-8 ml-8">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Sandbox Analytics
          </h1>
          <p className="text-gray-600">
            Monitor sandbox usage, performance, and optimization opportunities
          </p>
        </div>

        <div className="flex flex-wrap gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">Last Hour</SelectItem>
              <SelectItem value="24h">Last 24 Hours</SelectItem>
              <SelectItem value="7d">Last 7 Days</SelectItem>
              <SelectItem value="30d">Last 30 Days</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="sm"
            onClick={refreshData}
            disabled={loading}
          >
            <RefreshCw
              className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>

          <Button variant="outline" size="sm" onClick={exportData}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Sessions
            </CardTitle>
            <Activity className="w-4 h-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.totalSessions.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {metrics.activeSessions} currently active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="w-4 h-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.successRate.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">Launch success rate</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
            <Clock className="w-4 h-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDuration(metrics.averageSessionDuration)}
            </div>
            <p className="text-xs text-muted-foreground">Per session</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Peak Concurrency
            </CardTitle>
            <Users className="w-4 h-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.peakConcurrency}</div>
            <p className="text-xs text-muted-foreground">Simultaneous users</p>
          </CardContent>
        </Card>
      </div>

      {/* Resource Utilization */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="w-5 h-5" />
            Resource Utilization
          </CardTitle>
          <CardDescription>
            Current system resource usage across all sandbox instances
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">CPU Usage</span>
                <span className="text-sm text-gray-600">
                  {metrics.resourceUtilization.cpu.toFixed(1)}%
                </span>
              </div>
              <Progress
                value={metrics.resourceUtilization.cpu}
                className="h-2"
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Memory Usage</span>
                <span className="text-sm text-gray-600">
                  {metrics.resourceUtilization.memory.toFixed(1)}%
                </span>
              </div>
              <Progress
                value={metrics.resourceUtilization.memory}
                className="h-2"
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Storage Usage</span>
                <span className="text-sm text-gray-600">
                  {metrics.resourceUtilization.storage.toFixed(1)}%
                </span>
              </div>
              <Progress
                value={metrics.resourceUtilization.storage}
                className="h-2"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Session Activity
            </CardTitle>
            <CardDescription>
              Sandbox sessions and failures over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={mockTimeSeriesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="timestamp" />
                <YAxis />
                <Tooltip />
                <Area
                  type="monotone"
                  dataKey="sessions"
                  stackId="1"
                  stroke="#2B2B88"
                  fill="#2B2B88"
                  fillOpacity={0.8}
                />
                <Area
                  type="monotone"
                  dataKey="failures"
                  stackId="2"
                  stroke="#CF2027"
                  fill="#CF2027"
                  fillOpacity={0.8}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="w-5 h-5" />
              Resource Trends
            </CardTitle>
            <CardDescription>CPU and memory usage patterns</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={mockTimeSeriesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="timestamp" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="cpuUsage"
                  stroke="#B78A2D"
                  strokeWidth={2}
                />
                <Line
                  type="monotone"
                  dataKey="memoryUsage"
                  stroke="#5CCAE8"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* User Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Top Users
          </CardTitle>
          <CardDescription>
            Most active sandbox users and their usage patterns
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockUserData.map((user) => (
              <div
                key={user.userId}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                    <Users className="w-4 h-4 text-gray-600" />
                  </div>
                  <div>
                    <div className="font-medium">{user.userName}</div>
                    <div className="text-sm text-gray-600">{user.role}</div>
                  </div>
                </div>

                <div className="text-right space-y-1">
                  <div className="flex items-center gap-4 text-sm">
                    <span>{user.sessionsCount} sessions</span>
                    <span>{formatDuration(user.totalDuration)}</span>
                    <Badge
                      variant={
                        user.successRate > 90
                          ? "default"
                          : user.successRate > 80
                          ? "secondary"
                          : "destructive"
                      }
                      className="text-xs"
                    >
                      {user.successRate.toFixed(0)}% success
                    </Badge>
                  </div>
                  <div className="text-xs text-gray-500">
                    Last active: {user.lastActive.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Failure Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5" />
              Failure Analysis
            </CardTitle>
            <CardDescription>
              Common failure types and their frequency
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockFailureData.map((failure, index) => (
                <div
                  key={failure.type}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    {getTrendIcon(failure.trend)}
                    <div>
                      <div className="font-medium text-sm">{failure.type}</div>
                      <div className="text-xs text-gray-600">
                        {failure.count} occurrences
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-sm">
                      {failure.percentage}%
                    </div>
                    <div className="text-xs text-gray-500">of failures</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChartIcon className="w-5 h-5" />
              Failure Distribution
            </CardTitle>
            <CardDescription>Visual breakdown of failure types</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={mockFailureData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ percentage }) => `${percentage}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="percentage"
                >
                  {mockFailureData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Cost Optimization Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            Optimization Insights
          </CardTitle>
          <CardDescription>
            Recommendations for improving efficiency and reducing costs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border border-green-200 rounded-lg bg-green-50">
              <div className="flex items-center justify-between mb-2">
                <TrendingDown className="w-5 h-5 text-green-600" />
                <Badge
                  variant="outline"
                  className="text-green-700 border-green-300"
                >
                  Good
                </Badge>
              </div>
              <h4 className="font-medium text-green-900">
                Resource Efficiency
              </h4>
              <p className="text-sm text-green-700 mt-1">
                Current utilization is optimal. Consider scaling down during
                off-peak hours.
              </p>
            </div>

            <div className="p-4 border border-orange-200 rounded-lg bg-orange-50">
              <div className="flex items-center justify-between mb-2">
                <AlertTriangle className="w-5 h-5 text-orange-600" />
                <Badge
                  variant="outline"
                  className="text-orange-700 border-orange-300"
                >
                  Attention
                </Badge>
              </div>
              <h4 className="font-medium text-orange-900">Idle Sessions</h4>
              <p className="text-sm text-orange-700 mt-1">
                15% of sessions are idle for &gt;30 minutes. Implement
                auto-timeout.
              </p>
            </div>

            <div className="p-4 border border-blue-200 rounded-lg bg-blue-50">
              <div className="flex items-center justify-between mb-2">
                <TrendingUp className="w-5 h-5 text-blue-600" />
                <Badge
                  variant="outline"
                  className="text-blue-700 border-blue-300"
                >
                  Opportunity
                </Badge>
              </div>
              <h4 className="font-medium text-blue-900">Peak Usage</h4>
              <p className="text-sm text-blue-700 mt-1">
                Consider load balancing during 2-4 PM peak hours.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
