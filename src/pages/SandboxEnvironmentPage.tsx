import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "../components/ui/tabs";
import { Progress } from "../components/ui/progress";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { Textarea } from "../components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../components/ui/dialog";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "../components/ui/sheet";
import { useAuth, useNavigation } from "../App";
//import useAuth from "../components/Context/AuthContext";
//import useNavigation from "../components/Context/NavigationContext";
import SandboxLaunchPanel from "../components/SandboxLaunchPanel";
import ActiveSessionBanner from "../components/ActiveSessionBanner";
import SandboxFileBrowser from "../components/SandboxFileBrowser";
import SandboxSessionsList from "../components/SandboxSessionsList";
import EmbeddedNotebook from "../components/EmbeddedNotebook";
import {
  Play,
  Square,
  RotateCcw,
  Download,
  Upload,
  Terminal,
  Code,
  FileText,
  Share,
  Clock,
  User,
  BookOpen,
  GraduationCap,
  MessageSquare,
  File,
  Database,
  Eye,
  X,
  ChevronLeft,
  ChevronRight,
  TestTube,
  Info,
  History,
  Layers,
  Calendar,
  Users,
  Save,
} from "lucide-react";
import { toast } from "sonner";

interface SandboxEnvironmentPageProps {
  mode?: "student" | "grading" | "student-notebook" | "sandbox";
  studentId?: string;
  studentName?: string;
  assignmentId?: string;
  assignmentTitle?: string;
  notebookUrl?: string;
  initialTab?: string;
}

interface NotebookCell {
  id: string;
  type: "code" | "markdown" | "output";
  content: string;
  executionCount?: number;
}

// Rubric Interface
interface RubricItem {
  id: string;
  criteria: string;
  description: string;
  points: number;
  earnedPoints?: number;
}

interface Assignment {
  id: string;
  title: string;
  course: string;
  dueDate: string;
  totalStudents: number;
  description: string;
  requirements: string[];
  rubric: RubricItem[];
  datasets: {
    name: string;
    description: string;
    format: string;
  }[];
}

interface SubmissionFile {
  name: string;
  size: string;
  type: string;
  description: string;
  content?: any;
  cells?: NotebookCell[];
}

interface PreviousSubmission {
  version: number;
  submittedAt: string;
  status: "submitted" | "graded" | "late";
  grade?: string;
  files: SubmissionFile[];
}

// Mock assignment data
const mockProject: Assignment = {
  id: "1",
  title: "Linear Regression Analysis",
  course: "Data Science 101",
  dueDate: "2025-01-15T23:59:00Z",
  totalStudents: 30,
  description:
    "Build a comprehensive linear regression model to predict housing prices using multiple features. This project focuses on data preprocessing, feature engineering, model implementation, and evaluation.",
  requirements: [
    "Implement data preprocessing and cleaning",
    "Perform exploratory data analysis with visualizations",
    "Build and train a linear regression model",
    "Evaluate model performance using appropriate metrics",
    "Create meaningful visualizations of results",
    "Document findings and provide insights",
  ],
  datasets: [
    {
      name: "housing_data.csv",
      description: "Main dataset with housing features and prices",
      format: "CSV with 1000 rows, 5 columns",
    },
    {
      name: "supplementary_data.xlsx",
      description: "Additional market data and statistics",
      format: "Excel workbook with multiple sheets",
    },
  ],
  rubric: [
    {
      id: "lr_r1",
      criteria: "Data Exploration & Visualization",
      points: 20,
      description:
        "Comprehensive exploration of the dataset with meaningful visualizations",
    },
    {
      id: "lr_r2",
      criteria: "Data Preprocessing",
      points: 20,
      description:
        "Proper handling of missing values, outliers, and data transformation",
    },
    {
      id: "lr_r3",
      criteria: "Feature Engineering",
      points: 25,
      description: "Creation of meaningful features and feature selection",
    },
    {
      id: "lr_r4",
      criteria: "Model Implementation",
      points: 25,
      description:
        "Implementation of multiple regression models with proper validation",
    },
    {
      id: "lr_r5",
      criteria: "Analysis & Interpretation",
      points: 10,
      description: "Clear interpretation of results and model performance",
    },
  ],
};

// Mock notebook cells
const mockNotebookCells: NotebookCell[] = [
  {
    id: "1",
    type: "markdown",
    content: `# Linear Regression Analysis
## Assignment: Linear Regression Analysis
**Student ID:** 2023001
**Submission Date:** 1/12/2025

This notebook contains my implementation of linear regression analysis for the housing price prediction task.`,
  },
  {
    id: "2",
    type: "code",
    content: `# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score

print("📊 Data Science Libraries Imported Successfully")`,
    executionCount: 1,
  },
  {
    id: "3",
    type: "output",
    content: "📊 Data Science Libraries Imported Successfully",
  },
  {
    id: "4",
    type: "code",
    content: `# Load and explore the dataset
data = pd.read_csv('housing_data.csv')
print(f"Dataset shape: {data.shape}")
print("\\nFirst few rows:")
data.head()`,
    executionCount: 2,
  },
  {
    id: "5",
    type: "output",
    content: `Dataset shape: (1000, 5)

First few rows:
     sqft  bedrooms  bathrooms   age      price
0    1200       3          2    15   250000.0
1    1500       4          3     8   320000.0
2     800       2          1    25   180000.0
3    2000       5          4     3   450000.0
4    1100       3          2    20   230000.0`,
  },
  {
    id: "6",
    type: "code",
    content: `# Data preprocessing and feature engineering
# Check for missing values
print("Missing values:")
print(data.isnull().sum())

# Basic statistics
print("\\nBasic Statistics:")
data.describe()`,
    executionCount: 3,
  },
  {
    id: "7",
    type: "output",
    content: `Missing values:
sqft         0
bedrooms     0
bathrooms    0
age          0
price        0
dtype: int64

Basic Statistics:
              sqft    bedrooms   bathrooms         age         price
count  1000.000000  1000.00000  1000.000000  1000.000000   1000.000000
mean   1456.320000     3.21000     2.340000    14.560000  287450.000000
std     398.276543     1.12345     0.876543     8.234567   89234.567890
min     600.000000     1.00000     1.000000     1.000000  120000.000000
25%    1200.000000     3.00000     2.000000     8.000000  220000.000000
50%    1450.000000     3.00000     2.000000    15.000000  285000.000000
75%    1700.000000     4.00000     3.000000    21.000000  350000.000000
max    2500.000000     6.00000     4.000000    30.000000  580000.000000`,
  },
  {
    id: "8",
    type: "code",
    content: `# Split features and target
X = data[['sqft', 'bedrooms', 'bathrooms', 'age']]
y = data['price']

# Split the data
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

print(f"Training set size: {X_train.shape[0]}")
print(f"Test set size: {X_test.shape[0]}")`,
    executionCount: 4,
  },
  {
    id: "9",
    type: "output",
    content: `Training set size: 800
Test set size: 200`,
  },
  {
    id: "10",
    type: "code",
    content: `# Train the linear regression model
model = LinearRegression()
model.fit(X_train, y_train)

# Make predictions
y_pred = model.predict(X_test)

# Calculate performance metrics
mse = mean_squared_error(y_test, y_pred)
r2 = r2_score(y_test, y_pred)

print(f"Mean Squared Error: {mse:.2f}")
print(f"R² Score: {r2:.4f}")

# Feature importance (coefficients)
feature_importance = pd.DataFrame({
    'feature': X.columns,
    'coefficient': model.coef_
})
print("\\nFeature Importance:")
print(feature_importance.sort_values('coefficient', key=abs, ascending=False))`,
    executionCount: 5,
  },
  {
    id: "11",
    type: "output",
    content: `Mean Squared Error: 1250000000.85
R² Score: 0.7856

Feature Importance:
    feature  coefficient
0      sqft     156.789
3       age     -12.456
1  bedrooms      45.123
2 bathrooms      23.678`,
  },
  {
    id: "12",
    type: "code",
    content: `# Visualization
plt.figure(figsize=(12, 4))

# Actual vs Predicted scatter plot
plt.subplot(1, 2, 1)
plt.scatter(y_test, y_pred, alpha=0.6)
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
plt.xlabel('Actual Values')
plt.ylabel('Predicted Values')
plt.title('Actual vs Predicted Values')

# Residual plot
plt.subplot(1, 2, 2)
residuals = y_test - y_pred
plt.scatter(y_pred, residuals, alpha=0.6)
plt.axhline(y=0, color='r', linestyle='--')
plt.xlabel('Predicted Values')
plt.ylabel('Residuals')
plt.title('Residual Plot')

plt.tight_layout()
plt.show()

print("\\n✅ Analysis completed successfully!")`,
    executionCount: 6,
  },
  {
    id: "13",
    type: "output",
    content: `[Visualization Output - Scatter plots showing actual vs predicted values and residual analysis]

✅ Analysis completed successfully!`,
  },
];

// Mock submission data with files including notebook
const mockSubmissionData = {
  files: [
    {
      name: "linear_regression_analysis.ipynb",
      size: "3.2 MB",
      type: "notebook",
      description: "Main analysis notebook",
      cells: mockNotebookCells,
    },
    {
      name: "housing_data.csv",
      size: "2.3 MB",
      type: "csv",
      description: "Main dataset for analysis",
      content: [
        { sqft: 1200, bedrooms: 3, bathrooms: 2, age: 15, price: 250000 },
        { sqft: 1500, bedrooms: 4, bathrooms: 3, age: 8, price: 320000 },
        { sqft: 800, bedrooms: 2, bathrooms: 1, age: 25, price: 180000 },
        { sqft: 2000, bedrooms: 5, bathrooms: 4, age: 3, price: 450000 },
        { sqft: 1100, bedrooms: 3, bathrooms: 2, age: 20, price: 230000 },
      ],
    },
    {
      name: "data_dictionary.txt",
      size: "1.2 KB",
      type: "txt",
      description: "Column descriptions",
      content: `Housing Data Dictionary

sqft: Square footage of the house (numeric)
bedrooms: Number of bedrooms (integer, 1-6)
bathrooms: Number of bathrooms (numeric, 1-4)
age: Age of the house in years (integer, 1-30)
price: Sale price in USD (numeric, target variable)

Data Collection: Real estate transactions from 2020-2024
Source: Local MLS database
Quality: Cleaned and validated dataset
Usage: For educational purposes only`,
    },
    {
      name: "supplementary_data.xlsx",
      size: "456 KB",
      type: "xlsx",
      description: "Additional data sources",
      content: {
        Market_Stats: [
          { metric: "Average Price", value: 287450, change: "+5.2%" },
          { metric: "Median Price", value: 285000, change: "+3.8%" },
          { metric: "Price per SqFt", value: 197.5, change: "****%" },
        ],
        Regional_Data: [
          { region: "Downtown", avg_price: 320000, avg_sqft: 1200 },
          { region: "Suburbs", avg_price: 275000, avg_sqft: 1500 },
          { region: "Uptown", avg_price: 290000, avg_sqft: 1350 },
        ],
      },
    },
  ],
  submittedAt: "2025-01-12T10:30:00Z",
  comments:
    "Implemented all required algorithms and included extra analysis on feature importance.",
  instructorFeedback: [
    {
      id: "1",
      feedback:
        "Excellent work on data preprocessing! The feature engineering approach is well thought out.",
      addedAt: "2025-01-13T09:15:00Z",
      addedBy: "Dr. A. Sharma",
    },
    {
      id: "2",
      feedback:
        "Consider exploring polynomial features for better model performance. The current linear model captures the main trends well.",
      addedAt: "2025-01-13T14:30:00Z",
      addedBy: "Dr. A. Sharma",
    },
  ],
};

// Mock previous submissions
const mockPreviousSubmissions: PreviousSubmission[] = [
  {
    version: 1,
    submittedAt: "2025-01-10T16:20:00Z",
    status: "submitted",
    files: [
      {
        name: "initial_analysis.ipynb",
        size: "1.2 MB",
        type: "notebook",
        description: "First attempt at analysis",
      },
    ],
  },
  {
    version: 2,
    submittedAt: "2025-01-11T20:45:00Z",
    status: "submitted",
    files: [
      {
        name: "improved_analysis.ipynb",
        size: "1.8 MB",
        type: "notebook",
        description: "Added feature engineering",
      },
      {
        name: "housing_data.csv",
        size: "2.3 MB",
        type: "csv",
        description: "Dataset",
      },
    ],
  },
];

// Advanced Rubric-Based Grading Modal Component (same as GradebookPage/SubmissionsPage)
function RubricGradingModal({
  open,
  onOpenChange,
  studentName,
  assignment,
  onSave,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  studentName: string;
  assignment: Assignment;
  onSave: (gradeData: any) => void;
}) {
  const [rubricGrades, setRubricGrades] = useState<{ [key: string]: number }>(
    {}
  );
  const [feedback, setFeedback] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize rubric grades when modal opens
  React.useEffect(() => {
    if (assignment?.rubric) {
      const initialGrades: { [key: string]: number } = {};
      assignment.rubric.forEach((item: RubricItem) => {
        initialGrades[item.id] = item.earnedPoints || 0;
      });
      setRubricGrades(initialGrades);
    }
  }, [assignment]);

  const handleRubricChange = (rubricId: string, value: string) => {
    const numValue = parseFloat(value) || 0;
    setRubricGrades((prev) => ({
      ...prev,
      [rubricId]: numValue,
    }));
  };

  const calculateTotalScore = () => {
    const totalEarned = Object.values(rubricGrades).reduce(
      (sum, points) => sum + points,
      0
    );
    const totalPossible =
      assignment?.rubric?.reduce(
        (sum: number, item: RubricItem) => sum + item.points,
        0
      ) || 0;
    return { totalEarned, totalPossible };
  };

  const calculateLetterGrade = (percentage: number) => {
    if (percentage >= 95) return "A";
    if (percentage >= 90) return "A-";
    if (percentage >= 87) return "B+";
    if (percentage >= 83) return "B";
    if (percentage >= 80) return "B-";
    if (percentage >= 77) return "C+";
    if (percentage >= 73) return "C";
    if (percentage >= 70) return "C-";
    if (percentage >= 67) return "D+";
    if (percentage >= 65) return "D";
    return "F";
  };

  const handleSave = async () => {
    // Validate all rubric items have been graded
    const ungraded = assignment?.rubric?.filter(
      (item: RubricItem) =>
        !rubricGrades[item.id] && rubricGrades[item.id] !== 0
    );

    if (ungraded?.length > 0) {
      toast.error("Please grade all rubric criteria before saving");
      return;
    }

    // Validate points don't exceed maximum
    const invalidGrades = assignment?.rubric?.filter(
      (item: RubricItem) => rubricGrades[item.id] > item.points
    );

    if (invalidGrades?.length > 0) {
      toast.error("Points cannot exceed maximum for any criteria");
      return;
    }

    setIsSubmitting(true);

    try {
      const { totalEarned, totalPossible } = calculateTotalScore();
      const percentage =
        totalPossible > 0 ? (totalEarned / totalPossible) * 100 : 0;
      const letterGrade = calculateLetterGrade(percentage);

      const gradeData = {
        studentName,
        assignmentId: assignment.id,
        assignmentTitle: assignment.title,
        score: totalEarned,
        maxScore: totalPossible,
        letterGrade,
        feedback,
        rubricGrades,
        gradedAt: new Date().toISOString(),
      };

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      onSave(gradeData);
      onOpenChange(false);

      toast.success(`Grade saved successfully for ${studentName}`);

      // Reset form
      setRubricGrades({});
      setFeedback("");
    } catch (error) {
      toast.error("Failed to save grade. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    // Reset form
    if (assignment?.rubric) {
      const initialGrades: { [key: string]: number } = {};
      assignment.rubric.forEach((item: RubricItem) => {
        initialGrades[item.id] = item.earnedPoints || 0;
      });
      setRubricGrades(initialGrades);
    }
    setFeedback("");
  };

  const { totalEarned, totalPossible } = calculateTotalScore();
  const percentage =
    totalPossible > 0 ? (totalEarned / totalPossible) * 100 : 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Grade Submission - Rubric-Based Grading</DialogTitle>
          <DialogDescription>
            Grade {assignment.title} for {studentName} using the assignment
            rubric
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Student Info */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-bits-blue rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="font-medium">{studentName}</p>
                <p className="text-sm text-muted-foreground">
                  {assignment.course}
                </p>
              </div>
            </div>
          </div>

          {/* Assignment Info */}
          <div className="p-4 border rounded-lg">
            <div className="flex items-center space-x-3">
              <FileText className="h-5 w-5 text-bits-blue" />
              <div>
                <p className="font-medium">{assignment.title}</p>
                <p className="text-sm text-muted-foreground">
                  {assignment.course} • Due:{" "}
                  {new Date(assignment.dueDate).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>

          {/* Rubric Grading */}
          <div className="space-y-4">
            <h3 className="font-medium">Grading Rubric</h3>

            {assignment?.rubric?.map((item: RubricItem, index: number) => (
              <div key={item.id} className="border rounded-lg p-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{item.criteria}</h4>
                      <p className="text-xs text-muted-foreground mt-1">
                        {item.description}
                      </p>
                    </div>
                    <div className="text-right ml-4">
                      <p className="text-xs text-muted-foreground">
                        Max: {item.points} pts
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Label
                      htmlFor={`rubric-${item.id}`}
                      className="text-sm min-w-0"
                    >
                      Points:
                    </Label>
                    <Input
                      id={`rubric-${item.id}`}
                      type="number"
                      min="0"
                      max={item.points}
                      step="0.5"
                      value={rubricGrades[item.id] || ""}
                      onChange={(e) =>
                        handleRubricChange(item.id, e.target.value)
                      }
                      className="w-20"
                      placeholder="0"
                    />
                    <span className="text-sm text-muted-foreground">
                      / {item.points}
                    </span>
                  </div>

                  {/* Progress bar showing completion */}
                  <Progress
                    value={
                      rubricGrades[item.id]
                        ? (rubricGrades[item.id] / item.points) * 100
                        : 0
                    }
                    className="h-2"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Total Score Summary */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Total Score</p>
                <p className="text-xs text-muted-foreground">
                  {totalEarned.toFixed(1)} / {totalPossible} points
                </p>
              </div>
              <div className="text-right">
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-bold">
                    {percentage.toFixed(1)}%
                  </span>
                  <Badge variant="outline" className="text-sm">
                    {calculateLetterGrade(percentage)}
                  </Badge>
                </div>
              </div>
            </div>
            <Progress value={percentage} className="mt-2 h-2" />
          </div>

          {/* Feedback */}
          <div className="space-y-2">
            <Label htmlFor="feedback">Overall Feedback (Optional)</Label>
            <Textarea
              id="feedback"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Enter overall feedback for the student..."
              rows={4}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={isSubmitting}
              className="bg-bits-blue hover:bg-bits-blue/90"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSubmitting ? "Saving..." : "Save Grade"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function FeedbackDialog({ studentName }: { studentName: string }) {
  const [open, setOpen] = useState(false);
  const [feedback, setFeedback] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Adding feedback:", feedback);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <MessageSquare className="h-4 w-4 mr-2" />
          Add Feedback
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Add Feedback - {studentName}</DialogTitle>
          <DialogDescription>
            Provide detailed feedback on the student's submission.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="feedback">Feedback</Label>
            <Textarea
              id="feedback"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Write your feedback here..."
              rows={8}
              className="resize-none"
            />
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-bits-blue hover:bg-bits-blue/90"
            >
              Save Feedback
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

function FileContentRenderer({ file }: { file: SubmissionFile }) {
  const renderFileContent = () => {
    switch (file.type) {
      case "notebook":
        return (
          <div className="border-t border-gray-200">
            {file.cells?.map((cell, index) => (
              <div
                key={cell.id}
                className="border-b border-gray-100 last:border-b-0"
              >
                {cell.type === "markdown" && (
                  <div className="p-4 bg-white">
                    <div className="prose prose-sm max-w-none">
                      <pre className="whitespace-pre-wrap font-sans text-gray-700 text-sm leading-relaxed">
                        {cell.content}
                      </pre>
                    </div>
                  </div>
                )}

                {cell.type === "code" && (
                  <div className="flex">
                    <div className="w-12 bg-gray-50 border-r border-gray-200 flex items-start justify-center pt-3">
                      <span className="text-xs text-gray-500">
                        [{cell.executionCount || " "}]:
                      </span>
                    </div>
                    <div className="flex-1 p-3">
                      <pre className="font-mono text-sm text-gray-800 whitespace-pre-wrap overflow-x-auto">
                        {cell.content}
                      </pre>
                    </div>
                  </div>
                )}

                {cell.type === "output" && (
                  <div className="flex">
                    <div className="w-12 bg-gray-50 border-r border-gray-200"></div>
                    <div className="flex-1 p-3 bg-gray-50">
                      <pre className="font-mono text-sm text-gray-700 whitespace-pre-wrap overflow-x-auto">
                        {cell.content}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        );

      case "csv":
        return (
          <div className="p-6 space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Data Preview</h4>
              <Badge variant="outline">
                {file.content?.length || 0} rows shown
              </Badge>
            </div>
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>SqFt</TableHead>
                    <TableHead>Bedrooms</TableHead>
                    <TableHead>Bathrooms</TableHead>
                    <TableHead>Age</TableHead>
                    <TableHead>Price</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {file.content?.slice(0, 20).map((row: any, index: number) => (
                    <TableRow key={index}>
                      <TableCell>{row.sqft}</TableCell>
                      <TableCell>{row.bedrooms}</TableCell>
                      <TableCell>{row.bathrooms}</TableCell>
                      <TableCell>{row.age}</TableCell>
                      <TableCell>${row.price.toLocaleString()}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        );

      case "txt":
        return (
          <div className="p-6 space-y-4">
            <h4 className="font-medium">File Content</h4>
            <div className="border rounded-lg p-4 bg-gray-50">
              <pre className="text-sm whitespace-pre-wrap font-mono">
                {file.content}
              </pre>
            </div>
          </div>
        );

      case "xlsx":
        return (
          <div className="p-6 space-y-6">
            <h4 className="font-medium">Excel Workbook</h4>
            <Tabs defaultValue="Market_Stats">
              <TabsList>
                <TabsTrigger value="Market_Stats">Market Stats</TabsTrigger>
                <TabsTrigger value="Regional_Data">Regional Data</TabsTrigger>
              </TabsList>

              <TabsContent value="Market_Stats" className="space-y-4">
                <div className="border rounded-lg overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Metric</TableHead>
                        <TableHead>Value</TableHead>
                        <TableHead>Change</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {file.content?.Market_Stats?.map(
                        (row: any, index: number) => (
                          <TableRow key={index}>
                            <TableCell>{row.metric}</TableCell>
                            <TableCell>${row.value.toLocaleString()}</TableCell>
                            <TableCell
                              className={
                                row.change.startsWith("+")
                                  ? "text-green-600"
                                  : "text-red-600"
                              }
                            >
                              {row.change}
                            </TableCell>
                          </TableRow>
                        )
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>

              <TabsContent value="Regional_Data" className="space-y-4">
                <div className="border rounded-lg overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Region</TableHead>
                        <TableHead>Avg Price</TableHead>
                        <TableHead>Avg SqFt</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {file.content?.Regional_Data?.map(
                        (row: any, index: number) => (
                          <TableRow key={index}>
                            <TableCell>{row.region}</TableCell>
                            <TableCell>
                              ${row.avg_price.toLocaleString()}
                            </TableCell>
                            <TableCell>
                              {row.avg_sqft.toLocaleString()}
                            </TableCell>
                          </TableRow>
                        )
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        );

      default:
        return (
          <div className="p-6 text-center py-12">
            <File className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              File preview not available for this type
            </p>
          </div>
        );
    }
  };

  return renderFileContent();
}

function ModelTestingInterface({
  studentId,
  studentName,
  assignmentId,
  assignmentTitle,
}: {
  studentId?: string;
  studentName?: string;
  assignmentId?: string;
  assignmentTitle?: string;
}) {
  const { navigateTo } = useNavigation();

  const handleRunModelTest = () => {
    navigateTo("model-testing", {
      studentId,
      studentName,
      assignmentId,
      assignmentTitle,
      submissionId: "sub_001",
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <TestTube className="h-5 w-5 mr-2" />
          Model Testing
        </CardTitle>
        <CardDescription>
          Run comprehensive model testing and evaluation
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-center py-6">
          <TestTube className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground mb-4">
            Launch comprehensive model testing with configurable parameters
          </p>
          <Button
            onClick={handleRunModelTest}
            className="bg-green-600 hover:bg-green-700"
          >
            <TestTube className="h-4 w-4 mr-2" />
            Run Model Test
          </Button>
        </div>

        <div className="text-xs text-muted-foreground bg-gray-50 p-3 rounded-lg">
          <p className="font-medium mb-1">Testing includes:</p>
          <ul className="space-y-1">
            <li>• Comprehensive model evaluation</li>
            <li>• Multiple evaluation metrics</li>
            <li>• Performance analysis</li>
            <li>• Prediction validation</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}

function ProjectDetailsSheet({
  project,
  open,
  onOpenChange,
}: {
  project: Assignment;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-[1150px] max-w-[90vw] overflow-y-auto">
        <div className="mx-5">
          <SheetHeader className="pb-6 border-b border-gray-200">
            <SheetTitle className="flex items-center text-xl">
              <BookOpen className="h-6 w-6 mr-3 text-bits-blue" />
              {project.title}
            </SheetTitle>
            <SheetDescription className="text-base mt-2 text-muted-foreground">
              {project.course}
            </SheetDescription>
          </SheetHeader>

          <div className="py-6 space-y-8">
            {/* Project Overview */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-foreground border-b border-gray-200 pb-2">
                Project Description
              </h4>
              <div className="bg-blue-50 border-l-4 border-bits-blue p-5 rounded-r-lg">
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {project.description}
                </p>
              </div>
            </div>

            {/* Requirements */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-foreground border-b border-gray-200 pb-2">
                Requirements
              </h4>
              <div className="bg-gray-50 p-5 rounded-lg border">
                <ul className="space-y-3">
                  {project.requirements.map((req, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-bits-blue rounded-full mt-2.5 flex-shrink-0"></div>
                      <span className="text-sm text-muted-foreground leading-relaxed">
                        {req}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Datasets */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-foreground border-b border-gray-200 pb-2">
                Provided Datasets
              </h4>
              <div className="grid gap-4">
                {project.datasets.map((dataset, index) => (
                  <div
                    key={index}
                    className="border rounded-lg p-5 bg-gradient-to-r from-green-50 to-emerald-50 hover:from-green-100 hover:to-emerald-100 transition-all duration-200 border-green-200"
                  >
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <Database className="h-5 w-5 text-green-600" />
                      </div>
                      <span className="font-semibold text-base text-foreground">
                        {dataset.name}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3 leading-relaxed pl-10">
                      {dataset.description}
                    </p>
                    <div className="pl-10">
                      <Badge
                        variant="outline"
                        className="text-xs font-medium px-2 py-1 bg-white border-green-300 text-green-700"
                      >
                        {dataset.format}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Rubric */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-foreground border-b border-gray-200 pb-2">
                Grading Rubric
              </h4>
              <div className="grid gap-4">
                {project.rubric.map((item, index) => (
                  <div
                    key={index}
                    className="border rounded-lg p-5 bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 transition-all duration-200 border-blue-200"
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-bits-blue rounded-lg">
                          <GraduationCap className="h-4 w-4 text-white" />
                        </div>
                        <span className="font-semibold text-base text-foreground">
                          {item.criteria}
                        </span>
                      </div>
                      <Badge className="bg-bits-blue text-white font-semibold px-3 py-1 text-sm">
                        {item.maxPoints} pts
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground leading-relaxed pl-10">
                      {item.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* Project Info */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-foreground border-b border-gray-200 pb-2">
                Project Information
              </h4>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="p-1.5 bg-orange-100 rounded-lg">
                      <Calendar className="h-4 w-4 text-orange-600" />
                    </div>
                    <h5 className="font-semibold text-base text-foreground">
                      Due Date
                    </h5>
                  </div>
                  <div className="bg-gradient-to-r from-orange-50 to-amber-50 p-4 rounded-lg border border-orange-200 ml-8">
                    <p className="text-sm text-muted-foreground font-medium">
                      {new Date(project.dueDate).toLocaleDateString("en-US", {
                        weekday: "long",
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}
                    </p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="p-1.5 bg-green-100 rounded-lg">
                      <Users className="h-4 w-4 text-green-600" />
                    </div>
                    <h5 className="font-semibold text-base text-foreground">
                      Enrollment
                    </h5>
                  </div>
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border border-green-200 ml-8">
                    <p className="text-sm text-muted-foreground font-medium">
                      {project.totalStudents} students enrolled
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}

export default function SandboxEnvironmentPage({
  mode = "sandbox",
  studentId,
  studentName,
  assignmentId,
  assignmentTitle,
  notebookUrl,
  initialTab,
}: SandboxEnvironmentPageProps) {
  const { user } = useAuth();
  const { navigateTo, goBack } = useNavigation();

  // State management
  const [currentView, setCurrentView] = useState<
    "launch" | "active" | "notebook" | "sessions"
  >("launch");
  const [activeSession, setActiveSession] = useState<any>(null);
  const [isLaunching, setIsLaunching] = useState(false);
  const [projectId] = useState("current-project-id"); // In real app, get from props/context
  const [activeTab, setActiveTab] = useState(
    activeSession ? "files" : initialTab || "launch"
  );

  // Update active tab when initialTab changes or when a session becomes active
  useEffect(() => {
    if (initialTab && !activeSession) {
      setActiveTab(initialTab);
    } else if (activeSession && initialTab === "notebook") {
      // If launching with notebook tab requested and session is active, honor that
      setActiveTab("notebook");
    }
  }, [initialTab, activeSession]);

  // API integration functions
  const handleLaunchSandbox = async (settings: any) => {
    setIsLaunching(true);
    try {
      // Mock implementation for demo - replace with real API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      const sessionData = {
        sessionId: `session-${Date.now()}`,
        success: true,
      };

      setActiveSession({
        sessionId: sessionData.sessionId,
        status: "Active",
        mode: settings.mode,
        startedAt: new Date().toISOString(),
        idleTimeout: settings.idleTimeout,
        timeRemaining: settings.idleTimeout,
        cpuLimit: settings.cpuLimit,
        memLimit: settings.memLimit,
      });
      setCurrentView("active");

      // Switch to the requested initial tab, or default to files
      if (initialTab === "notebook") {
        setActiveTab("notebook");
        toast.success(
          "Sandbox launched successfully! Opening notebook environment..."
        );
      } else {
        setActiveTab("files");
        toast.success("Sandbox launched successfully!");
      }
    } catch (error) {
      console.error("Failed to launch sandbox:", error);
      toast.error("Failed to launch sandbox. Please try again.");
    } finally {
      setIsLaunching(false);
    }
  };

  const handleStopSession = async (sessionId: string) => {
    try {
      // Mock implementation for demo - replace with real API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setActiveSession(null);
      setCurrentView("launch");
      setActiveTab("launch"); // Switch back to launch tab
    } catch (error) {
      console.error("Failed to stop session:", error);
    }
  };

  const handleSyncFiles = async () => {
    try {
      // Mock implementation for demo - replace with real API call
      await new Promise((resolve) => setTimeout(resolve, 800));

      console.log("Files synced successfully");
    } catch (error) {
      console.error("Failed to sync files:", error);
    }
  };

  const handleJoinSession = (session: any) => {
    setActiveSession(session);
    setCurrentView("notebook");
  };

  // Render different views based on current state and user role
  const renderContent = () => {
    // For instructor/admin viewing sessions
    if (
      mode === "grading" ||
      (user?.role !== "student" && currentView === "sessions")
    ) {
      return (
        <SandboxSessionsList
          projectId={projectId}
          onJoinSession={handleJoinSession}
        />
      );
    }

    // For viewing embedded notebook
    if (currentView === "notebook" || mode === "student-notebook") {
      return (
        <EmbeddedNotebook
          userId={studentId || user?.id || ""}
          projectId={projectId}
          projectName={assignmentTitle || "Current Project"}
          mode={user?.role === "student" ? "student" : "instructor"}
          onBack={() =>
            user?.role === "student" ? setCurrentView("active") : goBack()
          }
          onSave={() => console.log("Notebook saved")}
          onDownload={() => console.log("Notebook exported")}
          sessionId={activeSession?.sessionId}
        />
      );
    }

    // Main sandbox interface for students
    return (
      <div className="space-y-6">
        {/* Active Session Banner */}
        {activeSession && (
          <ActiveSessionBanner
            session={activeSession}
            onStop={handleStopSession}
            onSync={handleSyncFiles}
            projectId={projectId}
          />
        )}

        {/* Main Content with Custom Tabs */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-6">
              {/* Tab Navigation */}
              <div className="flex items-center bg-gray-200 rounded-lg p-1 mb-6 w-fit">
                <button
                  onClick={() => setActiveTab("launch")}
                  disabled={!!activeSession}
                  className={`px-6 py-2 rounded-md transition-all duration-200 text-sm font-medium ${
                    activeTab === "launch"
                      ? "bg-white text-bits-blue shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  } ${activeSession ? "opacity-50 cursor-not-allowed" : ""}`}
                >
                  Launch
                </button>
                <button
                  onClick={() => setActiveTab("files")}
                  className={`px-6 py-2 rounded-md transition-all duration-200 text-sm font-medium ${
                    activeTab === "files"
                      ? "bg-white text-bits-blue shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  Files
                </button>
                <button
                  onClick={() => setActiveTab("notebook")}
                  disabled={!activeSession}
                  className={`px-6 py-2 rounded-md transition-all duration-200 text-sm font-medium ${
                    activeTab === "notebook"
                      ? "bg-white text-bits-blue shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  } ${!activeSession ? "opacity-50 cursor-not-allowed" : ""}`}
                >
                  Notebook
                </button>
              </div>

              {/* Tab Content */}
              {activeTab === "launch" && (
                <div className="space-y-6">
                  <SandboxLaunchPanel
                    projectId={projectId}
                    onLaunch={handleLaunchSandbox}
                    isLaunching={isLaunching}
                  />
                </div>
              )}

              {activeTab === "files" && (
                <div className="space-y-6">
                  <SandboxFileBrowser
                    projectId={projectId}
                    userId={user?.id || ""}
                    isGroupProject={true}
                    onFileSelect={(file) => console.log("File selected:", file)}
                    onFileUpload={(files, path) =>
                      console.log("Files uploaded:", files, path)
                    }
                    onFileDownload={(file) =>
                      console.log("File downloaded:", file)
                    }
                    onRefresh={() => console.log("Files refreshed")}
                  />
                </div>
              )}

              {activeTab === "notebook" && (
                <div className="space-y-6">
                  {activeSession ? (
                    <Card>
                      <CardHeader>
                        <CardTitle>Launch Notebook Environment</CardTitle>
                        <CardDescription>
                          Open JupyterLab in a new window or embedded view
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="flex gap-4">
                          <Button
                            onClick={() => setCurrentView("notebook")}
                            className="bg-bits-blue hover:bg-bits-blue/90"
                          >
                            Open Embedded Notebook
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() =>
                              window.open(
                                `/studio/user/${user?.id}/lab/tree/work`,
                                "_blank"
                              )
                            }
                          >
                            Open in New Window
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card>
                      <CardContent className="text-center py-8">
                        <p className="text-gray-500">
                          Start a sandbox session to access the notebook
                          environment
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  // Header with navigation for instructors
  const renderHeader = () => {
    if (user?.role !== "student") {
      return (
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">
              {mode === "grading" ? "Sandbox Sessions" : "Sandbox Environment"}
            </h1>
            <p className="text-gray-600 mt-1">
              {mode === "grading"
                ? "Monitor and join student sandbox sessions"
                : "Manage sandbox environments and review student work"}
            </p>
          </div>

          {mode !== "grading" && (
            <div className="flex items-center bg-gray-200 rounded-lg p-1 w-fit">
              <button
                onClick={() => setCurrentView("launch")}
                className={`px-6 py-2 rounded-md transition-all duration-200 text-sm font-medium ${
                  currentView === "launch"
                    ? "bg-white text-bits-blue shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                Launch
              </button>
              <button
                onClick={() => setCurrentView("sessions")}
                className={`px-6 py-2 rounded-md transition-all duration-200 text-sm font-medium ${
                  currentView === "sessions"
                    ? "bg-white text-bits-blue shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                Sessions
              </button>
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">
          Sandbox Environment
        </h1>
        <p className="text-gray-600 mt-1">
          Launch and manage your JupyterLab workspace for data science projects
        </p>
      </div>
    );
  };

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {renderHeader()}
      {renderContent()}
    </div>
  );
}
