import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import { Switch } from "../components/ui/switch";
import { Progress } from "../components/ui/progress";
import { Separator } from "../components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../components/ui/dialog";
import { Checkbox } from "../components/ui/checkbox";
import {
  Terminal,
  Clock,
  CheckCircle,
  XCircle,
  Plus,
  Cpu,
  HardDrive,
  Zap,
  Search,
  MemoryStick,
  DollarSign,
  BarChart3,
  Edit,
} from "lucide-react";
import { toast } from "sonner";

interface SandboxSession {
  id: string;
  studentId: string;
  studentName: string;
  studentEmail: string;
  courseId: string;
  courseName: string;
  assignmentId?: string;
  assignmentTitle?: string;
  status: "active" | "idle" | "terminated" | "suspended";
  startTime: string;
  lastActivity: string;
  timeRemaining: number;
  totalTimeAllocated: number;
  memoryUsage: number;
  cpuUsage: number;
  storageUsage: number;
  environment: "python" | "r" | "jupyter" | "rstudio";
  sessionUrl?: string;
  ipAddress: string;
}

interface CreditRequest {
  id: string;
  studentId: string;
  studentName: string;
  studentEmail: string;
  courseId: string;
  courseName: string;
  assignmentId?: string;
  assignmentTitle?: string;
  currentTimeRemaining: number;
  requestedTime: number;
  reason: string;
  status: "pending" | "approved" | "rejected";
  requestDate: string;
  reviewedBy?: string;
  reviewDate?: string;
  reviewComments?: string;
  priority: "low" | "medium" | "high" | "urgent";
}

interface ResourceProfile {
  id: string;
  name: string;
  resourceType: "docker" | "uv" | "fargate" | "ec2";
  instanceType: string;
  vCpu: number;
  ramGB: number;
  gpuCount: number;
  diskGB: number;
  idleTimeoutMin: number;
  maxRuntimeHrs: number;
  costBudgetUSD: number;
  tags: string[];
  allowedPackages: string[];
  customPackages: string[];
  active: boolean;
  containerImage?: string;
  baseEnvironment?: string;
  fargateTaskDefinition?: string;
  ec2InstanceProfile?: string;
}

interface PlatformQuotas {
  maxSandboxesPerUser: number;
  totalvCPUPool: number;
  monthlyCostCap: number;
  currentSandboxes: number;
  currentvCPU: number;
  currentMonthlyCost: number;
}

const mockSandboxSessions: SandboxSession[] = [
  {
    id: "session_001",
    studentId: "student_001",
    studentName: "Rahul Sharma",
    studentEmail: "<EMAIL>",
    courseId: "course_001",
    courseName: "CS F314 - Machine Learning",
    assignmentId: "assign_001",
    assignmentTitle: "Linear Regression Analysis",
    status: "active",
    startTime: "2025-01-12T09:30:00Z",
    lastActivity: "2025-01-12T11:45:00Z",
    timeRemaining: 25,
    totalTimeAllocated: 120,
    memoryUsage: 78,
    cpuUsage: 45,
    storageUsage: 256,
    environment: "jupyter",
    sessionUrl: "https://jupyter-session-001.sandbox.bits.edu",
    ipAddress: "*********",
  },
];

const mockCreditRequests: CreditRequest[] = [
  {
    id: "request_001",
    studentId: "student_003",
    studentName: "Arjun Krishna",
    studentEmail: "<EMAIL>",
    courseId: "course_002",
    courseName: "CS F372 - Operating Systems",
    assignmentId: "assign_003",
    assignmentTitle: "Process Synchronization Lab",
    currentTimeRemaining: 8,
    requestedTime: 30,
    reason: "Need additional time to complete the threading implementation.",
    status: "pending",
    requestDate: "2025-01-12T11:30:00Z",
    priority: "high",
  },
];

const mockResourceProfiles: ResourceProfile[] = [
  {
    id: "1",
    name: "Docker - Basic Python",
    resourceType: "docker",
    instanceType: "small",
    vCpu: 2,
    ramGB: 4,
    gpuCount: 0,
    diskGB: 20,
    idleTimeoutMin: 30,
    maxRuntimeHrs: 4,
    costBudgetUSD: 2.5,
    tags: ["basic", "python"],
    allowedPackages: ["pandas", "numpy", "matplotlib"],
    customPackages: [],
    containerImage: "python:3.9-slim",
    active: true,
  },
  {
    id: "2",
    name: "EC2 - GPU ML Training",
    resourceType: "ec2",
    instanceType: "p3.2xlarge",
    vCpu: 8,
    ramGB: 32,
    gpuCount: 1,
    diskGB: 100,
    idleTimeoutMin: 60,
    maxRuntimeHrs: 12,
    costBudgetUSD: 25,
    tags: ["gpu", "ml-training"],
    allowedPackages: ["tensorflow", "pytorch", "pandas", "numpy"],
    customPackages: ["transformers", "torch-audio"],
    ec2InstanceProfile: "arn:aws:iam::account:instance-profile/ml-profile",
    active: true,
  },
  {
    id: "3",
    name: "UV - Data Analysis",
    resourceType: "uv",
    instanceType: "medium",
    vCpu: 4,
    ramGB: 8,
    gpuCount: 0,
    diskGB: 50,
    idleTimeoutMin: 45,
    maxRuntimeHrs: 8,
    costBudgetUSD: 5,
    tags: ["standard", "data-analysis"],
    allowedPackages: ["pandas", "numpy", "matplotlib", "seaborn"],
    customPackages: ["polars", "duckdb"],
    baseEnvironment: "python3.11",
    active: true,
  },
  {
    id: "4",
    name: "Fargate - Serverless Compute",
    resourceType: "fargate",
    instanceType: "fargate-1024-2048",
    vCpu: 1,
    ramGB: 2,
    gpuCount: 0,
    diskGB: 30,
    idleTimeoutMin: 15,
    maxRuntimeHrs: 2,
    costBudgetUSD: 1.5,
    tags: ["serverless", "lightweight"],
    allowedPackages: ["requests", "pandas", "numpy"],
    customPackages: ["httpx"],
    fargateTaskDefinition:
      "arn:aws:ecs:us-east-1:account:task-definition/sandbox:1",
    active: true,
  },
];

const mockPlatformQuotas: PlatformQuotas = {
  maxSandboxesPerUser: 3,
  totalvCPUPool: 100,
  monthlyCostCap: 1000,
  currentSandboxes: 24,
  currentvCPU: 68,
  currentMonthlyCost: 650,
};

const availablePackages = [
  "pandas",
  "numpy",
  "matplotlib",
  "seaborn",
  "plotly",
  "scikit-learn",
  "tensorflow",
  "pytorch",
  "keras",
  "jupyter",
  "scipy",
  "statsmodels",
  "opencv-python",
  "requests",
  "beautifulsoup4",
  "nltk",
  "pillow",
];

function EditResourceProfileModal({
  profile,
  isOpen,
  onClose,
  onSave,
}: {
  profile?: ResourceProfile;
  isOpen: boolean;
  onClose: () => void;
  onSave: (profile: ResourceProfile) => void;
}) {
  const [formData, setFormData] = useState<ResourceProfile>(
    profile || {
      id: "",
      name: "",
      resourceType: "docker",
      instanceType: "small",
      vCpu: 2,
      ramGB: 4,
      gpuCount: 0,
      diskGB: 20,
      idleTimeoutMin: 30,
      maxRuntimeHrs: 4,
      costBudgetUSD: 2.5,
      tags: [],
      allowedPackages: [],
      customPackages: [],
      active: true,
    }
  );

  const [newPackage, setNewPackage] = useState("");

  const resourceTypes = [
    {
      value: "docker",
      label: "Docker Container",
      icon: "🐳",
      description: "Containerized environment",
    },
    {
      value: "uv",
      label: "UV Virtual Environment",
      icon: "🐍",
      description: "Python virtual environment",
    },
    {
      value: "fargate",
      label: "AWS Fargate",
      icon: "☁️",
      description: "Serverless containers",
    },
    {
      value: "ec2",
      label: "EC2 Virtual Machine",
      icon: "🖥️",
      description: "Full virtual machine",
    },
  ];

  const handleSave = () => {
    if (!formData.name.trim() || !formData.instanceType) {
      toast.error("Please fill in all required fields");
      return;
    }

    const profileToSave = {
      ...formData,
      id: formData.id || Date.now().toString(),
    };

    onSave(profileToSave);
    onClose();
    toast.success(
      `Resource profile ${profile ? "updated" : "created"} successfully`
    );
  };

  const togglePackage = (pkg: string) => {
    setFormData((prev) => ({
      ...prev,
      allowedPackages: prev.allowedPackages.includes(pkg)
        ? prev.allowedPackages.filter((p) => p !== pkg)
        : [...prev.allowedPackages, pkg],
    }));
  };

  const addCustomPackage = () => {
    if (
      newPackage.trim() &&
      !formData.customPackages.includes(newPackage.trim()) &&
      !availablePackages.includes(newPackage.trim())
    ) {
      setFormData((prev) => ({
        ...prev,
        customPackages: [...prev.customPackages, newPackage.trim()],
      }));
      setNewPackage("");
    }
  };

  const removeCustomPackage = (pkg: string) => {
    setFormData((prev) => ({
      ...prev,
      customPackages: prev.customPackages.filter((p) => p !== pkg),
    }));
  };

  const getInstanceOptions = () => {
    switch (formData.resourceType) {
      case "fargate":
        return [
          { value: "fargate-256-512", label: "256 vCPU, 0.5GB RAM" },
          { value: "fargate-512-1024", label: "512 vCPU, 1GB RAM" },
          { value: "fargate-1024-2048", label: "1024 vCPU, 2GB RAM" },
          { value: "fargate-2048-4096", label: "2048 vCPU, 4GB RAM" },
        ];
      case "ec2":
        return [
          { value: "t3.micro", label: "t3.micro" },
          { value: "t3.small", label: "t3.small" },
          { value: "t3.medium", label: "t3.medium" },
          { value: "m5.large", label: "m5.large" },
          { value: "p3.2xlarge", label: "p3.2xlarge" },
        ];
      default:
        return [
          { value: "small", label: "Small (2 vCPU, 4GB RAM)" },
          { value: "medium", label: "Medium (4 vCPU, 8GB RAM)" },
          { value: "large", label: "Large (8 vCPU, 16GB RAM)" },
          { value: "xlarge", label: "XLarge (16 vCPU, 32GB RAM)" },
        ];
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {profile ? "Edit Resource Profile" : "Create Resource Profile"}
          </DialogTitle>
          <DialogDescription>
            Configure computational resources and constraints for this profile.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Resource Type Selection */}
          <div className="space-y-4">
            <h4 className="font-medium">Resource Type</h4>
            <div className="grid grid-cols-2 gap-4">
              {resourceTypes.map((type) => (
                <div
                  key={type.value}
                  onClick={() =>
                    setFormData((prev) => ({
                      ...prev,
                      resourceType: type.value as any,
                      instanceType: "",
                    }))
                  }
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    formData.resourceType === type.value
                      ? "border-bits-blue bg-blue-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{type.icon}</span>
                    <div>
                      <h5 className="font-medium">{type.label}</h5>
                      <p className="text-xs text-gray-600">
                        {type.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h4 className="font-medium">Basic Information</h4>

              <div className="space-y-2">
                <Label htmlFor="profileName">Profile Name *</Label>
                <Input
                  id="profileName"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, name: e.target.value }))
                  }
                  placeholder="e.g., Docker - Basic Python"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="instanceType">
                  {formData.resourceType === "fargate"
                    ? "Fargate Configuration"
                    : formData.resourceType === "ec2"
                    ? "EC2 Instance Type"
                    : "Instance Size"}{" "}
                  *
                </Label>
                <Select
                  value={formData.instanceType}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, instanceType: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select configuration" />
                  </SelectTrigger>
                  <SelectContent>
                    {getInstanceOptions().map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Resource Type Specific Configuration */}
              {formData.resourceType === "docker" && (
                <div className="space-y-2">
                  <Label htmlFor="containerImage">Container Image</Label>
                  <Input
                    id="containerImage"
                    value={formData.containerImage || ""}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        containerImage: e.target.value,
                      }))
                    }
                    placeholder="e.g., python:3.9-slim"
                  />
                </div>
              )}

              {formData.resourceType === "uv" && (
                <div className="space-y-2">
                  <Label htmlFor="baseEnvironment">Python Version</Label>
                  <Select
                    value={formData.baseEnvironment || ""}
                    onValueChange={(value) =>
                      setFormData((prev) => ({
                        ...prev,
                        baseEnvironment: value,
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Python version" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="python3.9">Python 3.9</SelectItem>
                      <SelectItem value="python3.10">Python 3.10</SelectItem>
                      <SelectItem value="python3.11">Python 3.11</SelectItem>
                      <SelectItem value="python3.12">Python 3.12</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {formData.resourceType === "fargate" && (
                <div className="space-y-2">
                  <Label htmlFor="fargateTaskDefinition">
                    Task Definition ARN
                  </Label>
                  <Input
                    id="fargateTaskDefinition"
                    value={formData.fargateTaskDefinition || ""}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        fargateTaskDefinition: e.target.value,
                      }))
                    }
                    placeholder="arn:aws:ecs:region:account:task-definition/family:revision"
                  />
                </div>
              )}

              {formData.resourceType === "ec2" && (
                <div className="space-y-2">
                  <Label htmlFor="ec2InstanceProfile">
                    Instance Profile ARN
                  </Label>
                  <Input
                    id="ec2InstanceProfile"
                    value={formData.ec2InstanceProfile || ""}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        ec2InstanceProfile: e.target.value,
                      }))
                    }
                    placeholder="arn:aws:iam::account:instance-profile/profile-name"
                  />
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="vCpu">vCPU</Label>
                  <Input
                    id="vCpu"
                    type="number"
                    min="1"
                    value={formData.vCpu}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        vCpu: Number(e.target.value),
                      }))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="ramGB">RAM (GB)</Label>
                  <Input
                    id="ramGB"
                    type="number"
                    min="1"
                    value={formData.ramGB}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        ramGB: Number(e.target.value),
                      }))
                    }
                  />
                </div>
              </div>
            </div>

            {/* Limits & Budget */}
            <div className="space-y-4">
              <h4 className="font-medium">Limits & Budget</h4>

              <div className="space-y-2">
                <Label htmlFor="costBudget">Cost Budget (USD)</Label>
                <Input
                  id="costBudget"
                  type="number"
                  min="0.1"
                  step="0.1"
                  value={formData.costBudgetUSD}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      costBudgetUSD: Number(e.target.value),
                    }))
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxRuntime">Max Runtime (hours)</Label>
                <Input
                  id="maxRuntime"
                  type="number"
                  min="1"
                  value={formData.maxRuntimeHrs}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      maxRuntimeHrs: Number(e.target.value),
                    }))
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="idleTimeout">Idle Timeout (minutes)</Label>
                <Input
                  id="idleTimeout"
                  type="number"
                  min="5"
                  value={formData.idleTimeoutMin}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      idleTimeoutMin: Number(e.target.value),
                    }))
                  }
                />
              </div>
            </div>
          </div>

          {/* Package Management */}
          <div className="space-y-6">
            <div>
              <h4 className="font-medium mb-4">Available Packages</h4>
              <div className="grid grid-cols-3 gap-2">
                {availablePackages.map((pkg) => (
                  <div key={pkg} className="flex items-center space-x-2">
                    <Checkbox
                      id={pkg}
                      checked={formData.allowedPackages.includes(pkg)}
                      onCheckedChange={() => togglePackage(pkg)}
                    />
                    <Label htmlFor={pkg} className="text-sm">
                      {pkg}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-4">Custom Packages</h4>
              <div className="space-y-3">
                <div className="flex gap-2">
                  <Input
                    value={newPackage}
                    onChange={(e) => setNewPackage(e.target.value)}
                    placeholder="Enter package name (e.g., transformers, polars)"
                    onKeyDown={(e) =>
                      e.key === "Enter" &&
                      (e.preventDefault(), addCustomPackage())
                    }
                  />
                  <Button type="button" onClick={addCustomPackage} size="sm">
                    <Plus className="h-4 w-4 mr-1" />
                    Add
                  </Button>
                </div>

                {formData.customPackages.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">
                      Custom packages:
                    </Label>
                    <div className="flex flex-wrap gap-1">
                      {formData.customPackages.map((pkg) => (
                        <Badge
                          key={pkg}
                          variant="outline"
                          className="flex items-center gap-1"
                        >
                          {pkg}
                          <button
                            type="button"
                            onClick={() => removeCustomPackage(pkg)}
                            className="text-red-500 hover:text-red-700"
                          >
                            ×
                          </button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <p className="text-xs text-gray-500">
                  Add custom packages that are not in the default list. These
                  will be installed using the appropriate package manager.
                </p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="bg-bits-blue hover:bg-bits-blue/90"
          >
            {profile ? "Update Profile" : "Create Profile"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default function SandboxManagementPage() {
  const [activeTab, setActiveTab] = useState("sessions");
  const [sessions, setSessions] = useState(mockSandboxSessions);
  const [resourceProfiles, setResourceProfiles] =
    useState(mockResourceProfiles);
  const [requests, setRequests] = useState(mockCreditRequests);
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingProfile, setEditingProfile] = useState<ResourceProfile | null>(
    null
  );
  const [platformQuotas, setPlatformQuotas] = useState(mockPlatformQuotas);

  const handleSaveProfile = (profile: ResourceProfile) => {
    if (profile.id && resourceProfiles.find((p) => p.id === profile.id)) {
      setResourceProfiles((prev) =>
        prev.map((p) => (p.id === profile.id ? profile : p))
      );
    } else {
      setResourceProfiles((prev) => [
        ...prev,
        { ...profile, id: Date.now().toString() },
      ]);
    }
  };

  const toggleProfileStatus = (profileId: string) => {
    setResourceProfiles((prev) =>
      prev.map((p) => (p.id === profileId ? { ...p, active: !p.active } : p))
    );
  };

  const terminateSession = (sessionId: string) => {
    setSessions((prev) =>
      prev.map((s) => (s.id === sessionId ? { ...s, status: "terminated" } : s))
    );
    toast.success("Session terminated successfully");
  };

  const extendSession = (sessionId: string, additionalMinutes: number) => {
    setSessions((prev) =>
      prev.map((s) =>
        s.id === sessionId
          ? { ...s, timeRemaining: s.timeRemaining + additionalMinutes }
          : s
      )
    );
    toast.success(`Session extended by ${additionalMinutes} minutes`);
  };

  const handleRequest = (
    requestId: string,
    action: "approve" | "reject",
    comments?: string
  ) => {
    setRequests((prev) =>
      prev.map((r) =>
        r.id === requestId
          ? {
              ...r,
              status: action === "approve" ? "approved" : "rejected",
              reviewedBy: "Current User",
              reviewDate: new Date().toISOString(),
              reviewComments: comments || "",
            }
          : r
      )
    );
    toast.success(`Request ${action}d successfully`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "text-green-600 bg-green-100";
      case "idle":
        return "text-yellow-600 bg-yellow-100";
      case "terminated":
        return "text-red-600 bg-red-100";
      case "suspended":
        return "text-gray-600 bg-gray-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "text-red-600 bg-red-100";
      case "high":
        return "text-orange-600 bg-orange-100";
      case "medium":
        return "text-yellow-600 bg-yellow-100";
      case "low":
        return "text-green-600 bg-green-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const filteredSessions = sessions.filter(
    (session) =>
      session.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.courseName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredProfiles = resourceProfiles.filter(
    (profile) =>
      profile.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      profile.instanceType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6 mt-8 ml-8">
      <div>
        <h1 className="font-bold text-2xl">Sandbox Management</h1>
        <p>
          Monitor and manage sandbox environments, resource allocation, and
          student requests
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab("sessions")}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === "sessions"
                ? "border-bits-blue text-bits-blue"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Active Sessions
          </button>
          <button
            onClick={() => setActiveTab("profiles")}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === "profiles"
                ? "border-bits-blue text-bits-blue"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Resource Profiles
          </button>
          <button
            onClick={() => setActiveTab("requests")}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === "requests"
                ? "border-bits-blue text-bits-blue"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Time Extension Requests
          </button>
        </nav>
      </div>

      {/* Sessions Tab */}
      {activeTab === "sessions" && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Terminal className="h-5 w-5" />
              Active Sandbox Sessions
            </CardTitle>
            <CardDescription>
              Monitor and manage currently running sandbox environments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search sessions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Course/Assignment</TableHead>
                      <TableHead>Environment</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Time Remaining</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSessions.map((session) => (
                      <TableRow key={session.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {session.studentName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {session.studentEmail}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {session.courseName}
                            </div>
                            {session.assignmentTitle && (
                              <div className="text-sm text-gray-500">
                                {session.assignmentTitle}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{session.environment}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(session.status)}>
                            {session.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {session.timeRemaining}m /{" "}
                            {session.totalTimeAllocated}m
                          </div>
                          <Progress
                            value={
                              (session.timeRemaining /
                                session.totalTimeAllocated) *
                              100
                            }
                            className="h-2 mt-1"
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {session.status === "active" && (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => extendSession(session.id, 30)}
                                >
                                  +30m
                                </Button>
                                <Button
                                  variant="default"
                                  size="sm"
                                  onClick={() => terminateSession(session.id)}
                                  className="bg-bits-red"
                                >
                                  Terminate
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Resource Profiles Tab */}
      {activeTab === "profiles" && (
        <div className="space-y-6">
          {/* Platform Quotas */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Platform Resource Quotas
              </CardTitle>
              <CardDescription>
                Monitor and configure platform-wide resource usage limits
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="max-sandboxes">Max Sandboxes per User</Label>
                  <Input
                    id="max-sandboxes"
                    type="number"
                    min="1"
                    value={platformQuotas.maxSandboxesPerUser}
                    onChange={(e) =>
                      setPlatformQuotas((prev) => ({
                        ...prev,
                        maxSandboxesPerUser: Number(e.target.value),
                      }))
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="total-vcpu">Total vCPU Pool</Label>
                  <Input
                    id="total-vcpu"
                    type="number"
                    min="10"
                    value={platformQuotas.totalvCPUPool}
                    onChange={(e) =>
                      setPlatformQuotas((prev) => ({
                        ...prev,
                        totalvCPUPool: Number(e.target.value),
                      }))
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="monthly-cap">Monthly Cost Cap (USD)</Label>
                  <Input
                    id="monthly-cap"
                    type="number"
                    min="100"
                    value={platformQuotas.monthlyCostCap}
                    onChange={(e) =>
                      setPlatformQuotas((prev) => ({
                        ...prev,
                        monthlyCostCap: Number(e.target.value),
                      }))
                    }
                  />
                </div>
              </div>

              <Separator className="my-6" />

              <div className="space-y-4">
                <h4 className="font-medium">Current Usage</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Active Sandboxes</span>
                      <span>
                        {platformQuotas.currentSandboxes}/
                        {platformQuotas.maxSandboxesPerUser * 100}
                      </span>
                    </div>
                    <Progress
                      value={
                        (platformQuotas.currentSandboxes /
                          (platformQuotas.maxSandboxesPerUser * 100)) *
                        100
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>vCPU Usage</span>
                      <span>
                        {platformQuotas.currentvCPU}/
                        {platformQuotas.totalvCPUPool}
                      </span>
                    </div>
                    <Progress
                      value={
                        (platformQuotas.currentvCPU /
                          platformQuotas.totalvCPUPool) *
                        100
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Monthly Cost</span>
                      <span>
                        ${platformQuotas.currentMonthlyCost}/$
                        {platformQuotas.monthlyCostCap}
                      </span>
                    </div>
                    <Progress
                      value={
                        (platformQuotas.currentMonthlyCost /
                          platformQuotas.monthlyCostCap) *
                        100
                      }
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end mt-6">
                <Button
                  onClick={() =>
                    toast.success("Platform quotas updated successfully!")
                  }
                  className="bg-bits-blue hover:bg-bits-blue/90"
                >
                  Update Quotas
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Resource Profiles */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Cpu className="h-5 w-5" />
                    Resource Profiles
                  </CardTitle>
                  <CardDescription>
                    Configure computational resource templates for sandbox
                    environments
                  </CardDescription>
                </div>
                <Button
                  onClick={() => setShowCreateModal(true)}
                  className="bg-bits-blue hover:bg-bits-blue/90"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Profile
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="relative flex-1 max-w-sm">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search profiles..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="grid gap-4">
                  {filteredProfiles.map((profile) => (
                    <Card
                      key={profile.id}
                      className={`border-l-4 ${
                        profile.active
                          ? "border-l-green-500 bg-green-50/30"
                          : "border-l-gray-400 bg-gray-50/30"
                      }`}
                    >
                      <CardContent className="pt-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-3">
                              <h3 className="font-medium">{profile.name}</h3>
                              <Badge variant="outline">
                                {profile.resourceType.toUpperCase()}
                              </Badge>
                              <Badge variant="outline">
                                {profile.instanceType}
                              </Badge>
                              {!profile.active && (
                                <Badge variant="secondary">Inactive</Badge>
                              )}
                            </div>

                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                              <div className="flex items-center gap-2">
                                <Cpu className="h-4 w-4 text-blue-600" />
                                <span className="text-sm">
                                  {profile.vCpu} vCPU
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <MemoryStick className="h-4 w-4 text-green-600" />
                                <span className="text-sm">
                                  {profile.ramGB}GB RAM
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <HardDrive className="h-4 w-4 text-orange-600" />
                                <span className="text-sm">
                                  {profile.diskGB}GB Disk
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <DollarSign className="h-4 w-4 text-purple-600" />
                                <span className="text-sm">
                                  ${profile.costBudgetUSD}
                                </span>
                              </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-gray-600" />
                                <span className="text-sm">
                                  Max: {profile.maxRuntimeHrs}h, Idle:{" "}
                                  {profile.idleTimeoutMin}m
                                </span>
                              </div>
                              {profile.gpuCount > 0 && (
                                <div className="flex items-center gap-2">
                                  <Zap className="h-4 w-4 text-yellow-600" />
                                  <span className="text-sm">
                                    {profile.gpuCount} GPU
                                  </span>
                                </div>
                              )}
                            </div>

                            <div className="space-y-3">
                              <div>
                                <span className="text-sm font-medium text-gray-700">
                                  Packages:{" "}
                                </span>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {profile.allowedPackages
                                    .slice(0, 5)
                                    .map((pkg) => (
                                      <Badge
                                        key={pkg}
                                        variant="outline"
                                        className="text-xs"
                                      >
                                        {pkg}
                                      </Badge>
                                    ))}
                                  {profile.allowedPackages.length > 5 && (
                                    <Badge
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      +{profile.allowedPackages.length - 5} more
                                    </Badge>
                                  )}
                                </div>
                              </div>

                              {profile.customPackages &&
                                profile.customPackages.length > 0 && (
                                  <div>
                                    <span className="text-sm font-medium text-gray-700">
                                      Custom:{" "}
                                    </span>
                                    <div className="flex flex-wrap gap-1 mt-1">
                                      {profile.customPackages
                                        .slice(0, 3)
                                        .map((pkg) => (
                                          <Badge
                                            key={pkg}
                                            variant="secondary"
                                            className="text-xs bg-blue-100 text-blue-800"
                                          >
                                            {pkg}
                                          </Badge>
                                        ))}
                                      {profile.customPackages.length > 3 && (
                                        <Badge
                                          variant="secondary"
                                          className="text-xs bg-blue-100 text-blue-800"
                                        >
                                          +{profile.customPackages.length - 3}{" "}
                                          more
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                )}
                            </div>
                          </div>

                          <div className="flex items-center gap-2 ml-4">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setEditingProfile(profile)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Switch
                              checked={profile.active}
                              onCheckedChange={() =>
                                toggleProfileStatus(profile.id)
                              }
                            />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Time Extension Requests Tab */}
      {activeTab === "requests" && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Time Extension Requests
            </CardTitle>
            <CardDescription>
              Review and approve student requests for additional sandbox time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {requests.map((request) => (
                <Card key={request.id} className="border-l-4 border-l-blue-500">
                  <CardContent className="pt-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <h4 className="font-medium">{request.studentName}</h4>
                          <Badge className={getPriorityColor(request.priority)}>
                            {request.priority}
                          </Badge>
                          <Badge
                            variant={
                              request.status === "pending"
                                ? "outline"
                                : request.status === "approved"
                                ? "default"
                                : "destructive"
                            }
                          >
                            {request.status}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div>
                            <span className="text-sm font-medium">Course:</span>
                            <div className="text-sm text-gray-600">
                              {request.courseName}
                            </div>
                          </div>
                          {request.assignmentTitle && (
                            <div>
                              <span className="text-sm font-medium">
                                Assignment:
                              </span>
                              <div className="text-sm text-gray-600">
                                {request.assignmentTitle}
                              </div>
                            </div>
                          )}
                          <div>
                            <span className="text-sm font-medium">
                              Current Time:
                            </span>
                            <div className="text-sm text-gray-600">
                              {request.currentTimeRemaining} minutes
                            </div>
                          </div>
                          <div>
                            <span className="text-sm font-medium">
                              Requested Time:
                            </span>
                            <div className="text-sm text-gray-600">
                              {request.requestedTime} minutes
                            </div>
                          </div>
                        </div>

                        <div className="mb-4">
                          <span className="text-sm font-medium">Reason:</span>
                          <div className="text-sm text-gray-600 mt-1 p-3 bg-gray-50 rounded-md">
                            {request.reason}
                          </div>
                        </div>
                      </div>

                      {request.status === "pending" && (
                        <div className="flex items-center gap-2 ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleRequest(
                                request.id,
                                "approve",
                                "Request approved"
                              )
                            }
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Approve
                          </Button>
                          <Button
                            variant="default"
                            size="sm"
                            className="bg-bits-red"
                            onClick={() =>
                              handleRequest(
                                request.id,
                                "reject",
                                "Request denied"
                              )
                            }
                          >
                            <XCircle className="h-4 w-4 mr-1" />
                            Reject
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Create/Edit Profile Modal */}
      <EditResourceProfileModal
        isOpen={showCreateModal || editingProfile !== null}
        onClose={() => {
          setShowCreateModal(false);
          setEditingProfile(null);
        }}
        profile={editingProfile || undefined}
        onSave={handleSaveProfile}
      />
    </div>
  );
}
