import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Progress } from "../components/ui/progress";
import SegmentedProgress from "../components/ui/SegmantProgress";
import {
  BookOpen,
  Clock,
  CheckCircle,
  AlertCircle,
  Users,
  Play,
  Terminal,
  Calendar,
  MessageSquare,
  RefreshCw,
  Award,
  FileText,
  User,
  ExternalLink,
} from "lucide-react";
import { useAuth,useNavigation } from "../App";
// import useAuth from "../components/Context/AuthContext";
// import useNavigation from "../components/Context/NavigationContext";
import { toast } from "sonner";
import ProgressLegend from "../components/ui/ProgressLegend";
import { EnhancedProjectsResponseData, useEnhancedProjectsQuery } from "../api/projectManagementApi";
import { UseQueryOptions, QueryKey } from "@tanstack/react-query";
import { tokenService } from "../services/TokenService";
import { useNavigate } from "react-router-dom";
import path from "path";
import paths from "../routes";

interface Project {
  id: string;
  title: string;
  description: string;
  subject: string;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
  status: "not_started" | "in_progress" | "submitted" | "graded";
  progress: {
    status: "pending" | "ongoing" | "submitted" | "overdue" | "graded";
  }[];
  totalCheckpoints: number;
  dueDate: string;
  totalPoints: number;
  grade?: string;
  sandboxStatus: "stopped" | "provisioning" | "active" | "error";
  hasDataset: boolean;
}

interface RecentActivity {
  id: string;
  type: "submission" | "grade" | "announcement" | "message";
  title: string;
  description: string;
  timestamp: string;
  course?: string;
  urgent?: boolean;
}

interface DashboardStats {
  activeProjects: number;
  averageGrade: number;
}

interface ProjectInResponse {
  course: { id: string; name: string; code: string; term: string },
  difficultyLevel: string,
  dueDate: string | null,
  id:string,
  status: string,
  submissionCount: number,
  title: string,
  userSubmission: any
}
interface ProjectResponse {
  pagination: { currentPage: number; totalPages: number; totalItems: number,itemsPerPage: number },
  projects: ProjectInResponse[];
}


const mockRecentActivity: RecentActivity[] = [
  {
    id: "act_1",
    type: "grade",
    title: "Assignment Graded",
    description: "Customer Segmentation Analysis",
    timestamp: "2025-01-16T14:30:00Z",
    course: "Data Analytics",
  },
  {
    id: "act_2",
    type: "submission",
    title: "Assignment Submitted",
    description: "Sentiment Analysis project submitted successfully",
    timestamp: "2025-01-15T16:45:00Z",
    course: "Natural Language Processing",
  },
  {
    id: "act_3",
    type: "announcement",
    title: "New Assignment Posted",
    description: "Neural Networks - Introduction to Deep Learning",
    timestamp: "2025-01-16T10:00:00Z",
    course: "Deep Learning",
  },
];

const mockStats: DashboardStats = {
  activeProjects: 2,
  averageGrade: 8.2,
};

function ProjectCard({
  project,
  onViewProject,
}: // onLaunchSandbox,
{
  project: Project;
  onViewProject: (projectId: string) => void;
  //onLaunchSandbox: (projectId: string) => void;
}) {
  const handleViewProject = (e: React.MouseEvent) => {
    e.stopPropagation();
    onViewProject(project.id);
  };

  // const handleLaunchSandbox = (e: React.MouseEvent) => {
  //   e.stopPropagation();
  //   onLaunchSandbox(project.id);
  // };

  const getStatusDisplay = () => {
    switch (project.status) {
      case "in_progress":
        return {
          icon: <RefreshCw className="h-4 w-4" />,
          text: "In Progress",
          color: "text-bits-blue",
        };
      case "submitted":
        return {
          icon: <CheckCircle className="h-4 w-4" />,
          text: "Submitted",
          color: "text-bits-gold",
        };
      case "graded":
        return {
          icon: <Award className="h-4 w-4" />,
          text: "Graded",
          color: "text-bits-blue",
        };
      default:
        return {
          icon: <Clock className="h-4 w-4" />,
          text: "Not Started",
          color: "text-muted-foreground",
        };
    }
  };

  const getDueDateDisplay = () => {
    const now = new Date();
    const due = new Date(project.dueDate);
    const isOverdue = now > due;

    const startDate = new Date("2025-06-12T00:00:00Z");
    const endDate = due;

    const formattedStartDate = `${startDate.getDate()} ${startDate.toLocaleString(
      "default",
      {
        month: "short",
      }
    )}`;
    const formattedEndDate = `${endDate.getDate()} ${endDate.toLocaleString(
      "default",
      {
        month: "short",
      }
    )} ${endDate.getFullYear()}`;

    const dateRange = `${formattedStartDate} - ${formattedEndDate}`;

    return {
      text: dateRange,
      isOverdue: isOverdue,
    };
  };

  const dueDateDisplay = getDueDateDisplay();

  return (
    <Card className="cursor-pointer hover:shadow-lg transition-all duration-200 border border-border rounded-lg h-full flex flex-col">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold pr-2 line-clamp-2">
            {project.title}
          </CardTitle>
        </div>
        <div>
          <CardDescription className="text-sm text-muted-foreground mt-2 mr-4 line-clamp-2">
            {project.description}
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="pt-0 flex flex-col flex-grow">
        <div className="space-y-4 flex flex-col justify-between h-full">
          {/* Progress section */}
          {project.status !== "graded" && (
            <div className="space-y-2">
              <span className="text-base text-muted-foreground">Progress</span>
              <SegmentedProgress
                progress={project.progress}
                totalCheckpoints={project.totalCheckpoints}
              />
            </div>
          )}
          {/* Bottom section */}
          <div className="flex items-center justify-between pt-2 mt-auto">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span className="text-muted-foreground font-semibold">
                {dueDateDisplay.text}
                {dueDateDisplay.isOverdue && (
                  <span className="text-bits-red ml-1">(Overdue)</span>
                )}
              </span>
            </div>
            <Button
              onClick={handleViewProject}
              className={`bg-bits-green hover:bg-green-700 whitespace-nowrap text-white px-4 py-2 rounded-md`}
            >
              <ExternalLink className="h-4 w-4" />
              Open
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function ActivityItem({
  icon,
  title,
  description,
  timeAgo,
  backgroundColor,
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  timeAgo: string;
  backgroundColor: string;
}) {
  return (
    <div
      className={`${backgroundColor} rounded-lg flex gap-3 p-4 items-start`}
    >
      <div className={`h-6 w-6 mt-1`}>{icon}</div>
      <div className="flex items-start gap-3">
        <div className="flex-1">
          <p className="text-sm font-semibold">{title}</p>
          <p className="text-sm text-muted-foreground mr-1">{description}</p>
        </div>
        <div>
          <p className="text-xs text-muted-foreground mt-4">{timeAgo}</p>
        </div>
      </div>
    </div>
  );
}
// gap-3 py-3
export default function StudentDashboard() {
  const { user } = useAuth();
  const navigate = useNavigate()
  const { navigateTo } = useNavigation();
const [token, setToken] = useState<string | null>(tokenService.getToken());
   useEffect(() => {
    const unsubscribe = tokenService.subscribe((newToken) => {
      setToken(newToken);
    });
    
    return unsubscribe;
  }, []);


  // const [projects] = useState<Project[]>(mockProjects);
  
  const { data: enhancedProjectsData, isLoading, isError } = useEnhancedProjectsQuery(
    { token }, 
    ({
      onSuccess: (response: EnhancedProjectsResponseData) => {
        console.log(response, "response ins here");
        console.log("Projects fetched successfully");
      },
      onError: (err: unknown) => {
        console.log("projects error", err);
      },
      retry: false,
    } as Omit<
      UseQueryOptions<
        EnhancedProjectsResponseData,
        Error,
        EnhancedProjectsResponseData,
        QueryKey
      >,
      "queryKey" | "queryFn"
    >)
  );
 // const [projects] = useState<Project[]>(mockProjects);

  const handleViewProject = (projectId: string) => {
    console.log(projectId, "projectId");
    console.log("we reached here")
    navigate(`/project-details/${projectId}`)
  //  navigateTo("project-details", { projectId });
  };
  const handleViewAllProjects = () => {
   // navigateTo("allprojects");
    navigate(paths.allProjects)
  };
  

  // const handleLaunchSandbox = (projectId: string) => {
  //   const project = projects.find((p) => p.id === projectId);
  //   if (!project) return;

  //   if (project.sandboxStatus === "active") {
  //     navigateTo("sandbox", { projectId, sandboxId: `sandbox_${projectId}` });
  //   } else {
  //     toast.success("Starting sandbox environment...", {
  //       description: "Your development environment is being prepared.",
  //     });

  //     setTimeout(() => {
  //       toast.success("Sandbox environment ready!", {
  //         description: "You can now start working on your project.",
  //       });
  //       navigateTo("sandbox", { projectId, sandboxId: `sandbox_${projectId}` });
  //     }, 3000);
  //   }
  // };
console.log(enhancedProjectsData, "enhancedProjectsData");
  
  return (
    <div className="p-6 space-y-6">
      {/* Welcome Header */}
      <div className="mb-8">
        <h1 className="text-3xl leading-[38px] tracking-normal font-semibold text-black mb-2 font-inter">
          Welcome back, {user?.name}
        </h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Your Projects */}
        <div className="lg:col-span-2">
          <Card className="border border-border rounded-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-lg font-semibold">
                  <BookOpen className="h-5 w-5 text-bits-blue" />
                  Your Projects
                </CardTitle>
                <button
                  onClick={handleViewAllProjects}
                  className="hover:underline border whitespace-nowrap px-2 text-lg font-semibold rounded-md flex items-center gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  View All
                </button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6">
                {isLoading ? (
                  <div className="p-6 text-center">
                    <div className="text-lg font-medium text-gray-600">Loading projects...</div>
                  </div>
                ) : isError ? (
                  <div className="p-6 text-center">
                    <div className="text-lg font-medium text-red-600">Failed to load projects</div>
                    <div className="text-sm text-gray-500 mt-2">Please try refreshing the page</div>
                  </div>
                ) : (
                  (enhancedProjectsData as EnhancedProjectsResponseData | undefined)?.projects
                    ?.slice(0, 2)
                    ?.map((project: any) => (
                      <ProjectCard
                        key={project.id}
                        project={project}
                        onViewProject={handleViewProject}
                        //   onLaunchSandbox={handleLaunchSandbox}
                      />
                    ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Upcoming Deadlines */}
          <Card className="border border-border rounded-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg font-semibold">
                <Clock className="h-5 w-5 text-bits-warning" />
                Upcoming Deadlines
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 rounded-lg gap-3">
                  <div>
                    <p className="text-base">Customer Segment Analysis</p>
                  </div>
                  <div className="text-right">
                    <span className="text-sm text-bits-red font-medium flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      29 days pending
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card className="border border-border rounded-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg font-semibold">
                <MessageSquare className="h-5 w-5 text-bits-blue" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                <ActivityItem
                  icon={<CheckCircle className="h-4 w-4 text-bits-green" />}
                  title="Assignment Graded"
                  description="Customer Segmentation Analysis Grade: 80"
                  timeAgo="2 hours ago"
                  backgroundColor="bg-gray-50"
                />

                <ActivityItem
                  icon={<CheckCircle className="h-4 w-4 text-blue-500" />}
                  title="Assignment Submitted"
                  description="Sentiment Analysis project submitted successfully"
                  timeAgo="2 hours ago"
                  backgroundColor="bg-blue-50"
                />

                <ActivityItem
                  icon={<FileText className="h-4 w-4 text-bits-warming " />}
                  title="New Assignment Posted"
                  description="Neural Networks - Introduction to Deep Learning"
                  timeAgo="2 hours ago"
                  backgroundColor="bg-yellow-50"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
