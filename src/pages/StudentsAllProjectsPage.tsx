import React, { useState,useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Progress } from "../components/ui/progress";
import SegmentedProgress from "../components/ui/SegmantProgress";
import {
  BookOpen,
  Clock,
  CheckCircle,
  AlertCircle,
  Users,
  Play,
  Terminal,
  Calendar,
  MessageSquare,
  RefreshCw,
  Award,
  FileText,
  User,
  ExternalLink,
  ArrowLeft,
} from "lucide-react";
import { useAuth, useNavigation } from "../App";
// import useAuth from "../components/Context/AuthContext";
// import useNavigation from "../components/Context/NavigationContext";
import { toast } from "sonner";
import ProgressLegend from "../components/ui/ProgressLegend";
import { useNavigate } from "react-router-dom";
import paths from "../routes";
import { EnhancedProjectsResponseData, useEnhancedProjectsQuery } from "../api/projectManagementApi";
import { tokenService } from "../services/TokenService";
import { UseQueryOptions,QueryKey } from "@tanstack/react-query";

interface Project {
  id: string;
  title: string;
  description?: string;
  subject?: string;
  difficulty?: "Beginner" | "Intermediate" | "Advanced";
  status: "not_started" | "in_progress" | "submitted" | "graded";
  progress: Progress[];
  totalCheckpoints: number;
  dueDate: string;
  totalPoints: number;
  grade?: string;
  sandboxStatus: "stopped" | "provisioning" | "active" | "error";
  hasDataset: boolean;
  startDate?:string|null;
  projectId?:string
}

interface Progress {
  status: "pending" | "ongoing" | "submitted" | "overdue" | "graded";
  checkpoint: string;
  startDate: string;
  endDate: string;
}

// const mockProjects: Project[] = [
//   {
//     id: "proj_1",
//     title: "Gold Price Prediction",
//     description: "Build a machine learning model to predict gold prices",
//     subject: "Machine Learning",
//     difficulty: "Intermediate",
//     status: "in_progress",
//     progress: [
//       {
//         status: "submitted",
//         checkpoint: "data visualization",
//         startDate: "2025-08-01T09:00:00Z",
//         endDate: "2025-08-02T09:00:00Z",
//       },
//       {
//         status: "submitted",
//         checkpoint: "data visualization",
//         startDate: "2025-08-02T09:00:00Z",
//         endDate: "2025-08-03T09:00:00Z",
//       },
//       {
//         status: "overdue",
//         checkpoint: "data visualization",
//         startDate: "2025-08-03T09:00:00Z",
//         endDate: "2025-08-04T09:00:00Z",
//       },
//       {
//         status: "ongoing",
//         checkpoint: "data visualization",
//         startDate: "2025-08-04T09:00:00Z",
//         endDate: "2025-08-05T09:00:00Z",
//       },
//       {
//         status: "pending",
//         checkpoint: "data visualization",
//         startDate: "2025-08-05T09:00:00Z",
//         endDate: "2025-08-06T09:00:00Z",
//       },
//       {
//         status: "pending",
//         checkpoint: "data visualization",
//         startDate: "2025-08-06T09:00:00Z",
//         endDate: "2025-08-07T09:00:00Z",
//       },
//       {
//         status: "pending",
//         checkpoint: "data visualization",
//         startDate: "2025-08-07T09:00:00Z",
//         endDate: "2025-08-08T09:00:00Z",
//       },
//       {
//         status: "pending",
//         checkpoint: "data visualization",
//         startDate: "2025-08-08T09:00:00Z",
//         endDate: "2025-08-09T09:00:00Z",
//       },
//     ],
//     totalCheckpoints: 8,
//     dueDate: "2025-09-29T23:59:00Z",
//     totalPoints: 100,
//     sandboxStatus: "active",
//     hasDataset: true,
//   },
//   {
//     id: "proj_2",
//     title: "Customer Segment Analysis",
//     description:
//       "Perform clustering analysis to segment customers based on purchasing behavior.",
//     subject: "Data Analytics",
//     difficulty: "Beginner",
//     status: "submitted",
//     progress: [
//       {
//         status: "graded",
//         checkpoint: "data visualization",
//         startDate: "2025-01-10T10:00:00Z",
//         endDate: "2025-01-11T10:00:00Z",
//       },
//       {
//         status: "submitted",
//         checkpoint: "data visualization",
//         startDate: "2025-01-11T10:00:00Z",
//         endDate: "2025-01-12T10:00:00Z",
//       },
//       {
//         status: "overdue",
//         checkpoint: "data visualization",
//         startDate: "2025-01-12T10:00:00Z",
//         endDate: "2025-01-13T10:00:00Z",
//       },
//       {
//         status: "overdue",
//         checkpoint: "data visualization",
//         startDate: "2025-01-13T10:00:00Z",
//         endDate: "2025-01-14T10:00:00Z",
//       },
//       {
//         status: "pending",
//         checkpoint: "data visualization",
//         startDate: "2025-01-14T10:00:00Z",
//         endDate: "2025-01-15T10:00:00Z",
//       },
//       {
//         status: "pending",
//         checkpoint: "data visualization",
//         startDate: "2025-01-15T10:00:00Z",
//         endDate: "2025-01-16T10:00:00Z",
//       },
//     ],
//     totalCheckpoints: 6,
//     dueDate: "2025-01-15T23:59:00Z",
//     totalPoints: 80,
//     sandboxStatus: "stopped",
//     hasDataset: true,
//   },
//   {
//     id: "proj_3",
//     title: "Sentiment Analysis",
//     description:
//       "Build a text classification model to analyze sentiment in social media posts.",
//     subject: "Natural Language Processing",
//     difficulty: "Intermediate",
//     status: "graded",
//     progress: [
//       {
//         status: "graded",
//         checkpoint: "data visualization",
//         startDate: "2025-01-01T09:00:00Z",
//         endDate: "2025-01-02T09:00:00Z",
//       },
//       {
//         status: "graded",
//         checkpoint: "data visualization",
//         startDate: "2025-01-02T09:00:00Z",
//         endDate: "2025-01-03T09:00:00Z",
//       },
//       {
//         status: "graded",
//         checkpoint: "data visualization",
//         startDate: "2025-01-03T09:00:00Z",
//         endDate: "2025-01-04T09:00:00Z",
//       },
//       {
//         status: "graded",
//         checkpoint: "data visualization",
//         startDate: "2025-01-04T09:00:00Z",
//         endDate: "2025-01-05T09:00:00Z",
//       },
//       {
//         status: "graded",
//         checkpoint: "data visualization",
//         startDate: "2025-01-05T09:00:00Z",
//         endDate: "2025-01-06T09:00:00Z",
//       },
//       {
//         status: "graded",
//         checkpoint: "data visualization",
//         startDate: "2025-01-06T09:00:00Z",
//         endDate: "2025-01-07T09:00:00Z",
//       },
//       {
//         status: "graded",
//         checkpoint: "data visualization",
//         startDate: "2025-01-07T09:00:00Z",
//         endDate: "2025-01-08T09:00:00Z",
//       },
//       {
//         status: "graded",
//         checkpoint: "data visualization",
//         startDate: "2025-01-08T09:00:00Z",
//         endDate: "2025-01-09T09:00:00Z",
//       },
//     ],
//     totalCheckpoints: 8,
//     dueDate: "2025-01-18T23:59:00Z",
//     totalPoints: 90,
//     grade: "A",
//     sandboxStatus: "stopped",
//     hasDataset: true,
//   },
// ];

function ProjectCard({
  project,
  onViewProject,
  onLaunchSandbox,
}: {
  project: Project;
  onViewProject: (projectId: string) => void;
  onLaunchSandbox: (projectId: string) => void;
}) {
const navigate = useNavigate()
  console.log(project.id)
  const handleViewProject = (e: React.MouseEvent) => {
navigate(paths.projectDetails.replace(":projectId", project.id));

  };

  const handleLaunchSandbox = (e: React.MouseEvent) => {
    e.stopPropagation();
    onLaunchSandbox(project.id)
  };

  const getStatusDisplay = () => {
    switch (project.status) {
      case "in_progress":
        return {
          icon: <RefreshCw className="h-4 w-4" />,
          text: "In Progress",
          color: "text-bits-blue",
        };
      case "submitted":
        return {
          icon: <CheckCircle className="h-4 w-4" />,
          text: "Submitted",
          color: "text-bits-gold",
        };
      case "graded":
        return {
          icon: <Award className="h-4 w-4" />,
          text: "Graded",
          color: "text-bits-blue",
        };
      default:
        return {
          icon: <Clock className="h-4 w-4" />,
          text: "Not Started",
          color: "text-muted-foreground",
        };
    }
  };
console.log(project)
const getDueDateDisplay = () => {
  const now = new Date();
  const due = project.dueDate ? new Date(project.dueDate) : null;
  const start = project.startDate ? new Date(project.startDate) : null;

  if (!due) return { text: "No due date", isOverdue: false };

  const isOverdue = now > due;

  // Format start date
  const formattedStartDate = start
    ? `${start.getDate()} ${start.toLocaleString("default", { month: "short" })}`
    : "TBD"; // fallback if startDate is null

  // Format end date
  const formattedEndDate = `${due.getDate()} ${due.toLocaleString("default", { month: "short" })} ${due.getFullYear()}`;

  const dateRange = `${formattedStartDate} - ${formattedEndDate}`;

  return { text: dateRange, isOverdue };
};

  const dueDateDisplay = getDueDateDisplay();

  return (
    <Card className="overflow-hidden cursor-pointer hover:shadow-lg transition-all duration-200 border border-border rounded-lg h-full flex flex-col">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold pr-2 line-clamp-2">
            {project?.title}
          </CardTitle>
        </div>
        <div>
          <CardDescription className="text-sm text-muted-foreground mt-2">
            {project?.description}
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="pt-0 flex flex-col flex-grow">
        <div className="space-y-4 flex flex-col justify-between h-full">
          {/* Progress section */}
          <div className="space-y-2">
            <span className="text-base text-muted-foreground">Progress</span>
            <SegmentedProgress
              progress={project?.progress}
              totalCheckpoints={project?.totalCheckpoints}
            />
          </div>
          {/* Bottom section */}
          <div className="flex items-center justify-between pt-2 mt-auto">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span className="text-muted-foreground font-semibold">
                {dueDateDisplay?.text}
                {dueDateDisplay?.isOverdue && (
                  <span className="text-bits-red ml-1">(Overdue)</span>
                )}
              </span>
            </div>
            <Button
              onClick={handleViewProject}
              className={`bg-bits-green hover:bg-green-700 whitespace-nowrap text-white px-4 py-2 rounded-md`}
            >
              <ExternalLink className="h-4 w-4" />
              Open
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function StudentsAllProjectsPage() {
  const { user } = useAuth();
  const { navigateTo, goBack } = useNavigation();
const navigate = useNavigate()
  // const [projects] = useState<Project[]>(mockProjects);
  
  const handleViewProject = (projectId: string) => {
    console.log("here")
    navigate(`/project-details/${projectId}`)
   // navigateTo("project-details", { projectId });
  };
  const [token, setToken] = useState<string | null>(tokenService.getToken());
    useEffect(() => {
      const unsubscribe = tokenService.subscribe((newToken) => {
        setToken(newToken);
      });
      
      return unsubscribe;
    }, []);
  
const { data: enhancedProjectsData, isLoading, isError } = useEnhancedProjectsQuery<EnhancedProjectsResponseData>(
    { token }, 
    ({
      onSuccess: (response: EnhancedProjectsResponseData) => {
        console.log(response, "response ins here");
        console.log("Projects fetched successfully");
      },
      onError: (err: unknown) => {
        console.log("projects error", err);
      },
      retry: false,
    } as Omit<
      UseQueryOptions<
        EnhancedProjectsResponseData,
        Error,
        EnhancedProjectsResponseData,
        QueryKey
      >,
      "queryKey" | "queryFn"
    >)
  );

  const handleLaunchSandbox = (projectId: string) => {
    const project = projects.find((p) => p.id === projectId);
    if (!project) return;

    if (project.sandboxStatus === "active") {
      navigateTo("sandbox", { projectId, sandboxId: `sandbox_${projectId}` });
    } else {
      toast.success("Starting sandbox environment...", {
        description: "Your development environment is being prepared.",
      });

      setTimeout(() => {
        toast.success("Sandbox environment ready!", {
          description: "You can now start working on your project.",
        });
        navigateTo("sandbox", { projectId, sandboxId: `sandbox_${projectId}` });
      }, 3000);
    }
  };
  const navigateBack = ()=>{
    navigate(paths.root)
  }
  console.log(enhancedProjectsData)
    return (
    <div className="p-6 space-y-6">
      <div className="flex items-center gap-3">
        <button
          onClick={navigateBack}
          className="p-2 ml-2 rounded-full hover:bg-gray-100"
        >
          <ArrowLeft className="h-6 w-6 text-gray-700" />
        </button>
        <h1 className="flex shrink-0 text-3xl tracking-normal font-semibold text-black mb-2 font-inter">
          All Projects
        </h1>

        <div className="flex-1 justify-items-end p-4">
          <ProgressLegend
            className="flex flex-wrap items-center gap-5 mx-4 text-xs text-muted-foreground"
            barWidth="w-6"
            barHeight="h-1"
          />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-8">
    {isLoading ? (
      <div className="col-span-2 flex justify-center items-center p-6">
        <p className="text-gray-700 text-lg">Loading projects...</p>
      </div>
    ) : isError ? (
      <div className="col-span-2 flex flex-col justify-center items-center p-6">
        <p className="text-red-600 text-lg">Failed to load projects.</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    ) : !enhancedProjectsData?.projects?.length ? (
      <div className="col-span-2 flex justify-center items-center p-6">
        <p className="text-gray-500 text-lg">No projects found.</p>
      </div>
    ) : (
      enhancedProjectsData.projects.map((project) => (
        <ProjectCard
          key={project.id}
          project={project}
          onViewProject={handleViewProject}
          onLaunchSandbox={handleLaunchSandbox}
        />
      ))
    )}

      </div>
    </div>
  );
}
