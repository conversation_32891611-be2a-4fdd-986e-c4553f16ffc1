import React, { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { But<PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Progress } from "../components/ui/progress";
import {
  Tabs,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "../components/ui/tabs";
import {
  ArrowLeft,
  Calendar,
  Clock,
  User,
  FileText,
  Download,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  MessageSquare,
  Star,
  Code,
  Database,
  BarChart3,
  Activity,
  GitBranch,
  Timer,
  Award,
  TrendingUp,
  Eye,
  PlayCircle,
  FileCode,
  Mail,
  Phone,
  GraduationCap,
  BookOpen,
  Target,
  Zap,
  Folder,
  Upload,
} from "lucide-react";
import { useAuth, useNavigation } from "../App";

interface SubmissionDetails {
  id: string;
  studentId: string;
  studentName: string;
  studentEmail: string;
  studentIdNumber: string;
  studentPhone?: string;
  courseName: string;
  courseCode: string;
  assignmentName: string;
  assignmentId: string;
  assignmentDescription: string;
  submittedAt: string;
  dueDate: string;
  timeSpent: string;
  isLateSubmission: boolean;
  lateBy?: string;
  autoGrade?: number;
  finalGrade?: number;
  maxPoints: number;
  lastActivity: string;
  submissionAttempts: number;
  status: "submitted" | "grading" | "graded" | "returned";
  instructorId?: string;
  instructorName?: string;
  gradedAt?: string;
}

interface SubmissionFile {
  id: string;
  name: string;
  type: "notebook" | "dataset" | "script" | "output" | "report";
  size: number;
  lastModified: string;
  path: string;
  executionCount?: number;
}

interface GradingHistory {
  id: string;
  action:
    | "submitted"
    | "auto_graded"
    | "instructor_reviewed"
    | "graded"
    | "returned"
    | "commented";
  timestamp: string;
  actor: string;
  actorRole: "student" | "instructor" | "system";
  details: string;
  grade?: number;
}

interface PerformanceMetrics {
  codeQuality: number;
  executionSuccess: number;
  testsPassed: number;
  totalTests: number;
  documentationScore: number;
  algorithmicComplexity:
    | "O(1)"
    | "O(log n)"
    | "O(n)"
    | "O(n log n)"
    | "O(n²)"
    | "Unknown";
  memoryUsage: string;
  executionTime: string;
}

interface Comment {
  id: string;
  authorId: string;
  authorName: string;
  authorRole: "instructor" | "ta" | "student";
  content: string;
  createdAt: string;
  type: "comment" | "suggestion" | "error" | "praise";
  cellId?: string;
  isResolved?: boolean;
}

// Mock data
const mockSubmissionDetails: SubmissionDetails = {
  id: "sub_001",
  studentId: "student_1",
  studentName: "Rahul Sharma",
  studentEmail: "<EMAIL>",
  studentIdNumber: "2021A7PS001P",
  studentPhone: "+91 98765 43210",
  courseName: "Data Science Fundamentals",
  courseCode: "CS F301",
  assignmentName: "Housing Price Prediction Analysis",
  assignmentId: "assign_001",
  assignmentDescription:
    "Implement a machine learning model to predict housing prices using the California housing dataset. The assignment involves data preprocessing, feature engineering, model training, and evaluation.",
  submittedAt: "2025-01-15T14:30:00Z",
  dueDate: "2025-01-15T23:59:00Z",
  timeSpent: "4h 25m",
  isLateSubmission: false,
  autoGrade: 87,
  finalGrade: 92,
  maxPoints: 100,
  lastActivity: "2025-01-15T14:28:00Z",
  submissionAttempts: 1,
  status: "graded",
  instructorId: "instructor_1",
  instructorName: "Dr. A. Sharma",
  gradedAt: "2025-01-16T10:15:00Z",
};

const mockSubmissionFiles: SubmissionFile[] = [
  {
    id: "file_1",
    name: "housing_analysis.ipynb",
    type: "notebook",
    size: 45120,
    lastModified: "2025-01-15T14:30:00Z",
    path: "/submissions/rahul/housing_analysis.ipynb",
    executionCount: 25,
  },
  {
    id: "file_2",
    name: "housing_data.csv",
    type: "dataset",
    size: 2457600,
    lastModified: "2025-01-15T09:15:00Z",
    path: "/submissions/rahul/housing_data.csv",
  },
  {
    id: "file_3",
    name: "cleaned_housing.csv",
    type: "dataset",
    size: 1843200,
    lastModified: "2025-01-15T13:45:00Z",
    path: "/submissions/rahul/cleaned_housing.csv",
  },
  {
    id: "file_4",
    name: "data_preprocessing.py",
    type: "script",
    size: 8192,
    lastModified: "2025-01-15T12:30:00Z",
    path: "/submissions/rahul/data_preprocessing.py",
  },
  {
    id: "file_5",
    name: "model_results.html",
    type: "report",
    size: 12288,
    lastModified: "2025-01-15T14:25:00Z",
    path: "/submissions/rahul/model_results.html",
  },
];

const mockGradingHistory: GradingHistory[] = [
  {
    id: "hist_1",
    action: "submitted",
    timestamp: "2025-01-15T14:30:00Z",
    actor: "Rahul Sharma",
    actorRole: "student",
    details: "Assignment submitted successfully",
  },
  {
    id: "hist_2",
    action: "auto_graded",
    timestamp: "2025-01-15T14:31:00Z",
    actor: "System",
    actorRole: "system",
    details: "Automatic grading completed",
    grade: 87,
  },
  {
    id: "hist_3",
    action: "instructor_reviewed",
    timestamp: "2025-01-16T09:45:00Z",
    actor: "Dr. A. Sharma",
    actorRole: "instructor",
    details: "Started manual review and grading",
  },
  {
    id: "hist_4",
    action: "commented",
    timestamp: "2025-01-16T10:00:00Z",
    actor: "Dr. A. Sharma",
    actorRole: "instructor",
    details: "Added feedback comments to notebook cells",
  },
  {
    id: "hist_5",
    action: "graded",
    timestamp: "2025-01-16T10:15:00Z",
    actor: "Dr. A. Sharma",
    actorRole: "instructor",
    details: "Final grading completed",
    grade: 92,
  },
  {
    id: "hist_6",
    action: "returned",
    timestamp: "2025-01-16T10:16:00Z",
    actor: "System",
    actorRole: "system",
    details: "Assignment returned to student with feedback",
  },
];

const mockPerformanceMetrics: PerformanceMetrics = {
  codeQuality: 88,
  executionSuccess: 95,
  testsPassed: 18,
  totalTests: 20,
  documentationScore: 85,
  algorithmicComplexity: "O(n)",
  memoryUsage: "245 MB",
  executionTime: "12.5s",
};

const mockComments: Comment[] = [
  {
    id: "comment_1",
    authorId: "instructor_1",
    authorName: "Dr. A. Sharma",
    authorRole: "instructor",
    content:
      "Excellent data preprocessing approach! The way you handled missing values is very thorough.",
    createdAt: "2025-01-16T10:00:00Z",
    type: "praise",
    cellId: "cell_3",
  },
  {
    id: "comment_2",
    authorId: "instructor_1",
    authorName: "Dr. A. Sharma",
    authorRole: "instructor",
    content:
      "Consider adding feature scaling before training the model. This could improve performance.",
    createdAt: "2025-01-16T10:05:00Z",
    type: "suggestion",
    cellId: "cell_8",
  },
  {
    id: "comment_3",
    authorId: "instructor_1",
    authorName: "Dr. A. Sharma",
    authorRole: "instructor",
    content:
      "Great visualization! The correlation heatmap clearly shows the relationships between features.",
    createdAt: "2025-01-16T10:10:00Z",
    type: "praise",
    cellId: "cell_12",
  },
];

function StudentInformationCard({
  submission,
}: {
  submission: SubmissionDetails;
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <User className="h-5 w-5 mr-2 text-bits-blue" />
          Student Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Full Name
              </label>
              <p className="text-base">{submission.studentName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Student ID
              </label>
              <p className="text-base font-mono">
                {submission.studentIdNumber}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Email
              </label>
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <p className="text-base">{submission.studentEmail}</p>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            {submission.studentPhone && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Phone
                </label>
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <p className="text-base">{submission.studentPhone}</p>
                </div>
              </div>
            )}
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Course
              </label>
              <div className="flex items-center space-x-2">
                <BookOpen className="h-4 w-4 text-muted-foreground" />
                <p className="text-base">
                  {submission.courseName} ({submission.courseCode})
                </p>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Submission Status
              </label>
              <Badge
                className={`${
                  submission.status === "graded"
                    ? "bg-green-600"
                    : submission.status === "grading"
                    ? "bg-orange-600"
                    : submission.status === "returned"
                    ? "bg-purple-600"
                    : "bg-blue-600"
                } text-white`}
              >
                {submission.status.charAt(0).toUpperCase() +
                  submission.status.slice(1)}
              </Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function AssignmentDetailsCard({
  submission,
}: {
  submission: SubmissionDetails;
}) {
  const { navigateTo } = useNavigation();

  const timeAgo = (date: string) => {
    const now = new Date();
    const past = new Date(date);
    const diffInHours = Math.floor(
      (now.getTime() - past.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <GraduationCap className="h-5 w-5 mr-2 text-bits-blue" />
            Assignment Details
          </div>
          <Button
            size="sm"
            onClick={() =>
              navigateTo("grading-sandbox", {
                submissionId: submission.id,
                studentId: submission.studentId,
                projectId: submission.assignmentId,
                mode: "grading",
              })
            }
            className="bg-bits-blue hover:bg-bits-blue/90"
          >
            <Eye className="h-4 w-4 mr-2" />
            View Notebook
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="text-sm font-medium text-muted-foreground">
            Assignment Name
          </label>
          <p className="text-lg font-medium">{submission.assignmentName}</p>
        </div>

        <div>
          <label className="text-sm font-medium text-muted-foreground">
            Description
          </label>
          <p className="text-base text-muted-foreground">
            {submission.assignmentDescription}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Due Date
              </label>
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <p className="text-base">
                  {new Date(submission.dueDate).toLocaleString()}
                </p>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Submitted At
              </label>
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <p className="text-base">
                  {new Date(submission.submittedAt).toLocaleString()}
                </p>
                {submission.isLateSubmission && (
                  <Badge variant="destructive" className="text-xs ml-2">
                    Late
                  </Badge>
                )}
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Time Spent
              </label>
              <div className="flex items-center space-x-2">
                <Timer className="h-4 w-4 text-muted-foreground" />
                <p className="text-base">{submission.timeSpent}</p>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Attempts
              </label>
              <div className="flex items-center space-x-2">
                <Target className="h-4 w-4 text-muted-foreground" />
                <p className="text-base">{submission.submissionAttempts}</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function GradingInformationCard({
  submission,
}: {
  submission: SubmissionDetails;
}) {
  const getGradeColor = (grade: number, max: number) => {
    const percentage = (grade / max) * 100;
    if (percentage >= 90) return "text-bits-gold";
    if (percentage >= 80) return "text-bits-blue";
    if (percentage >= 70) return "text-bits-gold";
    if (percentage >= 60) return "text-bits-gold";
    return "text-bits-red";
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Award className="h-5 w-5 mr-2 text-bits-gold" />
          Grading Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {submission.autoGrade || 0}
            </div>
            <div className="text-sm text-muted-foreground">Auto Grade</div>
            <div className="text-xs text-muted-foreground mt-1">
              / {submission.maxPoints}
            </div>
          </div>

          {submission.finalGrade && (
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div
                className={`text-2xl font-bold ${getGradeColor(
                  submission.finalGrade,
                  submission.maxPoints
                )}`}
              >
                {submission.finalGrade}
              </div>
              <div className="text-sm text-muted-foreground">Final Grade</div>
              <div className="text-xs text-muted-foreground mt-1">
                / {submission.maxPoints}
              </div>
            </div>
          )}

          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div
              className={`text-2xl font-bold ${getGradeColor(
                submission.finalGrade || submission.autoGrade || 0,
                submission.maxPoints
              )}`}
            >
              {Math.round(
                ((submission.finalGrade || submission.autoGrade || 0) /
                  submission.maxPoints) *
                  100
              )}
              %
            </div>
            <div className="text-sm text-muted-foreground">Percentage</div>
          </div>
        </div>

        {submission.instructorName && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">
              Graded By
            </label>
            <p className="text-base">{submission.instructorName}</p>
          </div>
        )}

        {submission.gradedAt && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">
              Graded At
            </label>
            <p className="text-base">
              {new Date(submission.gradedAt).toLocaleString()}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function SubmissionFilesCard({ files }: { files: SubmissionFile[] }) {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case "notebook":
        return <FileCode className="h-4 w-4 text-bits-gold" />;
      case "dataset":
        return <Database className="h-4 w-4 text-bits-blue" />;
      case "script":
        return <Code className="h-4 w-4 text-bits-gold" />;
      case "report":
        return <FileText className="h-4 w-4 text-bits-gold" />;
      default:
        return <FileText className="h-4 w-4 text-bits-blue" />;
    }
  };

  const getFileTypeBadge = (type: string) => {
    const colors = {
      notebook: "bg-bits-gold/10 text-bits-gold",
      dataset: "bg-bits-blue/10 text-bits-blue",
      script: "bg-bits-gold/10 text-bits-gold",
      report: "bg-bits-gold/10 text-bits-gold",
      output: "bg-muted text-muted-foreground",
    };
    return (
      colors[type as keyof typeof colors] || "bg-muted text-muted-foreground"
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Folder className="h-5 w-5 mr-2 text-bits-blue" />
          Submission Files
        </CardTitle>
        <CardDescription>{files.length} files submitted</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {files.map((file) => (
            <div
              key={file.id}
              className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                {getFileIcon(file.type)}
                <div>
                  <p className="font-medium">{file.name}</p>
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <span>{formatFileSize(file.size)}</span>
                    <span>•</span>
                    <span>{new Date(file.lastModified).toLocaleString()}</span>
                    {file.executionCount && (
                      <>
                        <span>•</span>
                        <span>{file.executionCount} executions</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={`text-xs ${getFileTypeBadge(file.type)}`}>
                  {file.type}
                </Badge>
                <Button size="sm" variant="outline">
                  <Download className="h-3 w-3 mr-1" />
                  Download
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

function PerformanceMetricsCard({ metrics }: { metrics: PerformanceMetrics }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <BarChart3 className="h-5 w-5 mr-2 text-bits-blue" />
          Performance Metrics
        </CardTitle>
        <CardDescription>
          Automated analysis of code quality and execution
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Code Quality</span>
                <span>{metrics.codeQuality}%</span>
              </div>
              <Progress value={metrics.codeQuality} className="h-2" />
            </div>

            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Execution Success</span>
                <span>{metrics.executionSuccess}%</span>
              </div>
              <Progress value={metrics.executionSuccess} className="h-2" />
            </div>

            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Documentation Score</span>
                <span>{metrics.documentationScore}%</span>
              </div>
              <Progress value={metrics.documentationScore} className="h-2" />
            </div>
          </div>

          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Tests Passed
              </label>
              <p className="text-lg font-medium">
                {metrics.testsPassed} / {metrics.totalTests}
                <span className="text-sm text-muted-foreground ml-2">
                  (
                  {Math.round((metrics.testsPassed / metrics.totalTests) * 100)}
                  %)
                </span>
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Algorithmic Complexity
              </label>
              <Badge variant="outline" className="mt-1">
                {metrics.algorithmicComplexity}
              </Badge>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Memory Usage
                </label>
                <p className="text-base">{metrics.memoryUsage}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Execution Time
                </label>
                <p className="text-base">{metrics.executionTime}</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function GradingHistoryCard({ history }: { history: GradingHistory[] }) {
  const getActionIcon = (action: string) => {
    switch (action) {
      case "submitted":
        return <Upload className="h-4 w-4 text-bits-blue" />;
      case "auto_graded":
        return <Zap className="h-4 w-4 text-bits-gold" />;
      case "instructor_reviewed":
        return <Eye className="h-4 w-4 text-bits-gold" />;
      case "commented":
        return <MessageSquare className="h-4 w-4 text-bits-gold" />;
      case "graded":
        return <Award className="h-4 w-4 text-bits-gold" />;
      case "returned":
        return <CheckCircle className="h-4 w-4 text-bits-gold" />;
      default:
        return <Activity className="h-4 w-4 text-bits-blue" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case "submitted":
        return "bg-bits-blue/10 border-bits-blue";
      case "auto_graded":
        return "bg-bits-gold/10 border-bits-gold";
      case "instructor_reviewed":
        return "bg-bits-gold/10 border-bits-gold";
      case "commented":
        return "bg-bits-gold/10 border-bits-gold";
      case "graded":
        return "bg-bits-gold/10 border-bits-gold";
      case "returned":
        return "bg-bits-gold/10 border-bits-gold";
      default:
        return "bg-muted border-border";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Activity className="h-5 w-5 mr-2 text-bits-blue" />
          Grading History
        </CardTitle>
        <CardDescription>
          Timeline of submission and grading activities
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {history.map((item, index) => (
            <div
              key={item.id}
              className={`p-3 border rounded-lg ${getActionColor(item.action)}`}
            >
              <div className="flex items-start space-x-3">
                {getActionIcon(item.action)}
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <p className="font-medium text-sm">{item.details}</p>
                    {item.grade && (
                      <Badge className="bg-bits-blue text-white text-xs">
                        Grade: {item.grade}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <span>{item.actor}</span>
                    <span>•</span>
                    <span>{new Date(item.timestamp).toLocaleString()}</span>
                    <span>•</span>
                    <Badge variant="outline" className="text-xs">
                      {item.actorRole}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

function CommentsCard({ comments }: { comments: Comment[] }) {
  const getCommentTypeColor = (type: string) => {
    switch (type) {
      case "suggestion":
        return "text-blue-600 bg-blue-50 border-blue-200";
      case "error":
        return "text-red-600 bg-red-50 border-red-200";
      case "praise":
        return "text-green-600 bg-green-50 border-green-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const getCommentIcon = (type: string) => {
    switch (type) {
      case "suggestion":
        return <MessageSquare className="h-4 w-4" />;
      case "error":
        return <AlertCircle className="h-4 w-4" />;
      case "praise":
        return <Star className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <MessageSquare className="h-5 w-5 mr-2 text-bits-blue" />
          Instructor Comments
        </CardTitle>
        <CardDescription>
          {comments.length} comments and feedback
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {comments.map((comment) => (
            <div
              key={comment.id}
              className={`p-3 border rounded-lg ${getCommentTypeColor(
                comment.type
              )}`}
            >
              <div className="flex items-start space-x-3">
                {getCommentIcon(comment.type)}
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-sm">
                        {comment.authorName}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {comment.authorRole}
                      </Badge>
                      <Badge variant="outline" className="text-xs capitalize">
                        {comment.type}
                      </Badge>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {new Date(comment.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  <p className="text-sm">{comment.content}</p>
                  {comment.cellId && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Cell: {comment.cellId}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export default function SubmissionDetailsPage() {
  const { user } = useAuth();
  const { goBack, pageParams, navigateTo } = useNavigation();

  const submissionId = pageParams?.submissionId || "sub_001";
  const [submission] = useState<SubmissionDetails>(mockSubmissionDetails);
  const [files] = useState<SubmissionFile[]>(mockSubmissionFiles);
  const [gradingHistory] = useState<GradingHistory[]>(mockGradingHistory);
  const [performanceMetrics] = useState<PerformanceMetrics>(
    mockPerformanceMetrics
  );
  const [comments] = useState<Comment[]>(mockComments);

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={goBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-bits-blue">
              Submission Details
            </h1>
            <p className="text-muted-foreground mt-1">
              {submission.studentName} • {submission.assignmentName}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={() =>
              window.open(`mailto:${submission.studentEmail}`, "_blank")
            }
          >
            <Mail className="h-4 w-4 mr-2" />
            Contact Student
          </Button>
          <Button
            onClick={() =>
              navigateTo("grading-sandbox", {
                submissionId: submission.id,
                studentId: submission.studentId,
                projectId: submission.assignmentId,
                mode: "grading",
              })
            }
            className="bg-bits-blue hover:bg-bits-blue/90"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Open Grading Environment
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="files">Files</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="feedback">Feedback</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <StudentInformationCard submission={submission} />
            <GradingInformationCard submission={submission} />
          </div>
          <AssignmentDetailsCard submission={submission} />
        </TabsContent>

        <TabsContent value="files" className="space-y-6">
          <SubmissionFilesCard files={files} />
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <PerformanceMetricsCard metrics={performanceMetrics} />
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <GradingHistoryCard history={gradingHistory} />
        </TabsContent>

        <TabsContent value="feedback" className="space-y-6">
          <CommentsCard comments={comments} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
