import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { Textarea } from "../components/ui/textarea";
import {
  ArrowLeft,
  Download,
  Eye,
  Search,
  Filter,
  GraduationCap,
  Clock,
  CheckCircle,
  XCircle,
  User,
  FileText,
  MessageSquare,
  BookOpen,
  Code,
  AlertCircle,
  Calendar,
  TrendingUp,
  Save,
  X,
} from "lucide-react";
import { useAuth,useNavigation,useUtilities } from "../App";
// import useAuth from "../components/Context/AuthContext";
// import useNavigation from "../components/Context/NavigationContext";
//import useUtilities from "../components/Context/UtilityContext";
import { SubmissionNavigationProvider } from "../contexts/SubmissionNavigationContext";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../components/ui/dialog";
import { Progress } from "../components/ui/progress";
import { toast } from "sonner";

// Rubric Interface
interface RubricItem {
  id: string;
  criteria: string;
  description: string;
  points: number;
  earnedPoints?: number;
}

interface Submission {
  id: string;
  studentName: string;
  studentEmail: string;
  studentId: string;
  submittedAt: string;
  status: "submitted" | "graded" | "late";
  grade?: string;
  feedback?: string;
  assignmentId: string;
  assignmentTitle: string;
  course: string;
  files: {
    name: string;
    size: string;
    type: string;
  }[];
  notebookUrl?: string;
  comments?: string;
  gradeBreakdown?: {
    criteria: string;
    points: number;
    maxPoints: number;
    feedback?: string;
  }[];
  rubric?: RubricItem[];
}

interface Assignment {
  id: string;
  title: string;
  course: string;
  dueDate: string;
  totalStudents: number;
  rubric: RubricItem[];
}

// Mock data with enhanced rubric structure
const mockAssignments: { [key: string]: Assignment } = {
  "1": {
    id: "1",
    title: "Linear Regression Analysis",
    course: "Data Science 101",
    dueDate: "2025-01-15T23:59:00Z",
    totalStudents: 30,
    rubric: [
      {
        id: "lr_r1",
        criteria: "Data Exploration & Visualization",
        points: 20,
        description:
          "Comprehensive exploration of the dataset with meaningful visualizations",
      },
      {
        id: "lr_r2",
        criteria: "Data Preprocessing",
        points: 20,
        description:
          "Proper handling of missing values, outliers, and data transformation",
      },
      {
        id: "lr_r3",
        criteria: "Feature Engineering",
        points: 25,
        description: "Creation of meaningful features and feature selection",
      },
      {
        id: "lr_r4",
        criteria: "Model Implementation",
        points: 25,
        description:
          "Implementation of multiple regression models with proper validation",
      },
      {
        id: "lr_r5",
        criteria: "Analysis & Interpretation",
        points: 10,
        description: "Clear interpretation of results and model performance",
      },
    ],
  },
  "2": {
    id: "2",
    title: "Neural Network Implementation",
    course: "Machine Learning Advanced",
    dueDate: "2025-01-20T23:59:00Z",
    totalStudents: 25,
    rubric: [
      {
        id: "nn_r1",
        criteria: "Architecture Design",
        points: 25,
        description:
          "Proper neural network architecture design and justification",
      },
      {
        id: "nn_r2",
        criteria: "Implementation",
        points: 30,
        description:
          "Correct implementation of neural networks from scratch or with libraries",
      },
      {
        id: "nn_r3",
        criteria: "Training & Optimization",
        points: 25,
        description:
          "Effective training procedures and optimization techniques",
      },
      {
        id: "nn_r4",
        criteria: "Code Quality & Documentation",
        points: 20,
        description: "Clean, well-documented, and reproducible code",
      },
    ],
  },
  "3": {
    id: "3",
    title: "Data Visualization Project",
    course: "Data Science 101",
    dueDate: "2025-01-25T23:59:00Z",
    totalStudents: 30,
    rubric: [
      {
        id: "dv_r1",
        criteria: "Visualization Design",
        points: 40,
        description: "Creative and effective visualization design principles",
      },
      {
        id: "dv_r2",
        criteria: "Data Storytelling",
        points: 35,
        description:
          "Clear narrative and meaningful insights from visualizations",
      },
      {
        id: "dv_r3",
        criteria: "Technical Implementation",
        points: 25,
        description:
          "Proper use of visualization libraries and interactive features",
      },
    ],
  },
};

// Enhanced mock submissions with rubric data
const mockSubmissions: Submission[] = [
  {
    id: "1",
    studentName: "Rahul Sharma",
    studentEmail: "<EMAIL>",
    studentId: "2023001",
    submittedAt: "2025-01-12T10:30:00Z",
    status: "submitted",
    assignmentId: "1",
    assignmentTitle: "Linear Regression Analysis",
    course: "Data Science 101",
    files: [
      {
        name: "linear_regression_analysis.ipynb",
        size: "2.3 MB",
        type: "notebook",
      },
      { name: "analysis_report.pdf", size: "1.1 MB", type: "document" },
    ],
    notebookUrl: "notebooks/rahul_linear_regression.ipynb",
    comments:
      "Implemented all required algorithms and included extra analysis on feature importance.",
    rubric: [
      {
        id: "lr_r1",
        criteria: "Data Exploration & Visualization",
        description:
          "Comprehensive exploration of the dataset with meaningful visualizations",
        points: 20,
        earnedPoints: 0,
      },
      {
        id: "lr_r2",
        criteria: "Data Preprocessing",
        description:
          "Proper handling of missing values, outliers, and data transformation",
        points: 20,
        earnedPoints: 0,
      },
      {
        id: "lr_r3",
        criteria: "Feature Engineering",
        description: "Creation of meaningful features and feature selection",
        points: 25,
        earnedPoints: 0,
      },
      {
        id: "lr_r4",
        criteria: "Model Implementation",
        description:
          "Implementation of multiple regression models with proper validation",
        points: 25,
        earnedPoints: 0,
      },
      {
        id: "lr_r5",
        criteria: "Analysis & Interpretation",
        description: "Clear interpretation of results and model performance",
        points: 10,
        earnedPoints: 0,
      },
    ],
  },
  {
    id: "2",
    studentName: "Rahul Sharma",
    studentEmail: "<EMAIL>",
    studentId: "2023001",
    submittedAt: "2025-01-13T14:20:00Z",
    status: "graded",
    grade: "A",
    feedback:
      "Excellent work! Great analysis and clear explanations. Minor suggestion: could explore more feature engineering techniques.",
    assignmentId: "2",
    assignmentTitle: "Neural Network Implementation",
    course: "Machine Learning Advanced",
    files: [
      { name: "ml_project_submission.ipynb", size: "3.1 MB", type: "notebook" },
      { name: "final_report.pdf", size: "1.8 MB", type: "document" },
      { name: "presentation.pptx", size: "5.2 MB", type: "presentation" },
    ],
    notebookUrl: "notebooks/rahul_neural_network.ipynb",
    rubric: [
      {
        id: "nn_r1",
        criteria: "Architecture Design",
        description:
          "Proper neural network architecture design and justification",
        points: 25,
        earnedPoints: 23,
      },
      {
        id: "nn_r2",
        criteria: "Implementation",
        description:
          "Correct implementation of neural networks from scratch or with libraries",
        points: 30,
        earnedPoints: 28,
      },
      {
        id: "nn_r3",
        criteria: "Training & Optimization",
        description:
          "Effective training procedures and optimization techniques",
        points: 25,
        earnedPoints: 23,
      },
      {
        id: "nn_r4",
        criteria: "Code Quality & Documentation",
        description: "Clean, well-documented, and reproducible code",
        points: 20,
        earnedPoints: 18,
      },
    ],
  },
  {
    id: "3",
    studentName: "Rahul Sharma",
    studentEmail: "<EMAIL>",
    studentId: "2023001",
    submittedAt: "2025-01-16T08:15:00Z",
    status: "late",
    assignmentId: "3",
    assignmentTitle: "Data Visualization Project",
    course: "Data Science 101",
    files: [
      { name: "visualization_project.ipynb", size: "1.9 MB", type: "notebook" },
    ],
    notebookUrl: "notebooks/rahul_visualization.ipynb",
    comments:
      "Sorry for the late submission. Had to deal with some technical issues but included comprehensive visualizations.",
    rubric: [
      {
        id: "dv_r1",
        criteria: "Visualization Design",
        description: "Creative and effective visualization design principles",
        points: 40,
        earnedPoints: 0,
      },
      {
        id: "dv_r2",
        criteria: "Data Storytelling",
        description:
          "Clear narrative and meaningful insights from visualizations",
        points: 35,
        earnedPoints: 0,
      },
      {
        id: "dv_r3",
        criteria: "Technical Implementation",
        description:
          "Proper use of visualization libraries and interactive features",
        points: 25,
        earnedPoints: 0,
      },
    ],
  },
  // Non-student submissions for instructor/admin view
  {
    id: "4",
    studentName: "Priya Patel",
    studentEmail: "<EMAIL>",
    studentId: "2023002",
    submittedAt: "2025-01-13T14:20:00Z",
    status: "graded",
    grade: "A",
    feedback:
      "Excellent work! Great analysis and clear explanations. Minor suggestion: could explore more feature engineering techniques.",
    assignmentId: "1",
    assignmentTitle: "Linear Regression Analysis",
    course: "Data Science 101",
    files: [
      { name: "ml_project_submission.ipynb", size: "3.1 MB", type: "notebook" },
      { name: "final_report.pdf", size: "1.8 MB", type: "document" },
      { name: "presentation.pptx", size: "5.2 MB", type: "presentation" },
    ],
    notebookUrl: "notebooks/priya_linear_regression.ipynb",
    rubric: [
      {
        id: "lr_r1",
        criteria: "Data Exploration & Visualization",
        description:
          "Comprehensive exploration of the dataset with meaningful visualizations",
        points: 20,
        earnedPoints: 18,
      },
      {
        id: "lr_r2",
        criteria: "Data Preprocessing",
        description:
          "Proper handling of missing values, outliers, and data transformation",
        points: 20,
        earnedPoints: 17,
      },
      {
        id: "lr_r3",
        criteria: "Feature Engineering",
        description: "Creation of meaningful features and feature selection",
        points: 25,
        earnedPoints: 23,
      },
      {
        id: "lr_r4",
        criteria: "Model Implementation",
        description:
          "Implementation of multiple regression models with proper validation",
        points: 25,
        earnedPoints: 24,
      },
      {
        id: "lr_r5",
        criteria: "Analysis & Interpretation",
        description: "Clear interpretation of results and model performance",
        points: 10,
        earnedPoints: 8,
      },
    ],
  },
];

// Advanced Rubric-Based Grading Modal Component (same as GradebookPage)
function RubricGradingModal({
  open,
  onOpenChange,
  submission,
  assignment,
  onSave,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  submission: Submission;
  assignment: Assignment;
  onSave: (gradeData: any) => void;
}) {
  const [rubricGrades, setRubricGrades] = useState<{ [key: string]: number }>(
    {}
  );
  const [feedback, setFeedback] = useState(submission?.feedback || "");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize rubric grades when modal opens
  React.useEffect(() => {
    if (submission?.rubric) {
      const initialGrades: { [key: string]: number } = {};
      submission.rubric.forEach((item: RubricItem) => {
        initialGrades[item.id] = item.earnedPoints || 0;
      });
      setRubricGrades(initialGrades);
    }
  }, [submission]);

  const handleRubricChange = (rubricId: string, value: string) => {
    const numValue = parseFloat(value) || 0;
    setRubricGrades((prev) => ({
      ...prev,
      [rubricId]: numValue,
    }));
  };

  const calculateTotalScore = () => {
    const totalEarned = Object.values(rubricGrades).reduce(
      (sum, points) => sum + points,
      0
    );
    const totalPossible =
      submission?.rubric?.reduce(
        (sum: number, item: RubricItem) => sum + item.points,
        0
      ) || 0;
    return { totalEarned, totalPossible };
  };

  const calculateLetterGrade = (percentage: number) => {
    if (percentage >= 95) return "A";
    if (percentage >= 90) return "A-";
    if (percentage >= 87) return "B+";
    if (percentage >= 83) return "B";
    if (percentage >= 80) return "B-";
    if (percentage >= 77) return "C+";
    if (percentage >= 73) return "C";
    if (percentage >= 70) return "C-";
    if (percentage >= 67) return "D+";
    if (percentage >= 65) return "D";
    return "F";
  };

  const handleSave = async () => {
    // Validate all rubric items have been graded
    const ungraded = submission?.rubric?.filter(
      (item: RubricItem) =>
        !rubricGrades[item.id] && rubricGrades[item.id] !== 0
    );

    if (ungraded?.length > 0) {
      toast.error("Please grade all rubric criteria before saving");
      return;
    }

    // Validate points don't exceed maximum
    const invalidGrades = submission?.rubric?.filter(
      (item: RubricItem) => rubricGrades[item.id] > item.points
    );

    if (invalidGrades?.length > 0) {
      toast.error("Points cannot exceed maximum for any criteria");
      return;
    }

    setIsSubmitting(true);

    try {
      const { totalEarned, totalPossible } = calculateTotalScore();
      const percentage =
        totalPossible > 0 ? (totalEarned / totalPossible) * 100 : 0;
      const letterGrade = calculateLetterGrade(percentage);

      const gradeData = {
        submissionId: submission.id,
        studentId: submission.studentId,
        assignmentId: submission.assignmentId,
        score: totalEarned,
        maxScore: totalPossible,
        letterGrade,
        feedback,
        rubricGrades,
        gradedAt: new Date().toISOString(),
      };

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      onSave(gradeData);
      onOpenChange(false);

      toast.success(`Grade saved successfully for ${submission.studentName}`);

      // Reset form
      setRubricGrades({});
      setFeedback("");
    } catch (error) {
      toast.error("Failed to save grade. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    // Reset form
    if (submission?.rubric) {
      const initialGrades: { [key: string]: number } = {};
      submission.rubric.forEach((item: RubricItem) => {
        initialGrades[item.id] = item.earnedPoints || 0;
      });
      setRubricGrades(initialGrades);
    }
    setFeedback(submission?.feedback || "");
  };

  const { totalEarned, totalPossible } = calculateTotalScore();
  const percentage =
    totalPossible > 0 ? (totalEarned / totalPossible) * 100 : 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Grade Submission - Rubric-Based Grading</DialogTitle>
          <DialogDescription>
            Grade {submission.assignmentTitle} for {submission.studentName}{" "}
            using the assignment rubric
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Student Info */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-bits-blue rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="font-medium">{submission.studentName}</p>
                <p className="text-sm text-muted-foreground">
                  {submission.studentEmail}
                </p>
              </div>
            </div>
          </div>

          {/* Assignment Info */}
          <div className="p-4 border rounded-lg">
            <div className="flex items-center space-x-3">
              <FileText className="h-5 w-5 text-bits-blue" />
              <div>
                <p className="font-medium">{submission.assignmentTitle}</p>
                <p className="text-sm text-muted-foreground">
                  {submission.course} • Status: {submission.status}
                </p>
              </div>
            </div>
          </div>

          {/* Rubric Grading */}
          <div className="space-y-4">
            <h3 className="font-medium">Grading Rubric</h3>

            {submission?.rubric?.map((item: RubricItem, index: number) => (
              <div key={item.id} className="border rounded-lg p-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{item.criteria}</h4>
                      <p className="text-xs text-muted-foreground mt-1">
                        {item.description}
                      </p>
                    </div>
                    <div className="text-right ml-4">
                      <p className="text-xs text-muted-foreground">
                        Max: {item.points} pts
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Label
                      htmlFor={`rubric-${item.id}`}
                      className="text-sm min-w-0"
                    >
                      Points:
                    </Label>
                    <Input
                      id={`rubric-${item.id}`}
                      type="number"
                      min="0"
                      max={item.points}
                      step="0.5"
                      value={rubricGrades[item.id] || ""}
                      onChange={(e) =>
                        handleRubricChange(item.id, e.target.value)
                      }
                      className="w-20"
                      placeholder="0"
                    />
                    <span className="text-sm text-muted-foreground">
                      / {item.points}
                    </span>
                  </div>

                  {/* Progress bar showing completion */}
                  <Progress
                    value={
                      rubricGrades[item.id]
                        ? (rubricGrades[item.id] / item.points) * 100
                        : 0
                    }
                    className="h-2"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Total Score Summary */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Total Score</p>
                <p className="text-xs text-muted-foreground">
                  {totalEarned.toFixed(1)} / {totalPossible} points
                </p>
              </div>
              <div className="text-right">
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-bold">
                    {percentage.toFixed(1)}%
                  </span>
                  <Badge variant="outline" className="text-sm">
                    {calculateLetterGrade(percentage)}
                  </Badge>
                </div>
              </div>
            </div>
            <Progress value={percentage} className="mt-2 h-2" />
          </div>

          {/* Feedback */}
          <div className="space-y-2">
            <Label htmlFor="feedback">Overall Feedback (Optional)</Label>
            <Textarea
              id="feedback"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Enter overall feedback for the student..."
              rows={4}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={isSubmitting}
              className="bg-bits-blue hover:bg-bits-blue/90"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSubmitting ? "Saving..." : "Save Grade"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Student-specific view showing only their submissions
function StudentSubmissionsView({
  submissions,
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  courseFilter,
  setCourseFilter,
  selectedSubmission,
  setSelectedSubmission,
}: {
  submissions: Submission[];
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  courseFilter: string;
  setCourseFilter: (course: string) => void;
  selectedSubmission: Submission | null;
  setSelectedSubmission: (submission: Submission | null) => void;
}) {
  const { navigateTo } = useNavigation();
  const { downloadSubmission } = useUtilities();

  const filteredSubmissions = submissions.filter((submission) => {
    const matchesSearch =
      submission.assignmentTitle
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      submission.course.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" || submission.status === statusFilter;
    const matchesCourse =
      courseFilter === "all" || submission.course === courseFilter;
    return matchesSearch && matchesStatus && matchesCourse;
  });

  const submissionStats = {
    total: submissions.length,
    submitted: submissions.filter((s) => s.status === "submitted").length,
    graded: submissions.filter((s) => s.status === "graded").length,
    late: submissions.filter((s) => s.status === "late").length,
  };

  const courses = Array.from(new Set(submissions.map((s) => s.course)));

  const getStatusColor = (status: string) => {
    switch (status) {
      case "submitted":
        return "bg-blue-100 text-blue-800";
      case "graded":
        return "bg-green-100 text-green-800";
      case "late":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case "notebook":
        return "📊";
      case "document":
        return "📄";
      case "presentation":
        return "📽️";
      default:
        return "📎";
    }
  };

  const handleViewNotebook = (submission: Submission) => {
    navigateTo("notebook", {
      projectId: submission.assignmentId,
      projectTitle: submission.assignmentTitle,
      context: "submission-review",
      submissionId: submission.id,
    });
  };

  const handleDownloadFiles = (submission: Submission) => {
    downloadSubmission(submission.id, {
      id: submission.id,
      assignmentTitle: submission.assignmentTitle,
      studentName: submission.studentName,
      submissionDate: submission.submittedAt,
      status: submission.status,
      grade: submission.grade,
      feedback: submission.feedback,
      files: submission.files,
      notebooks: submission.notebookUrl ? [submission.notebookUrl] : [],
      datasets: [],
    });
  };

  const calculateGPA = () => {
    const gradedSubmissions = submissions.filter(
      (s) => s.rubric && s.status === "graded"
    );
    if (gradedSubmissions.length === 0) return 0;

    let totalPoints = 0;
    let maxTotalPoints = 0;

    gradedSubmissions.forEach((submission) => {
      if (submission.rubric) {
        submission.rubric.forEach((item) => {
          totalPoints += item.earnedPoints || 0;
          maxTotalPoints += item.points;
        });
      }
    });

    return maxTotalPoints > 0
      ? ((totalPoints / maxTotalPoints) * 4.0).toFixed(2)
      : "0.00";
  };

  return (
    <>
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-bits-blue">
                  {submissionStats.total}
                </div>
                <p className="text-sm text-muted-foreground">
                  Total Submissions
                </p>
              </div>
              <FileText className="h-8 w-8 text-bits-blue" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {submissionStats.submitted}
                </div>
                <p className="text-sm text-muted-foreground">Awaiting Grade</p>
              </div>
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {submissionStats.graded}
                </div>
                <p className="text-sm text-muted-foreground">Graded</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-orange-600">
                  {calculateGPA()}
                </div>
                <p className="text-sm text-muted-foreground">Current GPA</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Submissions List */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>My Submissions</CardTitle>
                <div className="flex space-x-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search assignments..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-9 w-64"
                    />
                  </div>
                  <Select value={courseFilter} onValueChange={setCourseFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Courses</SelectItem>
                      {courses.map((course) => (
                        <SelectItem key={course} value={course}>
                          {course}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="submitted">Submitted</SelectItem>
                      <SelectItem value="graded">Graded</SelectItem>
                      <SelectItem value="late">Late</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredSubmissions.map((submission) => (
                  <div
                    key={submission.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors hover:bg-gray-50 ${
                      selectedSubmission?.id === submission.id
                        ? "ring-2 ring-bits-blue"
                        : ""
                    }`}
                    onClick={() => setSelectedSubmission(submission)}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-bits-blue rounded-full flex items-center justify-center">
                          <BookOpen className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <p className="font-medium">
                            {submission.assignmentTitle}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {submission.course}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getStatusColor(submission.status)}>
                          {submission.status}
                        </Badge>
                        {submission.grade && (
                          <Badge variant="outline" className="font-bold">
                            {submission.grade}
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span>
                            {new Date(
                              submission.submittedAt
                            ).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span>{submission.files.length} files</span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDownloadFiles(submission);
                          }}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                        {submission.notebookUrl && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleViewNotebook(submission);
                            }}
                            className="bg-bits-blue text-white hover:bg-bits-blue/90"
                          >
                            <Code className="h-4 w-4 mr-2" />
                            View Work
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                {filteredSubmissions.length === 0 && (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      No submissions found matching your criteria
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Submission Details Sidebar */}
        <div>
          {selectedSubmission ? (
            <Card>
              <CardHeader>
                <CardTitle>Submission Details</CardTitle>
                <CardDescription>
                  {selectedSubmission.assignmentTitle}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Assignment Info</h4>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="font-medium text-sm">
                      {selectedSubmission.assignmentTitle}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {selectedSubmission.course}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Submitted:{" "}
                      {new Date(
                        selectedSubmission.submittedAt
                      ).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Submitted Files</h4>
                  <div className="space-y-2">
                    {selectedSubmission.files.map((file, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 border rounded"
                      >
                        <div className="flex items-center space-x-2">
                          <span>{getFileIcon(file.type)}</span>
                          <div>
                            <p className="text-sm font-medium truncate">
                              {file.name}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {file.size}
                            </p>
                          </div>
                        </div>
                        <Button size="sm" variant="ghost">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                {selectedSubmission.notebookUrl && (
                  <div>
                    <h4 className="font-medium mb-2">Notebook</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full bg-bits-blue text-white hover:bg-bits-blue/90"
                      onClick={() => handleViewNotebook(selectedSubmission)}
                    >
                      <Code className="h-4 w-4 mr-2" />
                      View My Work
                    </Button>
                  </div>
                )}

                {selectedSubmission.comments && (
                  <div>
                    <h4 className="font-medium mb-2">My Comments</h4>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm">{selectedSubmission.comments}</p>
                    </div>
                  </div>
                )}

                {selectedSubmission.rubric &&
                  selectedSubmission.status === "graded" && (
                    <div>
                      <h4 className="font-medium mb-2">Grade Breakdown</h4>
                      <div className="space-y-2">
                        {selectedSubmission.rubric.map((item, index) => (
                          <div key={index} className="p-3 border rounded-lg">
                            <div className="flex justify-between items-center">
                              <p className="text-sm font-medium">
                                {item.criteria}
                              </p>
                              <span className="text-sm">
                                {item.earnedPoints}/{item.points} pts
                              </span>
                            </div>
                            <Progress
                              value={
                                item.earnedPoints
                                  ? (item.earnedPoints / item.points) * 100
                                  : 0
                              }
                              className="mt-2 h-2"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                {selectedSubmission.feedback && (
                  <div>
                    <h4 className="font-medium mb-2">Instructor Feedback</h4>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <p className="text-sm text-green-800">
                        {selectedSubmission.feedback}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-8">
                <div className="text-center text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-4" />
                  <p>Select a submission to view details</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </>
  );
}

// Instructor/Admin view for all submissions
function InstructorSubmissionsView({
  allSubmissions,
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  courseFilter,
  setCourseFilter,
}: {
  allSubmissions: Submission[];
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  courseFilter: string;
  setCourseFilter: (course: string) => void;
}) {
  const { navigateTo } = useNavigation();
  const [submissions, setSubmissions] = useState(allSubmissions);
  const [showGradingModal, setShowGradingModal] = useState(false);
  const [selectedSubmission, setSelectedSubmission] =
    useState<Submission | null>(null);
  const [selectedAssignment, setSelectedAssignment] =
    useState<Assignment | null>(null);

  const filteredSubmissions = submissions.filter((submission) => {
    const matchesSearch =
      submission.assignmentTitle
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      submission.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      submission.course.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" || submission.status === statusFilter;
    const matchesCourse =
      courseFilter === "all" || submission.course === courseFilter;
    return matchesSearch && matchesStatus && matchesCourse;
  });

  const courses = Array.from(new Set(allSubmissions.map((s) => s.course)));

  const getStatusColor = (status: string) => {
    switch (status) {
      case "submitted":
        return "bg-blue-100 text-blue-800";
      case "graded":
        return "bg-green-100 text-green-800";
      case "late":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleGradeSubmission = (submission: Submission) => {
    const assignment = mockAssignments[submission.assignmentId];
    if (assignment) {
      setSelectedSubmission(submission);
      setSelectedAssignment(assignment);
      setShowGradingModal(true);
    }
  };

  const handleSaveGrade = (gradeData: any) => {
    // Update the submission with new grade data
    setSubmissions((prevSubmissions) =>
      prevSubmissions.map((submission) =>
        submission.id === gradeData.submissionId
          ? {
              ...submission,
              status: "graded" as const,
              grade: gradeData.letterGrade,
              feedback: gradeData.feedback,
              rubric: submission.rubric?.map((item: RubricItem) => ({
                ...item,
                earnedPoints: gradeData.rubricGrades[item.id] || 0,
              })),
            }
          : submission
      )
    );
  };

  const handleViewSubmission = (submission: Submission) => {
    // Convert submission to navigation context format and set up navigation
    const navSubmissions = filteredSubmissions.map((s) => ({
      id: s.id,
      studentId: s.studentId,
      studentName: s.studentName,
      assignmentId: s.assignmentId,
      assignmentTitle: s.assignmentTitle,
      submissionDate: s.submittedAt,
      status: s.status as "submitted" | "late" | "draft",
      grade: s.grade
        ? parseInt(s.grade.replace(/[^\d]/g, "")) || undefined
        : undefined,
      feedback: s.feedback,
      summary: s.comments,
      files: s.files.map((f) => ({
        name: f.name,
        type: f.type,
        size: parseInt(f.size) || 0,
      })),
      metrics: {
        accuracy: s.rubric
          ? `${(
              (s.rubric.reduce(
                (sum, item) => sum + (item.earnedPoints || 0),
                0
              ) /
                s.rubric.reduce((sum, item) => sum + item.points, 0)) *
              100
            ).toFixed(1)}%`
          : undefined,
        executionTime: "2.3s",
      },
    }));

    // Navigate to enhanced submission details with navigation context
    navigateTo("submission-details", {
      submissions: navSubmissions,
      currentSubmissionId: submission.id,
      assignmentTitle: submission.assignmentTitle,
    });
  };

  return (
    <>
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-bits-blue">
                  {allSubmissions.length}
                </div>
                <p className="text-sm text-muted-foreground">
                  Total Submissions
                </p>
              </div>
              <FileText className="h-8 w-8 text-bits-blue" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-yellow-600">
                  {
                    allSubmissions.filter((s) => s.status === "submitted")
                      .length
                  }
                </div>
                <p className="text-sm text-muted-foreground">Pending Grading</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {allSubmissions.filter((s) => s.status === "graded").length}
                </div>
                <p className="text-sm text-muted-foreground">Graded</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-red-600">
                  {allSubmissions.filter((s) => s.status === "late").length}
                </div>
                <p className="text-sm text-muted-foreground">
                  Late Submissions
                </p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search submissions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={courseFilter} onValueChange={setCourseFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="All Courses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Courses</SelectItem>
                {courses.map((course) => (
                  <SelectItem key={course} value={course}>
                    {course}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="submitted">Submitted</SelectItem>
                <SelectItem value="graded">Graded</SelectItem>
                <SelectItem value="late">Late</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Submissions Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Submissions</CardTitle>
          <CardDescription>
            Manage and grade student submissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Assignment</TableHead>
                <TableHead>Course</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Grade</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSubmissions.map((submission) => (
                <TableRow key={submission.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{submission.studentName}</p>
                      <p className="text-sm text-muted-foreground">
                        {submission.studentEmail}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">
                    {submission.assignmentTitle}
                  </TableCell>
                  <TableCell>{submission.course}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {new Date(submission.submittedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(submission.status)}>
                      {submission.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {submission.grade ? (
                      <Badge variant="outline" className="font-bold">
                        {submission.grade}
                      </Badge>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewSubmission(submission)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                      <Button
                        size="sm"
                        className="bg-bits-gold hover:bg-bits-gold/90"
                        onClick={() => handleGradeSubmission(submission)}
                      >
                        <GraduationCap className="h-4 w-4 mr-2" />
                        {submission.status === "graded"
                          ? "Update Grade"
                          : "Grade"}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredSubmissions.length === 0 && (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                No submissions found matching your criteria
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Rubric-Based Grading Modal */}
      {selectedSubmission && selectedAssignment && (
        <RubricGradingModal
          open={showGradingModal}
          onOpenChange={setShowGradingModal}
          submission={selectedSubmission}
          assignment={selectedAssignment}
          onSave={handleSaveGrade}
        />
      )}
    </>
  );
}

export default function SubmissionsPage({
  assignmentId,
}: {
  assignmentId?: string;
}) {
  const { user } = useAuth();
  const { goBack } = useNavigation();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [courseFilter, setCourseFilter] = useState("all");
  const [selectedSubmission, setSelectedSubmission] =
    useState<Submission | null>(null);

  // Get assignment title for breadcrumb when filtering by specific assignment
  const getAssignmentTitle = () => {
    if (assignmentId) {
      return mockAssignments[assignmentId]?.title || "Unknown Assignment";
    }
    return null;
  };

  // Filter submissions by assignmentId if provided
  const getFilteredSubmissions = () => {
    let filteredSubmissions = [...mockSubmissions];

    // Filter by assignmentId if provided
    if (assignmentId) {
      filteredSubmissions = filteredSubmissions.filter(
        (submission) => submission.assignmentId === assignmentId
      );
    }

    // Filter by user role and permissions
    if (user?.role === "student") {
      // Students see only their own submissions
      filteredSubmissions = filteredSubmissions.filter(
        (submission) => submission.studentEmail === user.email
      );
    }

    return filteredSubmissions;
  };

  const submissions = getFilteredSubmissions();
  const assignmentTitle = getAssignmentTitle();
  const isAssignmentSpecific = !!assignmentId;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {isAssignmentSpecific && (
            <Button variant="outline" onClick={goBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          )}
          <div>
            <h1 className="text-2xl font-bold text-bits-blue">
              {isAssignmentSpecific
                ? `${assignmentTitle} - Submissions`
                : user?.role === "student"
                ? "My Submissions"
                : "All Submissions"}
            </h1>
            <p className="text-muted-foreground">
              {isAssignmentSpecific
                ? `Viewing submissions for ${assignmentTitle}`
                : user?.role === "student"
                ? "Track your assignment submissions and grades"
                : "Review and grade student submissions"}
            </p>
          </div>
        </div>

        {user?.role !== "student" && (
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export Grades
            </Button>
            <Button variant="outline" size="sm">
              <MessageSquare className="h-4 w-4 mr-2" />
              Bulk Message
            </Button>
          </div>
        )}
      </div>

      {/* Content based on user role */}
      {user?.role === "student" ? (
        <StudentSubmissionsView
          submissions={submissions}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          courseFilter={courseFilter}
          setCourseFilter={setCourseFilter}
          selectedSubmission={selectedSubmission}
          setSelectedSubmission={setSelectedSubmission}
        />
      ) : (
        <InstructorSubmissionsView
          allSubmissions={submissions}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          courseFilter={courseFilter}
          setCourseFilter={setCourseFilter}
        />
      )}
    </div>
  );
}
