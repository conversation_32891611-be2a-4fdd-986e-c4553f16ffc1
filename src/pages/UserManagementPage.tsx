import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";
import {
  ArrowLeft,
  Search,
  Filter,
  Users,
  GraduationCap,
  Mail,
  Phone,
  MapPin,
  Calendar,
  BookOpen,
  Eye,
  User,
  School,
  ChevronDown,
  ChevronRight,
  Download,
  UserCheck,
  UserCog,
  ShieldCheck,
  ArrowUpDown,
  MoreHorizontal,
  Languages,
} from "lucide-react";
import { useAuth, useNavigation } from "../App";
//import useAuth from "../components/Context/AuthContext";
//import useNavigation from "../components/Context/NavigationContext";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../components/ui/dialog";
import { Separator } from "../components/ui/separator";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "../components/ui/pagination";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../components/ui/collapsible";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../components/ui/tabs";
import { UserProfileModal } from "../components/UserProfileModal";

interface User {
  id: string;
  name: string;
  email: string;
  userId: string;
  role: "student" | "instructor" | "admin" | "ta";
  phone?: string;
  address?: string;
  dateOfBirth?: string;
  enrollmentDate: string;
  status: "active" | "inactive" | "suspended";
  courses: CourseEnrollment[];
  gpa?: number;
  totalCredits?: number;
  year?: string;
  department: string;
  profilePicture?: string;
  specialization?: string;
  experience?: string;
}

interface CourseEnrollment {
  courseId: string;
  courseName: string;
  courseCode: string;
  instructor: string;
  enrollmentDate: string;
  currentGrade?: string;
  status: "enrolled" | "completed" | "dropped";
  role?: "student" | "ta" | "instructor";
}

interface Course {
  id: string;
  name: string;
  code: string;
  instructor: string;
  department: string;
  semester: string;
  enrolledStudents: number;
}

// Modern color palette for avatars
const AVATAR_COLORS = [
  { bg: "bg-bits-blue", text: "text-white" },
  { bg: "bg-bits-gold", text: "text-white" },
  { bg: "bg-bits-red", text: "text-white" },
  { bg: "bg-bits-light-blue", text: "text-white" },
  { bg: "bg-bits-gold", text: "text-white" },
  { bg: "bg-bits-red", text: "text-white" },
  { bg: "bg-bits-light-blue", text: "text-white" },
  { bg: "bg-bits-blue", text: "text-white" },
  { bg: "bg-bits-gold", text: "text-white" },
  { bg: "bg-bits-blue", text: "text-white" },
  { bg: "bg-bits-gold", text: "text-white" },
  { bg: "bg-bits-red", text: "text-white" },
  { bg: "bg-bits-light-blue", text: "text-white" },
  { bg: "bg-bits-gold", text: "text-white" },
  { bg: "bg-bits-blue", text: "text-white" },
  { bg: "bg-bits-red", text: "text-white" },
];

// Function to generate consistent color for a user based on their name
const getAvatarColor = (name: string) => {
  // Simple hash function to get consistent index
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  const index = Math.abs(hash) % AVATAR_COLORS.length;
  return AVATAR_COLORS[index];
};

// Mock data
const mockCourses: Course[] = [
  {
    id: "course_1",
    name: "Data Science Fundamentals",
    code: "CS F301",
    instructor: "Dr. A. Sharma",
    department: "Computer Science",
    semester: "Spring 2025",
    enrolledStudents: 45,
  },
  {
    id: "course_2",
    name: "Machine Learning Advanced",
    code: "CS F401",
    instructor: "Dr. B. Patel",
    department: "Computer Science",
    semester: "Spring 2025",
    enrolledStudents: 32,
  },
  {
    id: "course_3",
    name: "Database Systems",
    code: "CS F302",
    instructor: "Dr. C. Kumar",
    department: "Computer Science",
    semester: "Spring 2025",
    enrolledStudents: 38,
  },
  {
    id: "course_4",
    name: "Web Development",
    code: "CS F303",
    instructor: "Dr. D. Singh",
    department: "Computer Science",
    semester: "Spring 2025",
    enrolledStudents: 28,
  },
];

const mockUsers: User[] = [
  // Students
  {
    id: "stu_1",
    name: "Rahul Sharma",
    email: "<EMAIL>",
    userId: "2021A7PS001G",
    role: "student",
    phone: "+91 9876543210",
    address: "Mumbai, Maharashtra",
    dateOfBirth: "2003-05-15",
    enrollmentDate: "2021-08-01",
    status: "active",
    gpa: 8.5,
    totalCredits: 120,
    year: "3rd Year",
    department: "Computer Science",
    courses: [
      {
        courseId: "course_1",
        courseName: "Data Science Fundamentals",
        courseCode: "CS F301",
        instructor: "Dr. A. Sharma",
        enrollmentDate: "2025-01-01",
        currentGrade: "A-",
        status: "enrolled",
        role: "student",
      },
      {
        courseId: "course_2",
        courseName: "Machine Learning Advanced",
        courseCode: "CS F401",
        instructor: "Dr. B. Patel",
        enrollmentDate: "2025-01-01",
        currentGrade: "B+",
        status: "enrolled",
        role: "student",
      },
    ],
  },
  {
    id: "stu_2",
    name: "Priya Patel",
    email: "<EMAIL>",
    userId: "2021A7PS002G",
    role: "student",
    phone: "+91 9876543211",
    address: "Delhi, India",
    dateOfBirth: "2003-03-22",
    enrollmentDate: "2021-08-01",
    status: "active",
    gpa: 9.1,
    totalCredits: 125,
    year: "3rd Year",
    department: "Computer Science",
    courses: [
      {
        courseId: "course_1",
        courseName: "Data Science Fundamentals",
        courseCode: "CS F301",
        instructor: "Dr. A. Sharma",
        enrollmentDate: "2025-01-01",
        currentGrade: "A",
        status: "enrolled",
        role: "student",
      },
      {
        courseId: "course_3",
        courseName: "Database Systems",
        courseCode: "CS F302",
        instructor: "Dr. C. Kumar",
        enrollmentDate: "2025-01-01",
        currentGrade: "A+",
        status: "enrolled",
        role: "student",
      },
    ],
  },
  // Teaching Assistants
  {
    id: "ta_1",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    userId: "TA2024001",
    role: "ta",
    phone: "+91 9876543300",
    address: "Hyderabad, Telangana",
    dateOfBirth: "2001-08-15",
    enrollmentDate: "2024-08-01",
    status: "active",
    department: "Computer Science",
    specialization: "Machine Learning",
    experience: "2 years",
    courses: [
      {
        courseId: "course_1",
        courseName: "Data Science Fundamentals",
        courseCode: "CS F301",
        instructor: "Dr. A. Sharma",
        enrollmentDate: "2024-08-01",
        status: "enrolled",
        role: "ta",
      },
      {
        courseId: "course_2",
        courseName: "Machine Learning Advanced",
        courseCode: "CS F401",
        instructor: "Dr. B. Patel",
        enrollmentDate: "2024-08-01",
        status: "enrolled",
        role: "ta",
      },
    ],
  },
  {
    id: "ta_2",
    name: "Michael Chen",
    email: "<EMAIL>",
    userId: "TA2024002",
    role: "ta",
    phone: "+91 9876543301",
    address: "Bangalore, Karnataka",
    dateOfBirth: "2000-12-03",
    enrollmentDate: "2024-08-01",
    status: "active",
    department: "Computer Science",
    specialization: "Database Systems",
    experience: "1.5 years",
    courses: [
      {
        courseId: "course_3",
        courseName: "Database Systems",
        courseCode: "CS F302",
        instructor: "Dr. C. Kumar",
        enrollmentDate: "2024-08-01",
        status: "enrolled",
        role: "ta",
      },
    ],
  },
  // Instructors
  {
    id: "inst_1",
    name: "Dr. A. Sharma",
    email: "<EMAIL>",
    userId: "INST001",
    role: "instructor",
    phone: "+91 9876543400",
    address: "Pilani, Rajasthan",
    dateOfBirth: "1975-04-20",
    enrollmentDate: "2010-08-01",
    status: "active",
    department: "Computer Science",
    specialization: "Data Science, Machine Learning",
    experience: "15 years",
    courses: [
      {
        courseId: "course_1",
        courseName: "Data Science Fundamentals",
        courseCode: "CS F301",
        instructor: "Dr. A. Sharma",
        enrollmentDate: "2010-08-01",
        status: "enrolled",
        role: "instructor",
      },
    ],
  },
  {
    id: "inst_2",
    name: "Dr. B. Patel",
    email: "<EMAIL>",
    userId: "INST002",
    role: "instructor",
    phone: "+91 9876543401",
    address: "Pilani, Rajasthan",
    dateOfBirth: "1978-11-12",
    enrollmentDate: "2012-08-01",
    status: "active",
    department: "Computer Science",
    specialization: "Advanced Machine Learning, AI",
    experience: "13 years",
    courses: [
      {
        courseId: "course_2",
        courseName: "Machine Learning Advanced",
        courseCode: "CS F401",
        instructor: "Dr. B. Patel",
        enrollmentDate: "2012-08-01",
        status: "enrolled",
        role: "instructor",
      },
    ],
  },
  // Admin
  {
    id: "admin_1",
    name: "Admin User",
    email: "<EMAIL>",
    userId: "ADMIN001",
    role: "admin",
    phone: "+91 9876543500",
    address: "Pilani, Rajasthan",
    dateOfBirth: "1980-01-01",
    enrollmentDate: "2015-01-01",
    status: "active",
    department: "Administration",
    specialization: "Platform Management",
    experience: "10 years",
    courses: [],
  },
  // Additional students for demonstration
  ...Array.from({ length: 15 }, (_, i) => ({
    id: `stu_${i + 3}`,
    name: [
      "Sneha Reddy",
      "Vikram Singh",
      "Ananya Gupta",
      "Rohit Jain",
      "Kavya Menon",
    ][i % 5],
    email: `student${i + 3}@bits.edu`,
    userId: `2021A7PS${String(i + 3).padStart(3, "0")}G`,
    role: "student" as const,
    phone: `+91 987654${String(i + 3200).padStart(4, "0")}`,
    address: [
      "Chennai, Tamil Nadu",
      "Hyderabad, Telangana",
      "Pune, Maharashtra",
      "Kolkata, West Bengal",
    ][i % 4],
    dateOfBirth: `2003-${String((i % 12) + 1).padStart(2, "0")}-${String(
      (i % 28) + 1
    ).padStart(2, "0")}`,
    enrollmentDate: "2021-08-01",
    status: ["active", "active", "active", "inactive"][i % 4] as
      | "active"
      | "inactive",
    gpa: 6.5 + Math.random() * 3,
    totalCredits: 100 + i * 2,
    year: ["2nd Year", "3rd Year", "4th Year"][i % 3],
    department: "Computer Science",
    courses: [
      {
        courseId: mockCourses[i % mockCourses.length].id,
        courseName: mockCourses[i % mockCourses.length].name,
        courseCode: mockCourses[i % mockCourses.length].code,
        instructor: mockCourses[i % mockCourses.length].instructor,
        enrollmentDate: "2025-01-01",
        currentGrade: ["A+", "A", "A-", "B+", "B", "B-"][i % 6],
        status: "enrolled" as const,
        role: "student" as const,
      },
    ],
  })),
];

function UserRoleTab({
  role,
  users,
  title,
}: {
  role: string;
  users: User[];
  title: string;
}) {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [sortField, setSortField] = useState("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const itemsPerPage = 10;

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.userId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" || user.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Sort users
  const sortedUsers = [...filteredUsers].sort((a, b) => {
    let aValue: any = a[sortField as keyof User];
    let bValue: any = b[sortField as keyof User];

    if (sortField === "gpa") {
      aValue = a.gpa || 0;
      bValue = b.gpa || 0;
    }

    if (typeof aValue === "string") {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (sortDirection === "asc") {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  const totalPages = Math.ceil(sortedUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentUsers = sortedUsers.slice(startIndex, endIndex);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-bits-green-shade";
      case "inactive":
        return "bg-gray-100 text-gray-800";
      case "suspended":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "student":
        return <GraduationCap className="h-4 w-4 text-blue-600" />;
      case "ta":
        return <UserCheck className="h-4 w-4 text-purple-600" />;
      case "instructor":
        return <UserCog className="h-4 w-4 text-green-600" />;
      case "admin":
        return <ShieldCheck className="h-4 w-4 text-red-600" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const SortableHeader = ({
    field,
    children,
  }: {
    field: string;
    children: React.ReactNode;
  }) => (
    <TableHead
      className="cursor-pointer hover:bg-gray-100 transition-colors"
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center space-x-1">
        <span>{children}</span>
        <ArrowUpDown className="h-4 w-4 text-gray-400" />
      </div>
    </TableHead>
  );

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    return (
      <div className="flex justify-center mt-6">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                className={
                  currentPage === 1
                    ? "pointer-events-none opacity-50"
                    : "cursor-pointer"
                }
              />
            </PaginationItem>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = i + 1;
              return (
                <PaginationItem key={page}>
                  <PaginationLink
                    onClick={() => setCurrentPage(page)}
                    isActive={page === currentPage}
                    className="cursor-pointer"
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              );
            })}

            <PaginationItem>
              <PaginationNext
                onClick={() =>
                  setCurrentPage(Math.min(totalPages, currentPage + 1))
                }
                className={
                  currentPage === totalPages
                    ? "pointer-events-none opacity-50"
                    : "cursor-pointer"
                }
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    );
  };
  console.log("role", role);
  return (
    <div className="space-y-6">
      <div className="flex justify-between">
        <h3 className="text-lg font-semibold">
          {title}({currentUsers?.length})
        </h3>
        {/* <p className="text-sm text-muted-foreground">Manage {role} users in the platform</p> */}
        {/* Filters */}
        <div className="flex items-center space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={`Search ${role}s...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9"
            />
          </div>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="suspended">Suspended</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Results Info */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>
          Showing {startIndex + 1} to {Math.min(endIndex, sortedUsers.length)}{" "}
          of {sortedUsers.length} {role}s
        </span>
        <span>
          Page {currentPage} of {totalPages}
        </span>
      </div>

      {/* Enhanced Data Table */}
      <div className="border rounded-lg overflow-hidden bg-white">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50">
              <SortableHeader field="userId">ID</SortableHeader>
              <SortableHeader field="name">User</SortableHeader>
              <SortableHeader field="department">
                {role === "instructor" ? "Role" : "Department"}
              </SortableHeader>
              <SortableHeader field="status">Status</SortableHeader>
              {role !== "student" && (
                <SortableHeader field="gpa">GPA</SortableHeader>
              )}
              {role === "student" && (
                <SortableHeader field="year">Year</SortableHeader>
              )}
              {(role === "instructor" || role === "ta") && (
                <TableHead>Specialization</TableHead>
              )}
              <SortableHeader field="enrollmentDate">Joined</SortableHeader>
              <TableHead className="text-center">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentUsers.map((user) => {
              const avatarColor = getAvatarColor(user.name);
              return (
                <TableRow
                  key={user.id}
                  className="hover:bg-gray-50 transition-colors"
                >
                  <TableCell>
                    <span className=" text-sm text-bits-grey-600">
                      {user.userId}
                    </span>{" "}
                    {/* font-mono */}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div
                        className={`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 ${avatarColor.bg}`}
                      >
                        <span
                          className={`font-medium text-sm ${avatarColor.text}`}
                        >
                          {user.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </span>
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-gray-900 truncate">
                            {user.name}
                          </p>
                          {getRoleIcon(user.role)}
                        </div>
                        <p className="text-sm text-bits-grey-600 truncate">
                          {user.email}
                        </p>
                      </div>
                    </div>
                  </TableCell>

                  <TableCell>
                    {role === "instructor" ? (
                      <span className="text-sm font-medium text-bits-grey-600">
                        TA
                      </span>
                    ) : (
                      <span className="text-sm font-medium text-bits-grey-600">
                        {user.department}
                      </span>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(user.status)}>
                      {user.status}
                    </Badge>
                  </TableCell>
                  {role !== "student" && (
                    <TableCell>
                      <div className="text-sm">
                        {user.gpa ? (
                          <span
                            className={`font-medium ${
                              user.gpa >= 9.0
                                ? "text-green-600"
                                : user.gpa >= 8.0
                                ? "text-blue-600"
                                : user.gpa >= 7.0
                                ? "text-yellow-600"
                                : "text-red-600"
                            }`}
                          >
                            {user.gpa.toFixed(2)}
                          </span>
                        ) : (
                          <span className="text-bits-grey-600">N/A</span>
                        )}
                      </div>
                    </TableCell>
                  )}
                  {role === "student" && (
                    <TableCell>
                      <span className="text-sm text-bits-grey-600">
                        {user.year || "N/A"}
                      </span>
                    </TableCell>
                  )}
                  {(role === "instructor" || role === "ta") && (
                    <TableCell>
                      <span className="text-sm font-medium text-bits-grey-600">
                        {user.specialization || "Not specified"}
                      </span>
                    </TableCell>
                  )}
                  <TableCell>
                    <span className="text-sm text-gray-600 font-medium text-bits-grey-600">
                      {new Date(user.enrollmentDate).toLocaleDateString()}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <UserProfileModal
                      user={{
                        ...user,
                        courses: user.courses || [],
                      }}
                      trigger={
                        <Button
                          size="sm"
                          variant="outline"
                          className="hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-colors"
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Profile
                        </Button>
                      }
                    />
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {currentUsers.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Users className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">
              No {title} Found
            </h3>
            <p className="text-muted-foreground">
              Try adjusting your search criteria or filters.
            </p>
          </CardContent>
        </Card>
      )}

      {renderPagination()}
    </div>
  );
}

export default function UserManagementPage() {
  const { user } = useAuth();
  const { goBack } = useNavigation();

  // Access control - only admins and instructors can access
  if (user?.role === "student") {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <div className="space-y-4">
              <Users className="h-16 w-16 text-muted-foreground mx-auto" />
              <h2 className="text-2xl font-bold text-muted-foreground">
                Access Denied
              </h2>
              <p className="text-muted-foreground">
                You don't have permission to access user management.
              </p>
              <Button
                onClick={goBack}
                className="bg-bits-blue hover:bg-bits-blue/90"
              >
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const students = mockUsers.filter((u) => u.role === "student");
  const instructors = mockUsers.filter((u) => u.role === "instructor");
  const tas = mockUsers.filter((u) => u.role === "ta");
  const admins = mockUsers.filter((u) => u.role === "admin");

  const totalUsers = mockUsers.length;
  const activeUsers = mockUsers.filter((u) => u.status === "active").length;
  const inactiveUsers = mockUsers.filter((u) => u.status === "inactive").length;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">
            User Management
          </h1>
          <p className=" text-bits-grey-600">
            View and manage all platform users{" "}
          </p>
        </div>
        {/* <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Users
          </Button>
        </div> */}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="justify-center " style={{ height: 100 }}>
          <CardHeader className="flex items-center justify-between pt-0">
            <CardTitle className="text-base text-bits-grey-600">
              Total Users{" "}
              <div className="text-3xl font-bold text-black">{totalUsers}</div>
            </CardTitle>
            <Users className="text-bits-blue" />
          </CardHeader>
        </Card>
        <Card className="justify-center" style={{ height: 100 }}>
          <CardHeader className="flex items-center justify-between pt-0">
            <CardTitle className="text-base text-bits-grey-600">
              Students
              <div className="text-3xl font-bold text-black">
                {students.length}
              </div>
            </CardTitle>
            <GraduationCap className="text-bits-blue" />
          </CardHeader>
        </Card>

        <Card className="justify-center" style={{ height: 100 }}>
          <CardHeader className="flex items-center justify-between pt-0">
            <CardTitle className="text-base text-bits-grey-600">
              Staff
              <div className="text-3xl font-bold text-black">
                {instructors.length + tas.length + admins.length}
              </div>
            </CardTitle>
            <Languages className="text-bits-blue" />
          </CardHeader>
        </Card>
        <Card className="justify-center" style={{ height: 100 }}>
          <CardHeader className="flex items-center justify-between pt-0">
            <CardTitle className="text-base text-bits-grey-600">
              Active Users
              <div className="text-3xl font-bold text-black">{activeUsers}</div>
            </CardTitle>
            <UserCheck className="text-bits-blue" />
          </CardHeader>
        </Card>
      </div>

      {/* User Tabs */}
      <Card>
        <CardHeader>
          <CardTitle>Platform Users</CardTitle>
          <CardDescription>
            Browse and manage users by role category
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="students" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger
                value="students"
                className="flex items-center space-x-2"
              >
                <GraduationCap className="h-4 w-4" />
                <span>Students ({students.length})</span>
              </TabsTrigger>
              <TabsTrigger
                value="instructors"
                className="flex items-center space-x-2"
              >
                <UserCog className="h-4 w-4" />
                <span>Staff ({instructors.length})</span>
              </TabsTrigger>
              {/* <TabsTrigger value="tas" className="flex items-center space-x-2">
                <UserCheck className="h-4 w-4" />
                <span>TAs ({tas.length})</span>
              </TabsTrigger> */}
              <TabsTrigger
                value="admins"
                className="flex items-center space-x-2"
              >
                <ShieldCheck className="h-4 w-4" />
                <span>Admins ({admins.length})</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="students">
              <UserRoleTab role="student" users={students} title="Students" />
            </TabsContent>

            <TabsContent value="instructors">
              <UserRoleTab
                role="instructor"
                users={instructors}
                title="Instructors"
              />
            </TabsContent>

            <TabsContent value="tas">
              <UserRoleTab role="ta" users={tas} title="Teaching Assistants" />
            </TabsContent>

            <TabsContent value="admins">
              <UserRoleTab role="admin" users={admins} title="Administrators" />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
