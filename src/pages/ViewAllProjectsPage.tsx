import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Progress } from "../components/ui/progress";
import {
  Plus,
  Edit,
  LinkIcon,
  Calendar,
  UserIcon,
  ArrowLeft,
} from "lucide-react";
import { useAuth, useNavigation } from "../App";
// import useAuth from "../components/Context/AuthContext";
// import useNavigation from "../components/Context/NavigationContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";

interface Course {
  id: string;
  name: string;
  code: string;
  students: number;
  assignments: number;
  pendingGrades: number;
  averageGrade: number;
  nextDeadline: string;
  status: "active" | "completed";
}

interface Assignment {
  id: string;
  title: string;
  course: string;
  startDate: string;
  dueDate: string;
  submissions: number;
  totalStudents: number;
  gradedSubmissions: number;
  status: "active" | "grading" | "overdue" | "completed";
}

// Mock data
const mockCourses: Course[] = [
  {
    id: "1",
    name: "Data Science Fundamentals",
    code: "CS F301",
    students: 45,
    assignments: 8,
    pendingGrades: 12,
    averageGrade: 85.2,
    nextDeadline: "2025-01-15T23:59:00Z",
    status: "active",
  },
  {
    id: "2",
    name: "Machine Learning Advanced",
    code: "CS F401",
    students: 32,
    assignments: 6,
    pendingGrades: 8,
    averageGrade: 78.9,
    nextDeadline: "2025-01-20T23:59:00Z",
    status: "active",
  },
  {
    id: "3",
    name: "Database Systems",
    code: "CS F302",
    students: 38,
    assignments: 7,
    pendingGrades: 0,
    averageGrade: 82.1,
    nextDeadline: "2025-08-25T23:59:00Z",
    status: "active",
  },
];

const mockAssignments: Assignment[] = [
  {
    id: "1",
    title: "Housing Price Prediction",
    course: "Data Science Fundamentals",
    startDate: "2025-08-01T16:32:00Z",
    dueDate: "2025-08-15T23:59:00Z",
    submissions: 38,
    totalStudents: 45,
    gradedSubmissions: 33,
    status: "grading",
  },
  {
    id: "2",
    title: "Neural Network Implementation",
    course: "Machine Learning Advanced",
    startDate: "2025-07-25T22:31:00Z",
    dueDate: "2025-08-02T23:59:00Z",
    submissions: 28,
    totalStudents: 32,
    gradedSubmissions: 20,
    status: "overdue",
  },
  {
    id: "3",
    title: "SQL Query Optimization",
    course: "Database Systems",
    startDate: "2025-06-12T21:44:00Z",
    dueDate: "2025-07-25T23:59:00Z",
    submissions: 0,
    totalStudents: 38,
    gradedSubmissions: 0,
    status: "active",
  },
];

export default function ViewAllProjectsPage() {
  const [courses] = useState<Course[]>(mockCourses);
  const [assignments] = useState<Assignment[]>(mockAssignments);
  const { goBack, navigateTo } = useNavigation();

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <ArrowLeft
            className="h-4 w-4 text-bits-grey-500"
            mb-1
            onClick={goBack}
          />
          <div>
            <h1 className="text-3xl font-semibold text-gray-900">
              All Projects
            </h1>
          </div>
        </div>

        <div className="flex items-center gap-3 text-sm font-semibold text-primary-foreground">
          <Button
            className="bg-bits-blue  hover:bg-blue-700"
            onClick={() => navigateTo("create-project")}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create New Project
          </Button>
        </div>
      </div>

      {/* <Button
        className="bg-blue-600 hover:bg-blue-700"
        // onClick={() => navigateTo("create-project")}
      >
        <Plus className="h-4 w-4 mr-2" />
      </Button> */}
      <div className="grid grid-cols-2 text-lg font-semibold gap-6">
        {courses.map((course) => (
          <CourseCard
            key={course.id}
            course={course}
            assignment={assignments.find((a) => a.course === course.name)!}
          />
        ))}
      </div>
    </div>
  );
}

function CourseCard({
  course,
  assignment,
}: {
  course: Course;
  assignment: Assignment;
}) {
  const { navigateTo } = useNavigation();

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "overdue":
        return "bg-bits-red-100  text-bits-red-600";
      default:
        return "text-muted-foreground";
    }
  };

  function getDateDifference(startDate: string, dueDate: string) {
    const now = new Date();
    const due = new Date(dueDate);
    const start = new Date(startDate);
    const diffMs = Math.abs(due.getTime() - now.getTime());

    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    // const diffHours = Math.floor((diffMs / (1000 * 60 * 60)) % 24);
    // const diffMinutes = Math.floor((diffMs / (1000 * 60)) % 60);
    // const diffSeconds = Math.floor((diffMs / 1000) % 60);

    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    const dateValue =
      start.getDate() +
      " " +
      monthNames[start.getMonth()] +
      " " +
      "-" +
      " " +
      due.getDate() +
      " " +
      monthNames[due.getMonth()] +
      " " +
      due.getFullYear();

    console.log(diffDays);
    console.log(dueDate);

    // if (diffDays < 0) statusString = "Overdue";
    // if (diffDays === 0) statusString = "Due today";
    // if (diffDays === 1) statusString = "1 day left";
    // if (diffDays > 0) statusString = diffDays + " days left";
    return dateValue;
  }

  const submissionRate =
    ((course.students - course.pendingGrades) / course.students) * 100;

  const submissions = `${assignment.submissions} / ${assignment.totalStudents}`;
  const graded = `${assignment.gradedSubmissions} / ${assignment.submissions}`;

  return (
    <Card
      className="cursor-pointer hover:shadow-md transition-shadow duration-200"
      onClick={() => navigateTo("view-course", { courseId: course.id })}
    >
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex gap-4 items-start justify-between">
            <div>
              <CardTitle className="text-lg font-semibold text-bits-grey">
                {assignment.title}
              </CardTitle>
              <CardDescription>
                <div className="text-sm font-normal text-bits-grey-600">
                  {course.code}
                </div>
              </CardDescription>
            </div>
            <Badge
              className={getStatusColor(assignment.status)}
              variant="outline"
            >
              {assignment.status}
            </Badge>
          </div>
          <div className="flex gap-4 items-start justify-between">
            <Button className="border-text-bits-grey-300" variant="outline">
              <Edit className="h-4 w-4" />
            </Button>
            <Button className="border-text-bits-grey-300" variant="outline">
              <LinkIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <div className="text-2xl font-bold text-bits-blue-neutral-900">
                {submissions}
              </div>
              <div className="text-bits-grey-600 font-normal text-sm">
                Submissions
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-bits-blue-neutral-900">
                {graded}
              </div>
              <div className="text-bits-grey-600 font-normal text-sm">
                Graded
              </div>
            </div>

            <div>
              <div className="text-2xl font-bold text-bits-blue-neutral-900">
                {course.averageGrade}%
              </div>
              <div className="text-bits-grey-600 font-normal text-sm">
                Avg Grade
              </div>
            </div>
          </div>

          <div>
            <div className="flex justify-between text-md font-medium text-bits-grey-600 mb-2">
              <span>Progress</span>
              <span>{submissionRate.toFixed(0)}%</span>
            </div>
            <div className="mb-10">
              <Progress value={submissionRate} className="h-2" />
            </div>
          </div>

          <div className="flex items-center gap-20 text-sm">
            <div className="flex items-center space-x-1 justify-between">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-bits-grey-600 text-sm font-semibold">
                {getDateDifference(assignment.startDate, assignment.dueDate)}
              </span>
            </div>
            <div className="flex items-start justify-between">
              <UserIcon className="h-4 w-4 text-muted-foreground" />
              <div className="text-muted-foreground">Your Role:</div>
              <div className="text-sm font-semibold ml-1 text-bits-grey">
                {useAuth().user?.role}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
