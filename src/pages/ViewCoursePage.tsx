import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { Input } from "../components/ui/input";
import {
  Users,
  BookOpen,
  Calendar,
  Search,
  Mail,
  Phone,
  Award,
  TrendingUp,
  FileText,
  Clock,
  CheckCircle,
  AlertTriangle,
  BarChart3,
  Download,
  Plus,
  Settings,
  Copy,
  Trash2,
  Edit,
  AlertCircle,
  Shield,
  Target,
  Zap,
  Star,
  Timer,
  PlayCircle,
  CheckSquare,
  XCircle,
  ArrowLeft,
  BarChart,
  BarChartIcon,
  BarChart2,
  Edit2,
} from "lucide-react";
import { useAuth, useNavigation } from "../App";
// import useAuth from "../components/Context/AuthContext";
// import useNavigation from "../components/Context/NavigationContext";
import { Separator } from "../components/ui/separator";
import { Progress } from "../components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../components/ui/tabs";
import { ChartContainer } from "../components/ui/chart";
import { useNavigate } from "react-router-dom";

interface Course {
  id: string;
  name: string;
  code: string;
  description: string;
  instructor: string;
  semester: string;
  credits: number;
  startDate: string;
  endDate: string;
  enrolledStudents: number;
  maxCapacity: number;
  assignments: Assignment[];
  students: Student[];
  schedule: {
    day: string;
    time: string;
    location: string;
  }[];
}

interface Student {
  id: string;
  name: string;
  email: string;
  studentId: string;
  enrollmentDate: string;
  attendance: number;
  averageGrade: string;
  completedAssignments: number;
  totalAssignments: number;
  status: "active" | "inactive" | "dropped";
}

interface Assignment {
  id: string;
  title: string;
  dueDate: string;
  submissions: number;
  totalStudents: number;
  avgGrade: string;
  status: "active" | "closed" | "draft";
  difficulty?: "Beginner" | "Intermediate" | "Advanced";
  estimatedHours?: number;
  category?: string;
  description?: string;
  tags?: string[];
}

interface RecentActivity {
  id: string;
  type: "system" | "security" | "enrollment" | "assignment";
  title: string;
  description: string;
  timestamp: string;
  icon: React.ElementType;
  color: string;
}

// Mock course data
const mockCourseData: { [key: string]: Course } = {
  "1": {
    id: "1",
    name: "Data Science 101",
    code: "DS101",
    description:
      "Introduction to Data Science covering fundamentals of statistics, programming, and machine learning. Students will learn to analyze data, build predictive models, and communicate insights effectively.",
    instructor: "Dr. A. Sharma",
    semester: "Fall 2024",
    credits: 4,
    startDate: "2024-08-15",
    endDate: "2024-12-15",
    enrolledStudents: 30,
    maxCapacity: 35,
    schedule: [
      { day: "Monday", time: "10:00 AM - 11:30 AM", location: "Room 101" },
      { day: "Wednesday", time: "10:00 AM - 11:30 AM", location: "Room 101" },
      { day: "Friday", time: "2:00 PM - 3:30 PM", location: "Lab 205" },
    ],
    assignments: [
      {
        id: "1",
        title: "Linear Regression Analysis",
        description:
          "Build and evaluate linear regression models on real-world datasets",
        dueDate: "2025-01-15",
        submissions: 25,
        totalStudents: 30,
        avgGrade: "B+",
        status: "active",
        difficulty: "Intermediate",
        estimatedHours: 8,
        category: "Machine Learning",
        tags: ["Regression", "Statistics", "Python"],
      },
      {
        id: "2",
        title: "Data Visualization Project",
        description:
          "Create compelling visualizations using matplotlib and seaborn",
        dueDate: "2025-01-25",
        submissions: 0,
        totalStudents: 30,
        avgGrade: "N/A",
        status: "active",
        difficulty: "Beginner",
        estimatedHours: 5,
        category: "Data Visualization",
        tags: ["Matplotlib", "Seaborn", "EDA"],
      },
      {
        id: "3",
        title: "Exploratory Data Analysis",
        description: "Comprehensive analysis of customer behavior dataset",
        dueDate: "2024-12-10",
        submissions: 30,
        totalStudents: 30,
        avgGrade: "A-",
        status: "closed",
        difficulty: "Beginner",
        estimatedHours: 6,
        category: "Data Analysis",
        tags: ["Pandas", "NumPy", "EDA"],
      },
      {
        id: "4",
        title: "Deep Learning CNN Project",
        description:
          "Build a convolutional neural network for image classification",
        dueDate: "2025-02-05",
        submissions: 0,
        totalStudents: 30,
        avgGrade: "N/A",
        status: "draft",
        difficulty: "Advanced",
        estimatedHours: 15,
        category: "Deep Learning",
        tags: ["CNN", "TensorFlow", "Image Classification"],
      },
    ],
    students: [
      {
        id: "1",
        name: "Rahul Sharma",
        email: "<EMAIL>",
        studentId: "2023001",
        enrollmentDate: "2024-08-15",
        attendance: 95,
        averageGrade: "A",
        completedAssignments: 25,
        totalAssignments: 30,
        status: "active",
      },
      {
        id: "2",
        name: "Priya Patel",
        email: "<EMAIL>",
        studentId: "2023002",
        enrollmentDate: "2024-08-15",
        attendance: 88,
        averageGrade: "A-",
        completedAssignments: 28,
        totalAssignments: 30,
        status: "active",
      },
      {
        id: "3",
        name: "Amit Kumar",
        email: "<EMAIL>",
        studentId: "2023003",
        enrollmentDate: "2024-08-15",
        attendance: 78,
        averageGrade: "B+",
        completedAssignments: 22,
        totalAssignments: 30,
        status: "active",
      },
      {
        id: "4",
        name: "Sneha Singh",
        email: "<EMAIL>",
        studentId: "2023004",
        enrollmentDate: "2024-08-15",
        attendance: 92,
        averageGrade: "A",
        completedAssignments: 29,
        totalAssignments: 30,
        status: "active",
      },
    ],
  },
};

// Mock recent activities
const mockRecentActivities: RecentActivity[] = [
  {
    id: "1",
    type: "system",
    title: "System alert",
    description: "High AWS usage detected - automatic scaling initiated",
    timestamp: "2 hours ago",
    icon: AlertTriangle,
    color: "bg-yellow-50 border-l-yellow-400",
  },
  {
    id: "2",
    type: "security",
    title: "Security event detected",
    description: "Multiple failed login attempts from IP *************",
    timestamp: "2 hours ago",
    icon: Shield,
    color: "bg-red-50 border-l-red-400",
  },
  {
    id: "3",
    type: "enrollment",
    title: "Bulk user enrollment",
    description: "25 new students added to Machine Learning course",
    timestamp: "2 hours ago",
    icon: Users,
    color: "bg-blue-50 border-l-blue-400",
  },
];

// Helper functions moved outside to prevent recreation
const getStatusColor = (status: string) => {
  switch (status) {
    case "active":
      return "bg-bits-completed text-bits-green ";
    case "inactive":
      return "bg-yellow-100 text-yellow-800";
    case "dropped":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getGradeColor = (grade: string) => {
  if (grade.startsWith("A")) return "text-bits-gold";
  if (grade.startsWith("B")) return "text-bits-blue";
  if (grade.startsWith("C")) return "text-bits-gold";
  return "text-bits-red";
};

// Tab components moved outside to prevent recreation
interface TabProps {
  course: Course;
  user: any;
  navigateTo: (page: string, params?: any) => void;
}

interface StudentsTabProps extends TabProps {
  searchTerm: string;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  handleViewStudent: (studentId: string) => void;
}

interface ProjectsTabProps extends TabProps {
  handleViewAssignment: (assignmentId: string) => void;
  handleViewSubmissions: (assignmentId: string) => void;
}

const OverviewTab = React.memo(({ course }: TabProps) => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Course Information */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-bits-blue" />
              <CardTitle className="font-medium text-lg">
                Course Information
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2 text-[16px]">{course.name}</h4>
                <p className="text-bits-grey-600">{course.description}</p>
              </div>

              {/* <Separator />

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <BookOpen className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">Course Code</span>
                  </div>
                  <p className="text-sm text-gray-600">{course.code}</p>
                </div>

                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <Users className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">Instructor</span>
                  </div>
                  <p className="text-sm text-gray-600">{course.instructor}</p>
                </div>

                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">Semester</span>
                  </div>
                  <p className="text-sm text-gray-600">{course.semester}</p>
                </div>

                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <Award className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">Credits</span>
                  </div>
                  <p className="text-sm text-gray-600">{course.credits} credits</p>
                </div>
              </div> */}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Stats */}
      <div>
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              {/* <BarChart3 className="h-5 w-5 text-bits-blue" /> */}
              <BarChart2 className="h-6 w-6 text-bits-warning" />
              <CardTitle className="font-medium text-[18px]">
                Quick Stats
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-bits-grey-600" />
                <span className="text-[16px] text-bits-grey-600">Students</span>
              </div>
              <span className="font-semibold text-gray-900">
                {course.enrolledStudents}
                {/* /{course.maxCapacity} */}
              </span>
            </div>

            {/* <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <FileText className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">Total Projects</span>
              </div>
              <span className="font-semibold text-gray-900">{course.assignments.length}</span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">Active Projects</span>
              </div>
              <span className="font-semibold text-gray-900">
                {course.assignments.filter(a => a.status === 'active').length}
              </span>
            </div> */}

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Award className="h-5 w-5 text-bits-grey-600" />
                <span className="text-[16px] text-bits-grey-600 ">
                  Avg Grade
                </span>
              </div>
              <span className="font-semibold text-gray-900">B+</span>
            </div>

            {/* <Separator />

            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Enrollment Progress</span>
                <span className="font-medium">{Math.round((course.enrolledStudents / course.maxCapacity) * 100)}%</span>
              </div>
              <Progress value={(course.enrolledStudents / course.maxCapacity) * 100} className="h-2" />
            </div> */}
          </CardContent>
        </Card>
      </div>
    </div>

    {/* Recent Activity */}
    {/* <Card>
      <CardHeader>
        <div className="flex items-center space-x-2">
          <Clock className="h-5 w-5 text-bits-blue" />
          <CardTitle>Recent Activity</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {mockRecentActivities.map((activity) => (
            <div key={activity.id} className={`p-4 rounded-lg border-l-4 ${activity.color}`}>
              <div className="flex items-start space-x-3">
                <activity.icon className="h-5 w-5 text-gray-500 mt-0.5" />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900">{activity.title}</h4>
                    <span className="text-xs text-gray-500">{activity.timestamp}</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card> */}
  </div>
));

const StudentsTab = React.memo(
  ({
    course,
    searchTerm,
    setSearchTerm,
    handleViewStudent,
  }: StudentsTabProps) => {
    const filteredStudents = course.students.filter(
      (student) =>
        student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.studentId.includes(searchTerm)
    );

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="font-medium text-lg">
                {" "}
                Students {/* ({course.enrolledStudents}) */}
              </CardTitle>
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search students..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-64"
                  />
                </div>
                {/* <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button> */}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Student</TableHead>
                  {/* <TableHead>Enrollment Date</TableHead>
                <TableHead>Attendance</TableHead> */}
                  <TableHead>Avg Grade</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Progress</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredStudents.map((student) => (
                  <TableRow key={student.id}>
                    <TableCell>
                      <span className="text-sm  text-bits-grey-600">
                        {student.studentId}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium ">{student.name}</p>
                        <p className="text-sm text-bits-grey-600">
                          {student.email}
                        </p>
                      </div>
                    </TableCell>

                    {/*  <TableCell>
                    <span className="text-sm">{new Date(student.enrollmentDate).toLocaleDateString()}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">{student.attendance}%</span>
                      <Progress value={student.attendance} className="w-16 h-2" />
                    </div>
                  </TableCell> */}
                    <TableCell>
                      <span
                        className={`font-medium ${getGradeColor(
                          student.averageGrade
                        )}`}
                      >
                        {student.averageGrade}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(student.status)}>
                        {student.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-xs text-bits-grey-600">
                          {student.completedAssignments}/
                          {student.totalAssignments} Submitted
                        </div>
                        <div className="flex items-center pt-2">
                          <div className="w-1/2 h-1 bg-gray-200 rounded-full overflow-hidden">
                            <div
                              className="h-full transition-all duration-300 bg-bits-blue"
                              style={{
                                width: `${student.totalAssignments > 0
                                    ? (student.completedAssignments /
                                      student.totalAssignments) *
                                    100
                                    : 0
                                  }%`,
                              }}
                            />
                          </div>
                          {/* optional: percentage text */}
                          {/* <span className="text-xs">
                          {student.totalAssignments > 0
                            ? Math.round((student.completedAssignments / student.totalAssignments) * 100)
                            : 0}
                          %
                        </span> */}
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0"
                          onClick={() => handleViewStudent(student.id)}
                        >
                          <Edit2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    );
  }
);

const ProjectsTab = React.memo(
  ({
    course,
    user,
    handleViewAssignment,
    handleViewSubmissions,
  }: ProjectsTabProps) => {
    // Helper functions for project display
    const getDifficultyColor = (difficulty: string) => {
      switch (difficulty) {
        case "Beginner":
          return "bg-bits-gold/10 text-bits-gold border-bits-gold";
        case "Intermediate":
          return "bg-bits-blue/10 text-bits-blue border-bits-blue";
        case "Advanced":
          return "bg-bits-red/10 text-bits-red border-bits-red";
        default:
          return "bg-muted text-muted-foreground border-border";
      }
    };

    const getDifficultyIcon = (difficulty: string) => {
      switch (difficulty) {
        case "Beginner":
          return <Star className="h-3 w-3" />;
        case "Intermediate":
          return <Zap className="h-3 w-3" />;
        case "Advanced":
          return <Target className="h-3 w-3" />;
        default:
          return <Star className="h-3 w-3" />;
      }
    };

    const getStatusIcon = (status: string) => {
      switch (status) {
        case "active":
          return <PlayCircle className="h-4 w-4 text-bits-gold" />;
        case "closed":
          return <CheckSquare className="h-4 w-4 text-bits-blue" />;
        case "draft":
          return <Clock className="h-4 w-4 text-muted-foreground" />;
        default:
          return <Clock className="h-4 w-4 text-muted-foreground" />;
      }
    };

    const getUrgencyIndicator = (dueDate: string, status: string) => {
      if (status === "closed" || status === "draft") return null;

      const now = new Date();
      const due = new Date(dueDate);
      const diffTime = due.getTime() - now.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 0)
        return { text: "Overdue", color: "bg-bits-red", urgent: true };
      if (diffDays <= 1)
        return { text: "Due Soon", color: "bg-bits-gold", urgent: true };
      if (diffDays <= 3)
        return {
          text: `${diffDays}d left`,
          color: "bg-bits-gold",
          urgent: true,
        };
      return null;
    };

    const getCategoryIcon = (category: string) => {
      switch (category) {
        case "Machine Learning":
          return <Target className="h-4 w-4" />;
        case "Data Visualization":
          return <BarChart3 className="h-4 w-4" />;
        case "Data Analysis":
          return <FileText className="h-4 w-4" />;
        case "Deep Learning":
          return <Zap className="h-4 w-4" />;
        default:
          return <BookOpen className="h-4 w-4" />;
      }
    };

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center font-medium space-x-2">
                  {/* <FileText className="h-5 w-5 text-bits-blue" /> */}
                  <span className="text-xl">
                    {" "}
                    Project Details {/* ({course.assignments.length}) */}
                  </span>
                </CardTitle>
                {/* <CardDescription className="mt-1">
                Track your progress through hands-on data science projects
              </CardDescription> */}
              </div>
              {user?.role === "instructor" && (
                <Button className="bg-bits-blue hover:bg-bits-blue/90">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Project
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {/* <div className="grid grid-cols-1 lg:grid-cols-2 gap-6"> */}
            <div className="">
              {course.assignments.map((assignment) => {
                const urgency = getUrgencyIndicator(
                  assignment.dueDate,
                  assignment.status
                );
                const completionRate = Math.round(
                  (assignment.submissions / assignment.totalStudents) * 100
                );

                return (
                  <div key={assignment.id} className="group relative pb-5">
                    {/* Urgency indicator */}
                    {/* {urgency && (
                    <div className={`absolute -top-2 -right-2 z-10 px-2 py-1 rounded-full text-xs text-white ${urgency.color} shadow-lg`}>
                      {urgency.text}
                    </div>
                  )} */}

                    {/* <Card className="h-full transition-all duration-200 hover:shadow-lg hover:scale-[1.02] cursor-pointer border-l-4 border-l-bits-blue"> */}
                    <Card className="h-full">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center justify-between space-x-2 mb-2">
                              {/* {getCategoryIcon(assignment.category || '')} */}
                              <h3 className="font-semibold text-gray-900 text-xl  transition-colors">
                                {assignment.title}
                              </h3>

                              {/* {getStatusIcon(assignment.status)} */}
                            </div>
                            {/* <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                            {assignment.description}
                          </p> */}

                            {/* Project metadata */}
                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <div className="flex items-center space-x-1">
                                <Calendar className="h-3 w-3" />
                                <span>
                                  {new Date(
                                    assignment.dueDate
                                  ).toLocaleDateString()}
                                </span>
                              </div>
                              {/* {assignment.estimatedHours && (
                              <div className="flex items-center space-x-1">
                                <Timer className="h-3 w-3" />
                                <span>{assignment.estimatedHours}h</span>
                              </div>
                            )} */}
                            </div>
                          </div>

                          <div className="flex flex-col items-end space-y-2">
                            <Badge
                              className={getStatusColor(assignment.status)}
                            >
                              {assignment.status}
                            </Badge>
                            {/* {assignment.difficulty && (
                            <Badge
                              variant="outline"
                              className={`text-xs border ${getDifficultyColor(assignment.difficulty)}`}
                            >
                              <div className="flex items-center space-x-1">
                                {getDifficultyIcon(assignment.difficulty)}
                                <span>{assignment.difficulty}</span>
                              </div>
                            </Badge>
                          )} */}
                          </div>
                        </div>
                      </CardHeader>

                      <CardContent className="pt-0">
                        {/* Progress and stats */}
                        <div className="grid grid-cols-3 gap-4 mb-4">
                          <div className="text-center">
                            <div className="text-3xl font-bold">
                              {assignment.submissions}
                            </div>
                            <p className=" text-bits-grey-600">Submissions</p>
                          </div>
                          <div className="text-center">
                            <div className="text-3xl font-bold">
                              {completionRate}%
                            </div>
                            <p className=" text-bits-grey-600">Completion</p>
                          </div>
                          <div className="text-center">
                            <div className={`text-3xl font-bold `}>
                              {assignment.avgGrade}
                            </div>
                            <p className=" text-bits-grey-600">Avg Grade</p>
                          </div>
                        </div>

                        {/* Progress bar */}
                        {/* <div className="mb-4">
                        <div className="flex justify-between text-xs text-gray-600 mb-1">
                          <span>Progress</span>
                          <span>{completionRate}%</span>
                        </div>
                        <Progress
                          value={completionRate}
                          className="h-2 bg-gray-100"
                        />
                      </div> */}

                        {/* Tags */}
                        {/* {assignment.tags && assignment.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-4">
                          {assignment.tags.slice(0, 3).map((tag, index) => (
                            <Badge
                              key={index}
                              variant="outline"
                              className="text-xs px-2 py-0 h-5 bg-gray-50"
                            >
                              {tag}
                            </Badge>
                          ))}
                          {assignment.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs px-2 py-0 h-5 bg-gray-50">
                              +{assignment.tags.length - 3}
                            </Badge>
                          )}
                        </div>
                      )} */}

                        {/* Action buttons */}
                        <div className="">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewAssignment(assignment.id)}
                            className="flex-1 /* hover:bg-bits-blue hover:text-white transition-colors */"
                          >
                            {/* <BookOpen className="h-3 w-3 mr-1" /> */}
                            View Project
                          </Button>
                          {user?.role === "instructor" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() =>
                                handleViewSubmissions(assignment.id)
                              }
                              className="flex-1 hover:bg-bits-gold hover:text-white transition-colors"
                            >
                              <FileText className="h-3 w-3 mr-1" />
                              Submissions
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                );
              })}
            </div>

            {/* Empty state for no projects */}
            {course.assignments.length === 0 && (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No projects yet
                </h3>
                <p className="text-gray-600 mb-4">
                  Projects will appear here as they are assigned by your
                  instructor.
                </p>
                {user?.role === "instructor" && (
                  <Button className="bg-bits-blue hover:bg-bits-blue/90">
                    <Plus className="h-4 w-4 mr-2" />
                    Create First Project
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }
);

const AnalyticsTab = React.memo(() => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Grade Distribution</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span>A (90-100%)</span>
              <div className="flex items-center space-x-2">
                <Progress value={40} className="w-32 h-2" />
                <span className="text-sm">40%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span>B (80-89%)</span>
              <div className="flex items-center space-x-2">
                <Progress value={35} className="w-32 h-2" />
                <span className="text-sm">35%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span>C (70-79%)</span>
              <div className="flex items-center space-x-2">
                <Progress value={20} className="w-32 h-2" />
                <span className="text-sm">20%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span>D/F (&lt;70%)</span>
              <div className="flex items-center space-x-2">
                <Progress value={5} className="w-32 h-2" />
                <span className="text-sm">5%</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Performance Trends</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">↑ 5%</div>
              <p className="text-sm text-gray-500">Average grade improvement</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">87%</div>
              <p className="text-sm text-gray-500">Average attendance</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-bits-gold">92%</div>
              <p className="text-sm text-gray-500">
                Assignment completion rate
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Users className="h-5 w-5" />
          <span>Student Engagement Metrics</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-bits-blue mb-2">87%</div>
            <p className="text-sm text-gray-600">Average Attendance</p>
            <Progress value={87} className="mt-2" />
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-bits-gold mb-2">92%</div>
            <p className="text-sm text-gray-600">Assignment Completion</p>
            <Progress value={92} className="mt-2" />
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">4.2/5</div>
            <p className="text-sm text-gray-600">Course Satisfaction</p>
            <Progress value={84} className="mt-2" />
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
));

export default function ViewCoursePage({ courseId }: { courseId: string }) {
  const { user } = useAuth();
  const { navigateTo, goBack } = useNavigation();
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTab, setSelectedTab] = useState("overview");

  const course = mockCourseData[courseId];

  // Stable callback functions
  const handleViewStudent = (studentId: string) => {
    navigateTo("view-student", { studentId });
  };

  const handleViewAssignment = (assignmentId: string) => {
    navigate(`/project-details/${assignmentId}`)
    //navigateTo("project-details", { projectId: assignmentId });
  };

  const handleViewSubmissions = (assignmentId: string) => {
    navigateTo("submissions", { assignmentId });
  };

  if (!course) {
    return (
      <div className="p-6 space-y-6">
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Course Not Found</h2>
          <p className="text-gray-500">
            The requested course could not be found.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <button
              onClick={goBack}
              className="p-2 rounded-full hover:bg-gray-100"
            >
              <ArrowLeft className="h-7 w-8 text-bits-grey-600" />
            </button>{" "}
            {course.name}
          </h1>
          {/* <p className="text-gray-600 mt-1">{course.code} • {course.semester} • {course.instructor}</p> */}
        </div>
        {/* <div className="flex items-center space-x-2">
          <Badge variant="outline" className="bg-green-50 text-green-700">
            {course.enrolledStudents}/{course.maxCapacity} Students
          </Badge>
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            {course.credits} Credits
          </Badge>
        </div> */}
      </div>

      <Tabs
        value={selectedTab}
        onValueChange={setSelectedTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="students">Students</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          {/* <TabsTrigger value="analytics">Analytics</TabsTrigger> */}
        </TabsList>

        <TabsContent value="overview">
          <OverviewTab course={course} user={user} navigateTo={navigateTo} />
        </TabsContent>

        <TabsContent value="students">
          <StudentsTab
            course={course}
            user={user}
            navigateTo={navigateTo}
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            handleViewStudent={handleViewStudent}
          />
        </TabsContent>

        <TabsContent value="projects">
          <ProjectsTab
            course={course}
            user={user}
            navigateTo={navigateTo}
            handleViewAssignment={handleViewAssignment}
            handleViewSubmissions={handleViewSubmissions}
          />
        </TabsContent>

        {/* <TabsContent value="analytics">
          <AnalyticsTab />
        </TabsContent> */}
      </Tabs>
    </div>
  );
}
