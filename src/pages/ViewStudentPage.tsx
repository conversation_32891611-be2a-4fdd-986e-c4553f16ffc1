import React, { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import {
  ArrowLeft,
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  Award,
  BookOpen,
  FileText,
  TrendingUp,
  MessageSquare,
  Download,
  Edit,
  CheckCircle,
  Clock,
  AlertTriangle,
} from "lucide-react";
//import { useNavigation } from "../App";
import { Separator } from "../components/ui/separator";
import { Progress } from "../components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON><PERSON>ist,
  TabsTrigger,
} from "../components/ui/tabs";
import useNavigation from "../components/Context/NavigationContext";
import { useNavigate } from "react-router-dom";
interface Student {
  id: string;
  name: string;
  email: string;
  phone: string;
  studentId: string;
  program: string;
  year: string;
  semester: string;
  enrollmentDate: string;
  address: string;
  emergencyContact: {
    name: string;
    phone: string;
    relation: string;
  };
  academicInfo: {
    gpa: number;
    totalCredits: number;
    completedCredits: number;
    status: "active" | "inactive" | "graduated" | "dropped";
  };
  enrolledCourses: EnrolledCourse[];
  projectHistory: ProjectSubmission[];
  grades: GradeRecord[];
  attendance: AttendanceRecord[];
}

interface EnrolledCourse {
  id: string;
  name: string;
  code: string;
  instructor: string;
  credits: number;
  grade?: string;
  attendance: number;
  assignments: {
    completed: number;
    total: number;
  };
}

interface ProjectSubmission {
  id: string;
  title: string;
  course: string;
  submittedDate: string;
  grade: string;
  feedback: string;
  status: "submitted" | "graded" | "late";
}

interface GradeRecord {
  semester: string;
  courses: {
    name: string;
    code: string;
    credits: number;
    grade: string;
  }[];
  semesterGPA: number;
}

interface AttendanceRecord {
  course: string;
  totalClasses: number;
  attendedClasses: number;
  percentage: number;
}

// Mock student data
const mockStudentData: { [key: string]: Student } = {
  "1": {
    id: "1",
    name: "Rahul Sharma",
    email: "<EMAIL>",
    phone: "+91 9876543210",
    studentId: "2023001",
    program: "M.Tech Data Science",
    year: "2nd Year",
    semester: "Fall 2024",
    enrollmentDate: "2023-08-15",
    address: "Mumbai, Maharashtra, India",
    emergencyContact: {
      name: "Raj Sharma",
      phone: "+91 9876543211",
      relation: "Father",
    },
    academicInfo: {
      gpa: 3.7,
      totalCredits: 120,
      completedCredits: 72,
      status: "active",
    },
    enrolledCourses: [
      {
        id: "1",
        name: "Data Science 101",
        code: "DS101",
        instructor: "Dr. A. Sharma",
        credits: 4,
        grade: "A",
        attendance: 95,
        assignments: { completed: 2, total: 3 },
      },
      {
        id: "2",
        name: "Machine Learning Basics",
        code: "ML101",
        instructor: "Dr. B. Patel",
        credits: 4,
        attendance: 88,
        assignments: { completed: 3, total: 4 },
      },
      {
        id: "3",
        name: "Statistics for Data Science",
        code: "STAT201",
        instructor: "Dr. C. Singh",
        credits: 3,
        grade: "A-",
        attendance: 92,
        assignments: { completed: 4, total: 4 },
      },
    ],
    projectHistory: [
      {
        id: "1",
        title: "Linear Regression Analysis",
        course: "Data Science 101",
        submittedDate: "2024-12-10",
        grade: "A",
        feedback: "Excellent work! Great analysis and clear explanations.",
        status: "graded",
      },
      {
        id: "2",
        title: "Customer Segmentation",
        course: "Machine Learning Basics",
        submittedDate: "2024-11-25",
        grade: "A-",
        feedback: "Good implementation, could improve visualization.",
        status: "graded",
      },
      {
        id: "3",
        title: "Statistical Analysis Report",
        course: "Statistics for Data Science",
        submittedDate: "2024-11-15",
        grade: "A",
        feedback: "Thorough analysis with excellent insights.",
        status: "graded",
      },
    ],
    grades: [
      {
        semester: "Fall 2024",
        semesterGPA: 3.8,
        courses: [
          { name: "Data Science 101", code: "DS101", credits: 4, grade: "A" },
          {
            name: "Machine Learning Basics",
            code: "ML101",
            credits: 4,
            grade: "B+",
          },
          {
            name: "Statistics for Data Science",
            code: "STAT201",
            credits: 3,
            grade: "A-",
          },
        ],
      },
      {
        semester: "Spring 2024",
        semesterGPA: 3.6,
        courses: [
          {
            name: "Programming for Data Science",
            code: "CS101",
            credits: 4,
            grade: "A-",
          },
          { name: "Database Systems", code: "DB201", credits: 3, grade: "B+" },
          {
            name: "Mathematics for DS",
            code: "MATH201",
            credits: 3,
            grade: "A",
          },
        ],
      },
    ],
    attendance: [
      {
        course: "Data Science 101",
        totalClasses: 40,
        attendedClasses: 38,
        percentage: 95,
      },
      {
        course: "Machine Learning Basics",
        totalClasses: 35,
        attendedClasses: 31,
        percentage: 88,
      },
      {
        course: "Statistics for Data Science",
        totalClasses: 30,
        attendedClasses: 28,
        percentage: 92,
      },
    ],
  },
};

export default function ViewStudentPage({ studentId }: { studentId: string }) {
  const { goBack, navigateTo } = useNavigation();
  const [selectedTab, setSelectedTab] = useState("overview");
  const navigate = useNavigate()
  const student = mockStudentData[studentId];

  if (!student) {
    return (
      <div className="p-6">
        <div className="flex items-center space-x-4 mb-6">
          <Button variant="outline" onClick={goBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold mb-2">Student Not Found</h2>
          <p className="text-muted-foreground">
            The requested student could not be found.
          </p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-bits-blue/10 text-bits-blue";
      case "inactive":
        return "bg-bits-gold/10 text-bits-gold";
      case "graduated":
        return "bg-bits-gold/10 text-bits-gold";
      case "dropped":
        return "bg-bits-red/10 text-bits-red";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  const getGradeColor = (grade: string) => {
    if (grade.startsWith("A")) return "text-bits-gold";
    if (grade.startsWith("B")) return "text-bits-blue";
    if (grade.startsWith("C")) return "text-bits-gold";
    return "text-bits-red";
  };

  const getGradePoints = (grade: string): number => {
    const gradeMap: { [key: string]: number } = {
      A: 4.0,
      "A-": 3.7,
      "B+": 3.3,
      B: 3.0,
      "B-": 2.7,
      "C+": 2.3,
      C: 2.0,
      "C-": 1.7,
      D: 1.0,
      F: 0.0,
    };
    return gradeMap[grade] || 0;
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={goBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-bits-blue rounded-full flex items-center justify-center">
              <User className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-bits-blue">
                {student.name}
              </h1>
              <p className="text-muted-foreground">
                {student.studentId} • {student.program}
              </p>
            </div>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <MessageSquare className="h-4 w-4 mr-2" />
            Message
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Record
          </Button>
        </div>
      </div>

      <Tabs
        value={selectedTab}
        onValueChange={setSelectedTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="academic">Academic</TabsTrigger>
          <TabsTrigger value="courses">Courses</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Personal Information */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Email</p>
                          <p className="text-sm text-muted-foreground">
                            {student.email}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Phone</p>
                          <p className="text-sm text-muted-foreground">
                            {student.phone}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Address</p>
                          <p className="text-sm text-muted-foreground">
                            {student.address}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Enrollment Date</p>
                          <p className="text-sm text-muted-foreground">
                            {new Date(
                              student.enrollmentDate
                            ).toLocaleDateString()}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <BookOpen className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">
                            Current Semester
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {student.semester}
                          </p>
                        </div>
                      </div>

                      <div>
                        <p className="text-sm font-medium">Status</p>
                        <Badge
                          className={getStatusColor(
                            student.academicInfo.status
                          )}
                        >
                          {student.academicInfo.status}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Emergency Contact</h4>
                    <div className="text-sm">
                      <p>
                        {student.emergencyContact.name} (
                        {student.emergencyContact.relation})
                      </p>
                      <p className="text-muted-foreground">
                        {student.emergencyContact.phone}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Academic Summary */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Academic Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-bits-blue">
                      {student.academicInfo.gpa}
                    </div>
                    <p className="text-sm text-muted-foreground">Current GPA</p>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Credits Completed</span>
                      <span>
                        {student.academicInfo.completedCredits}/
                        {student.academicInfo.totalCredits}
                      </span>
                    </div>
                    <Progress
                      value={
                        (student.academicInfo.completedCredits /
                          student.academicInfo.totalCredits) *
                        100
                      }
                      className="h-2"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-lg font-semibold text-green-600">
                        {student.enrolledCourses.length}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Current Courses
                      </p>
                    </div>
                    <div>
                      <div className="text-lg font-semibold text-bits-gold">
                        {student.projectHistory.length}
                      </div>
                      <p className="text-xs text-muted-foreground">Projects</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button variant="outline" className="w-full justify-start">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Send Message
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Edit className="h-4 w-4 mr-2" />
                    Update Information
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <FileText className="h-4 w-4 mr-2" />
                    Generate Report
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="academic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Academic History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {student.grades.map((record, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-medium">{record.semester}</h4>
                      <Badge variant="outline">GPA: {record.semesterGPA}</Badge>
                    </div>

                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Course</TableHead>
                          <TableHead>Code</TableHead>
                          <TableHead>Credits</TableHead>
                          <TableHead>Grade</TableHead>
                          <TableHead>Points</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {record.courses.map((course, courseIndex) => (
                          <TableRow key={courseIndex}>
                            <TableCell>{course.name}</TableCell>
                            <TableCell>{course.code}</TableCell>
                            <TableCell>{course.credits}</TableCell>
                            <TableCell>
                              <span className={getGradeColor(course.grade)}>
                                {course.grade}
                              </span>
                            </TableCell>
                            <TableCell>
                              {getGradePoints(course.grade)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="courses" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>
                Current Courses ({student.enrolledCourses.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {student.enrolledCourses.map((course) => (
                  <div key={course.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-medium">{course.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {course.code} • {course.instructor} • {course.credits}{" "}
                          credits
                        </p>
                      </div>
                      {course.grade && (
                        <Badge className={getGradeColor(course.grade)}>
                          {course.grade}
                        </Badge>
                      )}
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm font-medium">Attendance</p>
                        <div className="flex items-center space-x-2">
                          <Progress
                            value={course.attendance}
                            className="flex-1 h-2"
                          />
                          <span className="text-sm">{course.attendance}%</span>
                        </div>
                      </div>

                      <div>
                        <p className="text-sm font-medium">Assignments</p>
                        <div className="flex items-center space-x-2">
                          <Progress
                            value={
                              (course.assignments.completed /
                                course.assignments.total) *
                              100
                            }
                            className="flex-1 h-2"
                          />
                          <span className="text-sm">
                            {course.assignments.completed}/
                            {course.assignments.total}
                          </span>
                        </div>
                      </div>

                      <div className="flex justify-end">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() =>
                            navigateTo("view-course", { courseId: course.id })
                          }
                        >
                          View Course
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="projects" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>
                Project History ({student.projectHistory.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {student.projectHistory.map((project) => (
                  <div key={project.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-medium">{project.title}</h4>
                        <p className="text-sm text-muted-foreground">
                          {project.course} • Submitted:{" "}
                          {new Date(project.submittedDate).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {project.status === "graded" && (
                          <Badge className={getGradeColor(project.grade)}>
                            {project.grade}
                          </Badge>
                        )}
                        <Badge
                          variant={
                            project.status === "late"
                              ? "destructive"
                              : "default"
                          }
                        >
                          {project.status}
                        </Badge>
                      </div>
                    </div>

                    {project.feedback && (
                      <div className="p-3 bg-green-50 rounded-lg">
                        <p className="text-sm text-green-800">
                          {project.feedback}
                        </p>
                      </div>
                    )}

                    <div className="flex justify-end mt-3">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          navigate(`/project-details/${project.id}`)
                          // navigateTo("project-details", {
                          //   projectId: project.id,
                          // })
                        }
                        }
                      >
                        View Details
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
