class TokenService {
  private token: string | null = null;
  private tokenExpiry: Date | null = null;
  private refreshTimer: ReturnType<typeof setTimeout> | null = null;
  private isRefreshing = false;
  private listeners: ((token: string | null) => void)[] = [];

  constructor() {
    // Initialize token from localStorage on creation
    this.token = localStorage.getItem("auth_token");
    const expiryStr = localStorage.getItem("auth_token_expiry");
    if (expiryStr) {
      this.tokenExpiry = new Date(expiryStr);
      // If expiry is in the future, schedule a refresh; otherwise clear the token
      if (this.token && new Date() < this.tokenExpiry) {
        this.scheduleRefresh(this.tokenExpiry);
      } else {
        this.clearToken();
      }
    } else if (this.token) {
      // Derive expiry from JWT if available
      const jwtExp = this.decodeJwtExpiry(this.token);
      if (jwtExp && jwtExp > new Date()) {
        this.tokenExpiry = jwtExp;
        localStorage.setItem("auth_token_expiry", jwtExp.toISOString());
        this.scheduleRefresh(jwtExp);
      } else {
        this.clearToken();
      }
    }
  }

  getToken(): string | null {
    // Check if token is expired
    if (this.token && this.tokenExpiry && new Date() >= this.tokenExpiry) {
      this.clearToken();
      return null;
    }
    return this.token;
  }

  setToken(newToken: string, expiresIn?: string | number): void {
    this.token = newToken;
    localStorage.setItem("auth_token", newToken);

    // Handle token expiry
    let expiryTime: Date | null = null;
    if (expiresIn !== undefined) {
      expiryTime = this.parseExpiryTime(expiresIn);
    } else {
      expiryTime = this.decodeJwtExpiry(newToken);
    }

    if (expiryTime) {
      this.tokenExpiry = expiryTime;
      localStorage.setItem("auth_token_expiry", expiryTime.toISOString());
      this.scheduleRefresh(expiryTime);
    }

    this.notifyListeners();
  }

  clearToken(): void {
    this.token = null;
    this.tokenExpiry = null;
    localStorage.removeItem("auth_token");
    localStorage.removeItem("auth_token_expiry");

    // Clear refresh timer
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }

    this.notifyListeners();
  }

  isTokenExpired(): boolean {
    if (!this.token || !this.tokenExpiry) return true;
    return new Date() >= this.tokenExpiry;
  }

  getTokenExpiry(): Date | null {
    return this.tokenExpiry;
  }

  private parseExpiryTime(expiresIn: string | number): Date {
    // Supports:
    // - numeric seconds (e.g., 900)
    // - numeric milliseconds since epoch (>= 1e12)
    // - relative strings like "15m", "1h", "30s", "1d"
    // - ISO date strings (absolute expiry time)
    // - ISO 8601 durations like "PT15M", "PT1H" (basic support)
    const nowMs = Date.now();

    // Numeric input
    if (typeof expiresIn === "number") {
      // treat small numbers as seconds, large as ms timestamp
      if (expiresIn < 1e12) {
        return new Date(nowMs + expiresIn * 1000);
      }
      return new Date(expiresIn);
    }

    const str = expiresIn.trim();

    // Plain digits string: assume seconds
    if (/^\d+$/.test(str)) {
      const secs = parseInt(str, 10);
      return new Date(nowMs + secs * 1000);
    }

    // Relative shorthand like 15m/1h/30s/1d
    const rel = str.match(/^(\d+)\s*([smhd])$/i);
    if (rel) {
      const value = parseInt(rel[1] ?? "0", 10);
      const unit = (rel[2] ?? "m").toLowerCase();
      const mult =
        unit === "s" ? 1 : unit === "m" ? 60 : unit === "h" ? 3600 : 86400;
      return new Date(nowMs + value * mult * 1000);
    }

    // ISO 8601 duration e.g. PT15M, PT1H, PT30S, P1D (basic parsing)
    const isoDur = str.match(
      /^P(T(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?)?(?:(\d+)D)?$/i
    );
    if (isoDur) {
      const hours = parseInt(isoDur[2] || "0", 10);
      const mins = parseInt(isoDur[3] || "0", 10);
      const secs = parseInt(isoDur[4] || "0", 10);
      const days = parseInt(isoDur[5] || "0", 10);
      const totalMs = ((days * 24 + hours) * 3600 + mins * 60 + secs) * 1000;
      if (totalMs > 0) return new Date(nowMs + totalMs);
    }

    // Absolute ISO datetime
    const parsed = Date.parse(str);
    if (!Number.isNaN(parsed)) {
      return new Date(parsed);
    }

    // Fallback: 15 minutes
    return new Date(nowMs + 15 * 60 * 1000);
  }

  private scheduleRefresh(expiryTime: Date): void {
    // Clear existing timer
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    // Prefer 1 minute before expiry; if lifetime is shorter, refresh 5s before expiry
    const now = Date.now();
    const msUntilExpiry = expiryTime.getTime() - now;
    const bufferMs = 5 * 60 * 1000; // 5 minutes
    const safetyMs = 60 * 1000; // 1 minute before expiry

    let delay: number;
    if (msUntilExpiry > bufferMs) {
      delay = msUntilExpiry - bufferMs;
    } else if (msUntilExpiry > safetyMs) {
      delay = msUntilExpiry - safetyMs;
    } else {
      // If already too close, trigger just before expiry (or immediately if negative)
      delay = Math.max(0, msUntilExpiry - 1000);
    }

    // Add a small jitter (0-2s) to avoid thundering herd if many tabs refresh together
    const jitter = Math.floor(Math.random() * 2000);
    delay = Math.max(0, delay - jitter);

    this.refreshTimer = setTimeout(() => {
      this.triggerTokenRefresh();
    }, delay);
  }

  private decodeJwtExpiry(token: string): Date | null {
    try {
      const parts = token.split(".");
      if (parts.length !== 3) return null;
      const mid = parts[1] ?? "";
      if (!mid) return null;
      const payload = JSON.parse(
        atob(mid.replace(/-/g, "+").replace(/_/g, "/"))
      );
      if (!payload || typeof payload.exp !== "number") return null;
      // exp is in seconds since epoch
      const expMs = payload.exp * 1000;
      if (Number.isFinite(expMs)) return new Date(expMs);
      return null;
    } catch {
      return null;
    }
  }

  private async triggerTokenRefresh(): Promise<void> {
    try {
      if (this.isRefreshing) return;
      this.isRefreshing = true;
      const { refreshToken } = await import("../api/authApi");
      const response = await refreshToken();

      if (response.success) {
        this.setToken(response.accessToken, response.expiresIn);
        console.log("Token refreshed successfully");
      } else {
        console.warn("Token refresh failed, clearing token");
        this.clearToken();
      }
    } catch (error) {
      console.error("Token refresh error:", error);
      this.clearToken();
    } finally {
      this.isRefreshing = false;
    }
  }

  subscribe(listener: (token: string | null) => void): () => void {
    this.listeners.push(listener);
    // Unsubscribe function
    return () => {
      this.listeners = this.listeners.filter((l) => l !== listener);
    };
  }

  private notifyListeners(): void {
    for (const listener of this.listeners) {
      listener(this.token);
    }
  }
}

export const tokenService = new TokenService();
