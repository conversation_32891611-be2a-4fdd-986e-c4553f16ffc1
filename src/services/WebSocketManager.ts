import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./jupyterAPI";
import { tokenService } from "./TokenService";

interface WebSocketMessage {
  header: {
    msg_id: string;
    msg_type: string;
    username: string;
    session: string;
    date: string;
    version: string;
  };
  parent_header: any;
  metadata: any;
  content: any;
}

interface ExecutionRequest {
  code: string;
  cellId: string;
  resolve: (result: any) => void;
  reject: (error: any) => void;
}

class WebSocketManager {
  private connections: Map<
    string,
    {
      ws: WebSocket | null;
      isConnecting: boolean;
      messageQueue: ExecutionRequest[];
      messageHandlers: Map<string, (result: any) => void>;
      executionCount: number;
    }
  > = new Map();

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async connectToKernel(kernelId: string): Promise<boolean> {
    const existing = this.connections.get(kernelId);

    if (existing?.ws?.readyState === WebSocket.OPEN) {
      console.log("WebSocket already connected for kernel:", kernelId);
      return true;
    }

    if (existing?.isConnecting) {
      console.log(
        "WebSocket connection already in progress for kernel:",
        kernelId
      );
      return new Promise((resolve) => {
        const checkConnection = () => {
          const conn = this.connections.get(kernelId);
          if (conn?.ws?.readyState === WebSocket.OPEN) {
            resolve(true);
          } else if (!conn?.isConnecting) {
            resolve(false);
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();
      });
    }

    console.log("Connecting to kernel:", kernelId);

    // Initialize connection state
    this.connections.set(kernelId, {
      ws: null,
      isConnecting: true,
      messageQueue: [],
      messageHandlers: new Map(),
      executionCount: 0,
    });

    try {
      const token = tokenService.getToken();
      if (!token) {
        throw new Error("No API token available");
      }
      const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
      const host = window.location.host;
      const wsUrl = `${protocol}//localhost:8888/api/kernels/${kernelId}/channels?token=${token}`;
      console.log("Connecting to WebSocket:", wsUrl);

      return new Promise((resolve, reject) => {
        const ws = new WebSocket(wsUrl);
        const connection = this.connections.get(kernelId)!;

        // Set connection timeout
        const connectionTimeout = setTimeout(() => {
          if (ws.readyState !== WebSocket.OPEN) {
            connection.isConnecting = false;
            this.connections.delete(kernelId);
            reject(new Error("WebSocket connection timeout"));
          }
        }, 5000);

        ws.onopen = () => {
          console.log("WebSocket connected for kernel:", kernelId);
          clearTimeout(connectionTimeout);
          connection.ws = ws;
          connection.isConnecting = false;

          // Process any queued messages
          this.processMessageQueue(kernelId);
          resolve(true);
        };

        ws.onmessage = (event) => {
          this.handleMessage(kernelId, event.data);
        };

        ws.onerror = (error) => {
          console.error("WebSocket error for kernel:", kernelId, error);
          connection.isConnecting = false;
          this.connections.delete(kernelId);
          reject(error);
        };

        ws.onclose = (event) => {
          console.log(
            "WebSocket closed for kernel:",
            kernelId,
            event.code,
            event.reason
          );
          connection.isConnecting = false;
          this.connections.delete(kernelId);
        };
      });
    } catch (error) {
      const connection = this.connections.get(kernelId);
      if (connection) {
        connection.isConnecting = false;
        this.connections.delete(kernelId);
      }
      throw error;
    }
  }

  private handleMessage(kernelId: string, data: string) {
    try {
      console.log(data);
      const message: WebSocketMessage = JSON.parse(data);
      console.log(
        "Received message for kernel:",
        kernelId,
        message.header.msg_type
      );

      const connection = this.connections.get(kernelId);
      if (!connection) return;

      const parentMsgId = message.parent_header.msg_id;
      if (parentMsgId && connection.messageHandlers.has(parentMsgId)) {
      const handler = connection.messageHandlers.get(parentMsgId)!;
        handler(message);
      //  connection.messageHandlers.delete(msgId);
      }
    } catch (error) {
      console.error("Error handling WebSocket message:", error);
    }
  }

  private processMessageQueue(kernelId: string) {
    const connection = this.connections.get(kernelId);
    if (
      !connection ||
      !connection.ws ||
      connection.ws.readyState !== WebSocket.OPEN
    ) {
      return;
    }

    while (connection.messageQueue.length > 0) {
      const request = connection.messageQueue.shift()!;
      this.sendExecuteRequest(kernelId, request);
    }
  }

  private sendExecuteRequest(
  kernelId: string,
  code: string,
  onMessage: (output: any) => void,
  onDone: (status: "ok" | "error") => void
) {
  const connection = this.connections.get(kernelId);
  if (!connection?.ws || connection.ws.readyState !== WebSocket.OPEN) {
    onMessage({
      output_type: "error",
      ename: "WebSocketError",
      evalue: "WebSocket is not open.",
      traceback: [],
    });
    onDone("error");
    return;
  }

  const msgId = this.generateMessageId();
  let isIdle = false;
  let hasReceivedReply = false;

  const timeoutId = setTimeout(() => {
    if (connection.messageHandlers.has(msgId)) {
      connection.messageHandlers.delete(msgId);
      onMessage({
        output_type: "error",
        ename: "TimeoutError",
        evalue: "Execution timed out after 30 seconds.",
        traceback: [],
      });
      onDone("error");
    }
  }, 30000); // 30-second timeout

  const checkCompletion = () => {
    if (isIdle && hasReceivedReply) {
      clearTimeout(timeoutId);
      connection.messageHandlers.delete(msgId);
      onDone("ok");
    }
  };

  connection.messageHandlers.set(msgId, (message: WebSocketMessage) => {
    const msgType = message.header.msg_type;

    switch (msgType) {
      case "execute_reply":
        hasReceivedReply = true;
        if (message.content?.status === "error") {
          onMessage({
            output_type: "error",
            ename: message.content.ename,
            evalue: message.content.evalue,
            traceback: message.content.traceback,
          });
        }
        checkCompletion();
        break;

      case "stream":
        onMessage({
          output_type: "stream",
          name: message.content.name || "stdout",
          text: message.content.text || "",
        });
        break;

      case "execute_result":
        onMessage({
          output_type: "execute_result",
          data: message.content.data || {},
          metadata: message.content.metadata || {},
          execution_count: message.content.execution_count,
        });
        break;

      case "display_data":
        onMessage({
          output_type: "display_data",
          data: message.content.data || {},
          metadata: message.content.metadata || {},
        });
        break;

      case "error":
        onMessage({
          output_type: "error",
          ename: message.content.ename,
          evalue: message.content.evalue,
          traceback: message.content.traceback,
        });
        break;

      case "status":
        if (message.content?.execution_state === "idle") {
          isIdle = true;
          checkCompletion();
        }
        break;
    }
  });

  const executeRequest: WebSocketMessage = {
      header: {
        msg_id: msgId,
        msg_type: "execute_request",
        username: "user",
        session: kernelId,
        date: new Date().toISOString(),
        version: "5.3",
      },
      parent_header: {},
      metadata: {},
      content: {
        code: code,
        silent: false,
        store_history: true,
        user_expressions: {},
        allow_stdin: false,
        stop_on_error: true,
      },
    };
    
  connection.ws.send(JSON.stringify(executeRequest));
}

  executeCode(
  kernelId: string,
  code: string,
  onMessage: (output: any) => void,
  onDone: (status: "ok" | "error") => void
): void {
  this.connectToKernel(kernelId).then(isConnected => {
    if (!isConnected) {
      onMessage({
        output_type: "error",
        ename: "ConnectionError",
        evalue: "Failed to connect to the kernel WebSocket.",
        traceback: [],
      });
      onDone("error");
      return;
    }

    this.sendExecuteRequest(kernelId, code, onMessage, onDone);
  }).catch(error => {
    onMessage({
      output_type: "error",
      ename: "ConnectionError",
      evalue: error.message || "An unknown error occurred while connecting to the kernel.",
      traceback: [],
    });
    onDone("error");
  });
}

  disconnectFromKernel(kernelId: string) {
    const connection = this.connections.get(kernelId);
    if (connection?.ws) {
      connection.ws.close();
    }
    this.connections.delete(kernelId);
    console.log("Disconnected from kernel:", kernelId);
  }

  isConnected(kernelId: string): boolean {
    const connection = this.connections.get(kernelId);
    return connection?.ws?.readyState === WebSocket.OPEN;
  }

  getConnectionStatus(
    kernelId: string
  ): "connected" | "connecting" | "disconnected" {
    const connection = this.connections.get(kernelId);
    if (!connection) return "disconnected";
    if (connection.isConnecting) return "connecting";
    if (connection.ws?.readyState === WebSocket.OPEN) return "connected";
    return "disconnected";
  }
}

// Export singleton instance
export const webSocketManager = new WebSocketManager();
