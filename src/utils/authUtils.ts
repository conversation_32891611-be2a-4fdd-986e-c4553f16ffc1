import {
  BackendRole,
  AuthMeRole,
  Permission,
  RefreshTokenRole,
} from "../api/authApi";

/**
 * Extracts permission keys from user roles (handles both complex and simple role structures)
 * @param roles - Array of user roles with permissions
 * @returns Array of permission keys (strings)
 *
 * @example
 * const permissions = extractPermissionKeys(user.roles);
 * // Returns: ["create_projects", "view_projects", "edit_projects", ...]
 */
export function extractPermissionKeys(
  roles: BackendRole[] | AuthMeRole[] | RefreshTokenRole[]
): string[] {
  const permissionKeys: string[] = [];

  roles.forEach((role) => {
    if (Array.isArray(role.permissions) && role.permissions.length > 0) {
      // Handle RefreshTokenRole (simple string array)
      if (typeof role.permissions[0] === "string") {
        permissionKeys.push(...(role.permissions as string[]));
      } else {
        // Handle BackendRole/AuthMeRole (complex Permission objects)
        (role.permissions as Permission[]).forEach((permission) => {
          permissionKeys.push(permission.key);
        });
      }
    }
  });

  return [...new Set(permissionKeys)]; // Remove duplicates
}

/**
 * Checks if user has a specific permission (handles both complex and simple role structures)
 * @param roles - Array of user roles with permissions
 * @param permissionKey - Permission key to check for
 * @returns Boolean indicating if user has the permission
 *
 * @example
 * const canCreateProjects = hasPermission(user.roles, "create_projects");
 * const canViewSubmissions = hasPermission(user.roles, "view_submissions");
 */
export function hasPermission(
  roles: BackendRole[] | AuthMeRole[] | RefreshTokenRole[],
  permissionKey: string
): boolean {
  return roles.some((role) => {
    if (Array.isArray(role.permissions) && role.permissions.length > 0) {
      // Handle RefreshTokenRole (simple string array)
      if (typeof role.permissions[0] === "string") {
        return (role.permissions as string[]).includes(permissionKey);
      } else {
        // Handle BackendRole/AuthMeRole (complex Permission objects)
        return (role.permissions as Permission[]).some(
          (permission) => permission.key === permissionKey
        );
      }
    }
    return false;
  });
}

/**
 * Checks if user has any of the specified permissions (handles both complex and simple role structures)
 * @param roles - Array of user roles with permissions
 * @param permissionKeys - Array of permission keys to check for
 * @returns Boolean indicating if user has any of the permissions
 *
 * @example
 * const canManageProjects = hasAnyPermission(user.roles, [
 *   "create_projects",
 *   "edit_projects",
 *   "delete_projects"
 * ]);
 */
export function hasAnyPermission(
  roles: BackendRole[] | AuthMeRole[] | RefreshTokenRole[],
  permissionKeys: string[]
): boolean {
  return permissionKeys.some((key) => hasPermission(roles, key));
}

/**
 * Checks if user has all of the specified permissions (handles both complex and simple role structures)
 * @param roles - Array of user roles with permissions
 * @param permissionKeys - Array of permission keys to check for
 * @returns Boolean indicating if user has all of the permissions
 *
 * @example
 * const canFullyManageProjects = hasAllPermissions(user.roles, [
 *   "create_projects",
 *   "edit_projects",
 *   "delete_projects",
 *   "publish_projects"
 * ]);
 */
export function hasAllPermissions(
  roles: BackendRole[] | AuthMeRole[] | RefreshTokenRole[],
  permissionKeys: string[]
): boolean {
  return permissionKeys.every((key) => hasPermission(roles, key));
}

/**
 * Gets all permissions for a user grouped by category (only works with complex role structures)
 * @param roles - Array of user roles with permissions
 * @returns Object with permissions grouped by category
 *
 * @example
 * const permissionsByCategory = getPermissionsByCategory(user.roles);
 * // Returns: {
 * //   "projects": [Permission[],
 * //   "courses": Permission[],
 * //   "enrollment": Permission[]
 * // }
 */
export function getPermissionsByCategory(
  roles: BackendRole[] | AuthMeRole[]
): Record<string, Permission[]> {
  const permissionsByCategory: Record<string, Permission[]> = {};

  roles.forEach((role) => {
    role.permissions.forEach((permission) => {
      if (!permissionsByCategory[permission.category]) {
        permissionsByCategory[permission.category] = [];
      }

      const categoryPermissions = permissionsByCategory[permission.category];
      if (categoryPermissions) {
        // Avoid duplicates
        const exists = categoryPermissions.some(
          (p) => p.key === permission.key
        );

        if (!exists) {
          categoryPermissions.push(permission);
        }
      }
    });
  });

  return permissionsByCategory;
}

/**
 * Gets the primary role name (first role with highest priority or first role)
 * @param roles - Array of user roles
 * @returns Primary role name
 *
 * @example
 * const primaryRole = getPrimaryRoleName(user.roles);
 * // Returns: "admin", "instructor", or "student"
 */
export function getPrimaryRoleName(
  roles: BackendRole[] | AuthMeRole[] | RefreshTokenRole[]
): string {
  if (!roles || roles.length === 0) return "student";

  // For complex roles, sort by priority (highest first)
  if (roles.length > 0 && roles[0] && "priority" in roles[0]) {
    const sortedRoles = [...(roles as (BackendRole | AuthMeRole)[])].sort(
      (a, b) => (b.priority || 0) - (a.priority || 0)
    );
    return sortedRoles[0]?.name.toLowerCase() || "student";
  }

  // For simple roles, just return the first one
  return roles[0]?.name.toLowerCase() || "student";
}

// Common permission constants for easy reference
export const PERMISSIONS = {
  // Project permissions
  CREATE_PROJECTS: "create_projects",
  VIEW_PROJECTS: "view_projects",
  EDIT_PROJECTS: "edit_projects",
  DELETE_PROJECTS: "delete_projects",
  PUBLISH_PROJECTS: "publish_projects",

  // Course permissions
  VIEW_COURSES: "view_courses",
  MANAGE_ENROLLMENTS: "manage_enrollments",

  // API scope permissions
  PROJECT_READ: "project:read",
  PROJECT_CREATE: "project:create",
  PROJECT_UPDATE: "project:update",

  // Assignment permissions
  SUBMIT_ASSIGNMENTS: "submit_assignments",
  VIEW_SUBMISSIONS: "view_submissions",
} as const;
