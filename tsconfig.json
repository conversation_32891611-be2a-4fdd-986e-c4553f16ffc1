{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "allowJs": false,
    "types": ["node"],

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true,

    /* Path mapping */
    "baseUrl": ".",
    // "paths": {
    //   "@/*": ["./*"],
    //   "@/components/*": ["./components/*"],
    //   "@/styles/*": ["./styles/*"],
    //   "@/assets/*": ["./assets/*"]
    // },

    "paths": {
      "@/*": ["./src/*"]
    },

    /* Additional strict checks */
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "alwaysStrict": true,

    /* Module resolution */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,

    /* Emit */
    "declaration": false,
    "declarationMap": false,
    "sourceMap": true,
    "removeComments": true,
    "importHelpers": true,
    "downlevelIteration": true
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "**/*.js",
    "**/*.jsx",
    "styles/**/*",
    "src"
  ],
  "exclude": ["node_modules", "dist", "build", "*.config.js", "*.config.ts"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
